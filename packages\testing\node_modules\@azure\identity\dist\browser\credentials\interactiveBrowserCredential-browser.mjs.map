{"version": 3, "file": "interactiveBrowserCredential-browser.mjs", "sourceRoot": "", "sources": ["../../../src/credentials/interactiveBrowserCredential-browser.mts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAOlC,OAAO,EAAE,gBAAgB,EAAE,WAAW,EAAE,MAAM,oBAAoB,CAAC;AACnE,OAAO,EACL,yBAAyB,EACzB,mCAAmC,GACpC,MAAM,0BAA0B,CAAC;AAIlC,OAAO,EAAE,YAAY,EAAE,MAAM,uBAAuB,CAAC;AACrD,OAAO,EAAE,aAAa,EAAE,MAAM,oBAAoB,CAAC;AAEnD,OAAO,EAAE,uBAAuB,EAAE,MAAM,2CAA2C,CAAC;AAEpF,MAAM,MAAM,GAAG,gBAAgB,CAAC,8BAA8B,CAAC,CAAC;AAEhE;;;GAGG;AACH,MAAM,OAAO,4BAA4B;IAMvC;;;;;;;;;;;;;OAaG;IACH,YACE,OAA+F;QAE/F,IAAI,CAAC,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAA,EAAE,CAAC;YACvB,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,0FAA0F,CAC3F,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;YACpC,MAAM,KAAK,CAAC;QACd,CAAC;QAED,IAAI,CAAC,QAAQ,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,QAAQ,CAAC;QAClC,IAAI,CAAC,4BAA4B,GAAG,mCAAmC,CACrE,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,0BAA0B,CACpC,CAAC;QAEF,MAAM,cAAc,GAAG,OAAuD,CAAC;QAC/E,MAAM,UAAU,GAAG,cAAc,CAAC,UAAU,IAAI,OAAO,CAAC;QACxD,MAAM,WAAW,GAAG,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;QAE1C,IAAI,WAAW,CAAC,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,EAAE,CAAC;YAC3C,MAAM,KAAK,GAAG,IAAI,KAAK,CACrB,uBACE,cAAc,CAAC,UACjB,qCAAqC,WAAW,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,CAC/D,CAAC;YACF,MAAM,CAAC,IAAI,CAAC,WAAW,CAAC,EAAE,EAAE,KAAK,CAAC,CAAC,CAAC;YACpC,MAAM,KAAK,CAAC;QACd,CAAC;QAED,MAAM,WAAW,mCACZ,OAAO,KACV,sBAAsB,EAAE,OAAO,EAC/B,MAAM,EACN,UAAU,EAAE,UAAU,EACtB,WAAW,EACT,OAAO,OAAO,CAAC,WAAW,KAAK,UAAU,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,GAC1F,CAAC;QAEF,IAAI,CAAC,UAAU,GAAG,uBAAuB,CAAC,WAAW,CAAC,CAAC;QACvD,IAAI,CAAC,8BAA8B,GAAG,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,8BAA8B,CAAC;IAChF,CAAC;IAED;;;;;;;;;;;OAWG;IACH,KAAK,CAAC,QAAQ,CAAC,MAAyB,EAAE,UAA2B,EAAE;QACrE,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,WAAW,EACnC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,MAAM,QAAQ,GAAG,yBAAyB,CACxC,IAAI,CAAC,QAAQ,EACb,UAAU,EACV,IAAI,CAAC,4BAA4B,CAClC,CAAC;YACF,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;YAE/B,MAAM,WAAW,GAAG,YAAY,CAAC,MAAM,CAAC,CAAC;YACzC,OAAO,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,kCACtC,UAAU,KACb,8BAA8B,EAAE,IAAI,CAAC,8BAA8B,IACnE,CAAC;QACL,CAAC,CACF,CAAC;IACJ,CAAC;IAED;;;;;;;;;OASG;IACH,KAAK,CAAC,YAAY,CAChB,MAAyB,EACzB,UAA2B,EAAE;QAE7B,OAAO,aAAa,CAAC,QAAQ,CAC3B,GAAG,IAAI,CAAC,WAAW,CAAC,IAAI,eAAe,EACvC,OAAO,EACP,KAAK,EAAE,UAAU,EAAE,EAAE;YACnB,MAAM,WAAW,GAAG,KAAK,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC;YAC9D,MAAM,IAAI,CAAC,UAAU,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;YACxD,OAAO,IAAI,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;QAC5C,CAAC,CACF,CAAC;IACJ,CAAC;CACF", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { AccessToken, GetTokenOptions, TokenCredential } from \"@azure/core-auth\";\nimport type {\n  InteractiveBrowserCredentialInBrowserOptions,\n  InteractiveBrowserCredentialNodeOptions,\n} from \"./interactiveBrowserCredentialOptions.js\";\nimport { credentialLogger, formatError } from \"../util/logging.js\";\nimport {\n  processMultiTenantRequest,\n  resolveAdditionallyAllowedTenantIds,\n} from \"../util/tenantIdUtils.js\";\n\nimport type { AuthenticationRecord } from \"../msal/types.js\";\nimport type { MsalBrowserFlowOptions } from \"../msal/browserFlows/msalBrowserOptions.js\";\nimport { ensureScopes } from \"../util/scopeUtils.js\";\nimport { tracingClient } from \"../util/tracing.js\";\nimport type { MsalBrowserClient } from \"../msal/browserFlows/msalBrowserCommon.js\";\nimport { createMsalBrowserClient } from \"../msal/browserFlows/msalBrowserCommon.js\";\n\nconst logger = credentialLogger(\"InteractiveBrowserCredential\");\n\n/**\n * Enables authentication to Microsoft Entra ID inside of the web browser\n * using the interactive login flow.\n */\nexport class InteractiveBrowserCredential implements TokenCredential {\n  private tenantId?: string;\n  private additionallyAllowedTenantIds: string[];\n  private msalClient: MsalBrowserClient;\n  private disableAutomaticAuthentication?: boolean;\n\n  /**\n   * Creates an instance of the InteractiveBrowserCredential with the\n   * details needed to authenticate against Microsoft Entra ID with\n   * a user identity.\n   *\n   * This credential uses the [Authorization Code Flow](https://learn.microsoft.com/en-us/azure/active-directory/develop/v2-oauth2-auth-code-flow).\n   * On Node.js, it will open a browser window while it listens for a redirect response from the authentication service.\n   * On browsers, it authenticates via popups. The `loginStyle` optional parameter can be set to `redirect` to authenticate by redirecting the user to an Azure secure login page, which then will redirect the user back to the web application where the authentication started.\n   *\n   * It's recommended that the Microsoft Entra Applications used are configured to authenticate using Single Page Applications.\n   * More information here: [link](https://learn.microsoft.com/en-us/azure/active-directory/develop/scenario-spa-app-registration#redirect-uri-msaljs-20-with-auth-code-flow).\n   *\n   * @param options - Options for configuring the client which makes the authentication request.\n   */\n  constructor(\n    options: InteractiveBrowserCredentialInBrowserOptions | InteractiveBrowserCredentialNodeOptions,\n  ) {\n    if (!options?.clientId) {\n      const error = new Error(\n        \"The parameter `clientId` cannot be left undefined for the `InteractiveBrowserCredential`\",\n      );\n      logger.info(formatError(\"\", error));\n      throw error;\n    }\n\n    this.tenantId = options?.tenantId;\n    this.additionallyAllowedTenantIds = resolveAdditionallyAllowedTenantIds(\n      options?.additionallyAllowedTenants,\n    );\n\n    const browserOptions = options as InteractiveBrowserCredentialInBrowserOptions;\n    const loginStyle = browserOptions.loginStyle || \"popup\";\n    const loginStyles = [\"redirect\", \"popup\"];\n\n    if (loginStyles.indexOf(loginStyle) === -1) {\n      const error = new Error(\n        `Invalid loginStyle: ${\n          browserOptions.loginStyle\n        }. Should be any of the following: ${loginStyles.join(\", \")}.`,\n      );\n      logger.info(formatError(\"\", error));\n      throw error;\n    }\n\n    const msalOptions: MsalBrowserFlowOptions = {\n      ...options,\n      tokenCredentialOptions: options,\n      logger,\n      loginStyle: loginStyle,\n      redirectUri:\n        typeof options.redirectUri === \"function\" ? options.redirectUri() : options.redirectUri,\n    };\n\n    this.msalClient = createMsalBrowserClient(msalOptions);\n    this.disableAutomaticAuthentication = options?.disableAutomaticAuthentication;\n  }\n\n  /**\n   * Authenticates with Microsoft Entra ID and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the user provided the option `disableAutomaticAuthentication`,\n   * once the token can't be retrieved silently,\n   * this method won't attempt to request user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                TokenCredential implementation might make.\n   */\n  async getToken(scopes: string | string[], options: GetTokenOptions = {}): Promise<AccessToken> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.getToken`,\n      options,\n      async (newOptions) => {\n        const tenantId = processMultiTenantRequest(\n          this.tenantId,\n          newOptions,\n          this.additionallyAllowedTenantIds,\n        );\n        newOptions.tenantId = tenantId;\n\n        const arrayScopes = ensureScopes(scopes);\n        return this.msalClient.getToken(arrayScopes, {\n          ...newOptions,\n          disableAutomaticAuthentication: this.disableAutomaticAuthentication,\n        });\n      },\n    );\n  }\n\n  /**\n   * Authenticates with Microsoft Entra ID and returns an access token if successful.\n   * If authentication fails, a {@link CredentialUnavailableError} will be thrown with the details of the failure.\n   *\n   * If the token can't be retrieved silently, this method will require user interaction to retrieve the token.\n   *\n   * @param scopes - The list of scopes for which the token will have access.\n   * @param options - The options used to configure any requests this\n   *                  TokenCredential implementation might make.\n   */\n  async authenticate(\n    scopes: string | string[],\n    options: GetTokenOptions = {},\n  ): Promise<AuthenticationRecord | undefined> {\n    return tracingClient.withSpan(\n      `${this.constructor.name}.authenticate`,\n      options,\n      async (newOptions) => {\n        const arrayScopes = Array.isArray(scopes) ? scopes : [scopes];\n        await this.msalClient.getToken(arrayScopes, newOptions);\n        return this.msalClient.getActiveAccount();\n      },\n    );\n  }\n}\n"]}