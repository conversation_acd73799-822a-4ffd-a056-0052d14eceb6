export const configuration = () => ({
  // Application
  nodeEnv: process.env.NODE_ENV || 'development',
  apiPort: parseInt(process.env.API_PORT || '3000', 10),
  apiHost: process.env.API_HOST || '0.0.0.0',

  // Database
  database: {
    url: process.env.DATABASE_URL || 'postgresql://ironrelay:ironrelay@localhost:5432/ironrelay',
  },

  // Redis
  redis: {
    url: process.env.REDIS_URL || 'redis://localhost:6379',
  },

  // JWT
  jwt: {
    secret: process.env.JWT_SECRET || 'your-super-secret-jwt-key',
    expiresIn: process.env.JWT_EXPIRES_IN || '1h',
    refreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d',
  },

  // Security
  security: {
    bcryptRounds: parseInt(process.env.BCRYPT_ROUNDS || '12', 10),
    sessionSecret: process.env.SESSION_SECRET || 'your-session-secret',
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10),
  },

  // SMTP Engine
  smtp: {
    port: parseInt(process.env.SMTP_PORT || '2525', 10),
    host: process.env.SMTP_HOST || '0.0.0.0',
  },

  // Frontend
  frontend: {
    port: parseInt(process.env.FRONTEND_PORT || '3001', 10),
    url: process.env.FRONTEND_URL || 'http://localhost:3001',
  },

  // Logging
  logging: {
    level: process.env.LOG_LEVEL || 'info',
    dir: process.env.LOG_DIR || './logs',
  },

  // TLS
  tls: {
    certPath: process.env.TLS_CERT_PATH || './certs',
    keyPath: process.env.TLS_KEY_PATH || './certs',
  },

  // Monitoring
  monitoring: {
    prometheusPort: parseInt(process.env.PROMETHEUS_PORT || '9090', 10),
    grafanaPort: parseInt(process.env.GRAFANA_PORT || '3000', 10),
  },

  // Email notifications
  notifications: {
    smtp: {
      host: process.env.SMTP_NOTIFICATION_HOST || 'smtp.gmail.com',
      port: parseInt(process.env.SMTP_NOTIFICATION_PORT || '587', 10),
      secure: process.env.SMTP_NOTIFICATION_SECURE === 'true',
      user: process.env.SMTP_NOTIFICATION_USER || '',
      pass: process.env.SMTP_NOTIFICATION_PASS || '',
      from: process.env.SMTP_NOTIFICATION_FROM || 'IronRelay <<EMAIL>>',
    },
  },

  // License
  license: {
    key: process.env.LICENSE_KEY || '',
    serverUrl: process.env.LICENSE_SERVER_URL || 'https://license.ironrelay.com',
  },

  // Backup
  backup: {
    enabled: process.env.BACKUP_ENABLED === 'true',
    schedule: process.env.BACKUP_SCHEDULE || '0 2 * * *',
    retentionDays: parseInt(process.env.BACKUP_RETENTION_DAYS || '7', 10),
    storageType: process.env.BACKUP_STORAGE_TYPE || 'local',
    localPath: process.env.BACKUP_LOCAL_PATH || './backups',
    s3: {
      accessKeyId: process.env.AWS_ACCESS_KEY_ID || '',
      secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY || '',
      region: process.env.AWS_REGION || 'us-east-1',
      bucket: process.env.AWS_S3_BUCKET || '',
    },
  },

  // Development
  development: {
    enableSwagger: process.env.ENABLE_SWAGGER === 'true',
    enableCors: process.env.ENABLE_CORS === 'true',
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:3001',
  },

  // Health checks
  health: {
    timeout: parseInt(process.env.HEALTH_CHECK_TIMEOUT || '5000', 10),
    interval: parseInt(process.env.HEALTH_CHECK_INTERVAL || '30000', 10),
  },

  // Queue
  queue: {
    redisUrl: process.env.QUEUE_REDIS_URL || 'redis://localhost:6379/1',
    concurrency: parseInt(process.env.QUEUE_CONCURRENCY || '10', 10),
    maxAttempts: parseInt(process.env.QUEUE_MAX_ATTEMPTS || '3', 10),
    delay: parseInt(process.env.QUEUE_DELAY || '5000', 10),
  },

  // Metrics
  metrics: {
    enabled: process.env.METRICS_ENABLED !== 'false',
    interval: parseInt(process.env.METRICS_INTERVAL || '60000', 10),
    retentionDays: parseInt(process.env.METRICS_RETENTION_DAYS || '30', 10),
  },
});
