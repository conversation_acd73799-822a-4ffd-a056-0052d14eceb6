import { TestHelpers } from '../../test-utils/TestHelpers';
import { TestDataFactory } from '../../test-utils/TestDataFactory';
import { ChildProcess } from 'child_process';

describe('Full System E2E Tests', () => {
  let apiProcess: ChildProcess;
  let smtpProcess: ChildProcess;
  let frontendProcess: ChildProcess;
  let apiClient: any;
  let testDatabase: any;
  let testRedis: any;
  let smtpServer: any;

  const API_PORT = 3000;
  const SMTP_PORT = 2525;
  const FRONTEND_PORT = 3001;

  beforeAll(async () => {
    // Set up test environment
    process.env.NODE_ENV = 'test';
    process.env.DATABASE_URL = 'postgresql://test:test@localhost:5432/ironrelay_e2e_test';
    process.env.REDIS_URL = 'redis://localhost:6379/2';
    process.env.SMTP_PORT = SMTP_PORT.toString();
    process.env.API_PORT = API_PORT.toString();
    process.env.FRONTEND_PORT = FRONTEND_PORT.toString();

    // Create test database and Redis
    testDatabase = await TestHelpers.createTestDatabase('e2e_test');
    testRedis = await TestHelpers.createTestRedis(2);

    // Start SMTP engine
    smtpProcess = await TestHelpers.startProcess('npm', ['run', 'start'], {
      cwd: '../smtp-engine',
      env: { ...process.env },
    });

    // Start API server
    apiProcess = await TestHelpers.startProcess('npm', ['run', 'start'], {
      cwd: '../api',
      env: { ...process.env },
    });

    // Start frontend (optional for API-only tests)
    frontendProcess = await TestHelpers.startProcess('npm', ['run', 'dev'], {
      cwd: '../frontend',
      env: { ...process.env },
    });

    // Wait for services to be ready
    await TestHelpers.waitForAPI(`http://localhost:${API_PORT}`, 60000);
    
    // Create API client
    apiClient = TestHelpers.createAPIClient(`http://localhost:${API_PORT}`);

    // Set up test SMTP server for receiving emails
    const smtpSetup = await TestHelpers.createTestSMTPServer(SMTP_PORT + 1);
    smtpServer = smtpSetup.server;

    console.log('E2E test environment ready');
  }, 120000); // 2 minutes timeout for setup

  afterAll(async () => {
    // Cleanup processes
    if (frontendProcess) await TestHelpers.stopProcess(frontendProcess);
    if (apiProcess) await TestHelpers.stopProcess(apiProcess);
    if (smtpProcess) await TestHelpers.stopProcess(smtpProcess);
    
    // Cleanup test infrastructure
    if (smtpServer) await TestHelpers.stopSMTPServer(smtpServer);
    if (testDatabase) await TestHelpers.cleanupDatabase(testDatabase);
    if (testRedis) await TestHelpers.cleanupRedis(testRedis);
  }, 60000);

  describe('Authentication Flow', () => {
    let authToken: string;

    it('should register a new user', async () => {
      const userData = TestDataFactory.createUser({
        email: '<EMAIL>',
        role: 'ADMIN',
      });

      const response = await apiClient.post('/api/auth/register', {
        email: userData.email,
        password: 'TestPassword123!',
        firstName: userData.firstName,
        lastName: userData.lastName,
      });

      expect(response.status).toBe(201);
      expect(response.data.user.email).toBe(userData.email);
      expect(response.data.accessToken).toBeDefined();
    });

    it('should login with valid credentials', async () => {
      const response = await apiClient.post('/api/auth/login', {
        email: '<EMAIL>',
        password: 'TestPassword123!',
      });

      expect(response.status).toBe(200);
      expect(response.data.accessToken).toBeDefined();
      expect(response.data.refreshToken).toBeDefined();
      expect(response.data.user.email).toBe('<EMAIL>');

      authToken = response.data.accessToken;
    });

    it('should access protected routes with valid token', async () => {
      const response = await apiClient.get('/api/auth/profile', {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(200);
      expect(response.data.user.email).toBe('<EMAIL>');
    });

    it('should reject access with invalid token', async () => {
      const response = await apiClient.get('/api/auth/profile', {
        headers: { Authorization: 'Bearer invalid-token' },
      });

      expect(response.status).toBe(401);
    });
  });

  describe('SMTP Configuration Management', () => {
    let smtpConfigId: string;

    it('should create SMTP configuration', async () => {
      const smtpConfig = TestDataFactory.createSMTPConfig({
        name: 'E2E Test SMTP Config',
        hostname: 'localhost',
        port: SMTP_PORT,
      });

      const response = await apiClient.post('/api/smtp-config', smtpConfig, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(201);
      expect(response.data.name).toBe(smtpConfig.name);
      expect(response.data.hostname).toBe(smtpConfig.hostname);
      expect(response.data.port).toBe(smtpConfig.port);

      smtpConfigId = response.data.id;
    });

    it('should retrieve SMTP configuration', async () => {
      const response = await apiClient.get(`/api/smtp-config/${smtpConfigId}`, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(200);
      expect(response.data.id).toBe(smtpConfigId);
      expect(response.data.name).toBe('E2E Test SMTP Config');
    });

    it('should update SMTP configuration', async () => {
      const updateData = {
        name: 'Updated E2E Test SMTP Config',
        maxConnections: 500,
      };

      const response = await apiClient.put(`/api/smtp-config/${smtpConfigId}`, updateData, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(200);
      expect(response.data.name).toBe(updateData.name);
      expect(response.data.maxConnections).toBe(updateData.maxConnections);
    });

    it('should activate SMTP configuration', async () => {
      const response = await apiClient.post(`/api/smtp-config/${smtpConfigId}/activate`, {}, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(200);
      expect(response.data.enabled).toBe(true);
    });

    it('should test SMTP configuration', async () => {
      const response = await apiClient.post(`/api/smtp-config/${smtpConfigId}/test`, {}, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(200);
      expect(response.data.success).toBe(true);
    });
  });

  describe('Email Transaction Flow', () => {
    let smtpUserId: string;

    it('should create SMTP user', async () => {
      const smtpUser = TestDataFactory.createSMTPUser({
        username: 'e2e-test-user',
        email: '<EMAIL>',
      });

      const response = await apiClient.post('/api/smtp-users', smtpUser, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(201);
      expect(response.data.username).toBe(smtpUser.username);
      expect(response.data.email).toBe(smtpUser.email);

      smtpUserId = response.data.id;
    });

    it('should send email through SMTP relay', async () => {
      const emailData = {
        to: '<EMAIL>',
        from: '<EMAIL>',
        subject: 'E2E Test Email',
        text: 'This is an end-to-end test email',
      };

      // Send email through the SMTP relay
      await TestHelpers.sendTestEmail({
        ...emailData,
        smtpConfig: {
          host: 'localhost',
          port: SMTP_PORT,
          auth: {
            user: 'e2e-test-user',
            pass: 'password',
          },
        },
      });

      // Wait for transaction to be recorded
      await TestHelpers.sleep(2000);

      // Check if transaction was recorded
      const response = await apiClient.get('/api/email-transactions', {
        headers: { Authorization: `Bearer ${authToken}` },
        params: { limit: 10 },
      });

      expect(response.status).toBe(200);
      expect(response.data.data.length).toBeGreaterThan(0);

      const transaction = response.data.data.find((t: any) => 
        t.subject === emailData.subject
      );
      expect(transaction).toBeDefined();
      expect(transaction.from).toBe(emailData.from);
      expect(transaction.to).toContain(emailData.to);
    });

    it('should track email delivery status', async () => {
      // Get recent transactions
      const response = await apiClient.get('/api/email-transactions', {
        headers: { Authorization: `Bearer ${authToken}` },
        params: { limit: 1 },
      });

      expect(response.status).toBe(200);
      expect(response.data.data.length).toBeGreaterThan(0);

      const transaction = response.data.data[0];
      expect(['RECEIVED', 'QUEUED', 'PROCESSING', 'DELIVERED', 'FAILED']).toContain(transaction.status);
    });
  });

  describe('Domain Routing', () => {
    let domainRouteId: string;

    it('should create domain route', async () => {
      const domainRoute = TestDataFactory.createDomainRoute({
        domain: 'example.com',
        targetHost: 'mail.example.com',
        targetPort: 25,
      });

      const response = await apiClient.post('/api/domain-routing', domainRoute, {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(201);
      expect(response.data.domain).toBe(domainRoute.domain);
      expect(response.data.targetHost).toBe(domainRoute.targetHost);

      domainRouteId = response.data.id;
    });

    it('should route emails based on domain', async () => {
      // Send email to the configured domain
      await TestHelpers.sendTestEmail({
        to: '<EMAIL>',
        from: '<EMAIL>',
        subject: 'Domain Routing Test',
        text: 'Testing domain-based routing',
        smtpConfig: {
          host: 'localhost',
          port: SMTP_PORT,
          auth: {
            user: 'e2e-test-user',
            pass: 'password',
          },
        },
      });

      await TestHelpers.sleep(2000);

      // Verify the email was processed with the correct route
      const response = await apiClient.get('/api/email-transactions', {
        headers: { Authorization: `Bearer ${authToken}` },
        params: { limit: 10 },
      });

      const transaction = response.data.data.find((t: any) => 
        t.subject === 'Domain Routing Test'
      );
      expect(transaction).toBeDefined();
      expect(transaction.route).toContain('example.com');
    });
  });

  describe('Monitoring and Health Checks', () => {
    it('should return system health status', async () => {
      const response = await apiClient.get('/api/health');

      expect(response.status).toBe(200);
      expect(response.data.status).toBeDefined();
      expect(response.data.timestamp).toBeDefined();
      expect(response.data.uptime).toBeGreaterThan(0);
    });

    it('should return detailed health information', async () => {
      const response = await apiClient.get('/api/health/detailed', {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(200);
      expect(response.data.checks).toBeDefined();
      expect(response.data.checks.database).toBeDefined();
      expect(response.data.checks.smtpEngine).toBeDefined();
    });

    it('should return metrics summary', async () => {
      const response = await apiClient.get('/api/metrics/summary', {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(200);
      expect(response.data.totalMetrics).toBeDefined();
      expect(typeof response.data.totalMetrics).toBe('number');
    });
  });

  describe('Security Features', () => {
    it('should enforce rate limiting', async () => {
      // Make multiple rapid requests to test rate limiting
      const requests = Array.from({ length: 20 }, () =>
        apiClient.get('/api/health')
      );

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited (429 status)
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });

    it('should log security events', async () => {
      // Attempt unauthorized access
      await apiClient.get('/api/users', {
        headers: { Authorization: 'Bearer invalid-token' },
      });

      // Check if security event was logged
      const response = await apiClient.get('/api/security/events', {
        headers: { Authorization: `Bearer ${authToken}` },
        params: { limit: 10 },
      });

      expect(response.status).toBe(200);
      expect(response.data.data.length).toBeGreaterThan(0);
    });
  });

  describe('Performance Tests', () => {
    it('should handle concurrent API requests', async () => {
      const concurrentRequests = 10;
      const requests = Array.from({ length: concurrentRequests }, () =>
        apiClient.get('/api/health', {
          headers: { Authorization: `Bearer ${authToken}` },
        })
      );

      const startTime = Date.now();
      const responses = await Promise.all(requests);
      const endTime = Date.now();

      // All requests should succeed
      responses.forEach(response => {
        expect(response.status).toBe(200);
      });

      // Should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(5000);
    });

    it('should handle email sending load', async () => {
      const emailCount = 5;
      const emails = Array.from({ length: emailCount }, (_, index) =>
        TestHelpers.sendTestEmail({
          to: `load-test-${index}@example.com`,
          from: '<EMAIL>',
          subject: `Load Test Email ${index}`,
          text: `This is load test email number ${index}`,
          smtpConfig: {
            host: 'localhost',
            port: SMTP_PORT,
            auth: {
              user: 'e2e-test-user',
              pass: 'password',
            },
          },
        })
      );

      const startTime = Date.now();
      await Promise.all(emails);
      const endTime = Date.now();

      // Should complete within reasonable time
      expect(endTime - startTime).toBeLessThan(10000);

      // Wait for transactions to be recorded
      await TestHelpers.sleep(3000);

      // Verify all emails were processed
      const response = await apiClient.get('/api/email-transactions', {
        headers: { Authorization: `Bearer ${authToken}` },
        params: { limit: emailCount + 10 },
      });

      const loadTestEmails = response.data.data.filter((t: any) => 
        t.subject.startsWith('Load Test Email')
      );
      expect(loadTestEmails.length).toBe(emailCount);
    });
  });

  describe('Error Handling', () => {
    it('should handle database connection errors gracefully', async () => {
      // This would require temporarily disrupting the database connection
      // For now, we'll test that the API returns appropriate error responses
      const response = await apiClient.get('/api/nonexistent-endpoint', {
        headers: { Authorization: `Bearer ${authToken}` },
      });

      expect(response.status).toBe(404);
    });

    it('should handle SMTP server errors', async () => {
      // Try to send email to invalid SMTP server
      await expect(
        TestHelpers.sendTestEmail({
          to: '<EMAIL>',
          from: '<EMAIL>',
          subject: 'Error Test',
          text: 'This should fail',
          smtpConfig: {
            host: 'nonexistent-server.com',
            port: 25,
          },
        })
      ).rejects.toThrow();
    });
  });
});
