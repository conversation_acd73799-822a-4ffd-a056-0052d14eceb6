import React from 'react';
import { Helmet } from 'react-helmet-async';

export default function SmtpConfigPage() {
  return (
    <>
      <Helmet>
        <title>SMTP Configuration - IronRelay</title>
      </Helmet>

      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            SMTP Configuration
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Configure SMTP server settings and parameters
          </p>
        </div>

        <div className="card">
          <div className="card-body">
            <p className="text-gray-500 dark:text-gray-400 text-center py-8">
              SMTP configuration interface coming soon...
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
