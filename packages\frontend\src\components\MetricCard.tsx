import React from 'react';
import { clsx } from 'clsx';

interface MetricCardProps {
  title: string;
  value: number | string;
  icon: React.ComponentType<{ className?: string }>;
  color?: 'primary' | 'success' | 'warning' | 'error' | 'gray';
  change?: {
    value: number;
    type: 'increase' | 'decrease';
    period?: string;
  };
  className?: string;
}

const colorClasses = {
  primary: 'text-primary-600 dark:text-primary-400',
  success: 'text-success-600 dark:text-success-400',
  warning: 'text-warning-600 dark:text-warning-400',
  error: 'text-error-600 dark:text-error-400',
  gray: 'text-gray-600 dark:text-gray-400',
};

export default function MetricCard({
  title,
  value,
  icon: Icon,
  color = 'primary',
  change,
  className,
}: MetricCardProps) {
  const formatValue = (val: number | string): string => {
    if (typeof val === 'string') return val;
    
    if (val >= 1000000) {
      return `${(val / 1000000).toFixed(1)}M`;
    } else if (val >= 1000) {
      return `${(val / 1000).toFixed(1)}K`;
    }
    
    return val.toLocaleString();
  };

  return (
    <div className={clsx('metric-card', className)}>
      <div className="flex items-center">
        <div className="flex-shrink-0">
          <Icon className={clsx('h-8 w-8', colorClasses[color])} />
        </div>
        <div className="ml-5 w-0 flex-1">
          <dl>
            <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
              {title}
            </dt>
            <dd className="flex items-baseline">
              <div className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
                {formatValue(value)}
              </div>
              {change && (
                <div className="ml-2 flex items-baseline text-sm font-semibold">
                  <span
                    className={clsx(
                      change.type === 'increase'
                        ? 'text-success-600 dark:text-success-400'
                        : 'text-error-600 dark:text-error-400'
                    )}
                  >
                    {change.type === 'increase' ? '+' : '-'}{Math.abs(change.value)}%
                  </span>
                  {change.period && (
                    <span className="ml-1 text-gray-500 dark:text-gray-400">
                      {change.period}
                    </span>
                  )}
                </div>
              )}
            </dd>
          </dl>
        </div>
      </div>
    </div>
  );
}
