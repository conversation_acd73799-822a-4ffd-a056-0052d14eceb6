// IronRelay Security Checks Plugin
// Handles SPF, DKIM, DMARC validation and other security checks

import * as dns from 'dns';
import { Logger } from '../utils/logger';
import { DatabaseService } from '../services/database.service';
import { MetricsService } from '../services/metrics.service';

interface HarakaConnection {
  uuid: string;
  remote: {
    ip: string;
    host?: string;
  };
  notes: Record<string, any>;
  transaction?: HarakaTransaction;
  loginfo: (plugin: any, message: string) => void;
  logwarn: (plugin: any, message: string) => void;
  logerror: (plugin: any, message: string) => void;
}

interface HarakaTransaction {
  uuid: string;
  mail_from: {
    user: string;
    host: string;
    original: string;
  };
  rcpt_to: Array<{
    user: string;
    host: string;
    original: string;
  }>;
  header: {
    get: (name: string) => string | undefined;
    get_all: (name: string) => string[];
  };
  notes: Record<string, any>;
}

interface HarakaNext {
  (action?: number, message?: string): void;
  DENY: number;
  DENYSOFT: number;
  DENYDISCONNECT: number;
  OK: number;
  CONT: number;
}

interface HarakaPlugin {
  config: any;
  register: () => void;
  hook_mail: (next: HarakaNext, connection: HarakaConnection, params: any[]) => void;
  hook_data_post: (next: HarakaNext, connection: HarakaConnection) => void;
}

class SecurityChecksPlugin implements HarakaPlugin {
  public config: any;
  private logger = Logger.getInstance();
  private database = DatabaseService.getInstance();
  private metrics = MetricsService.getInstance();
  
  // Configuration
  private spfEnabled = true;
  private dkimEnabled = true;
  private dmarcEnabled = true;
  private strictMode = false;
  private blockOnFailure = false;

  public register(): void {
    const plugin = this;

    // Load configuration
    plugin.loadConfig();

    // Register hooks
    plugin.register_hook('mail', 'check_spf');
    plugin.register_hook('data_post', 'check_dkim_dmarc');
  }

  private loadConfig(): void {
    try {
      const config = this.config.get('ironrelay.security_checks.ini') || {};
      
      this.spfEnabled = config.spf_enabled !== 'false';
      this.dkimEnabled = config.dkim_enabled !== 'false';
      this.dmarcEnabled = config.dmarc_enabled !== 'false';
      this.strictMode = config.strict_mode === 'true';
      this.blockOnFailure = config.block_on_failure === 'true';

      this.logger.info('Security checks configuration loaded', {
        spfEnabled: this.spfEnabled,
        dkimEnabled: this.dkimEnabled,
        dmarcEnabled: this.dmarcEnabled,
        strictMode: this.strictMode,
        blockOnFailure: this.blockOnFailure,
      });
    } catch (error) {
      this.logger.error('Failed to load security checks configuration', error);
    }
  }

  public hook_mail(next: HarakaNext, connection: HarakaConnection, params: any[]): void {
    const plugin = this;
    const transaction = connection.transaction;

    if (!transaction) {
      return next(next.OK);
    }

    try {
      // Initialize security check results
      transaction.notes.security_checks = {
        spf: { result: 'NONE', reason: 'Not checked' },
        dkim: { result: 'NONE', reason: 'Not checked' },
        dmarc: { result: 'NONE', reason: 'Not checked' },
      };

      // Perform SPF check if enabled
      if (this.spfEnabled) {
        this.checkSPF(connection, transaction).then(result => {
          transaction.notes.security_checks.spf = result;
          
          if (result.result === 'FAIL' && this.blockOnFailure) {
            connection.logwarn(plugin, `SPF check failed: ${result.reason}`);
            this.metrics.recordError('security', 'spf_fail');
            this.logSecurityEvent('SPF_FAILURE', connection, { spfResult: result });
          }
        }).catch(error => {
          connection.logerror(plugin, `SPF check error: ${error}`);
          transaction.notes.security_checks.spf = { result: 'TEMPERROR', reason: error.message };
        });
      }

      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Security checks error: ${error}`);
      this.logger.error('Security checks plugin error', error);
      return next(next.OK);
    }
  }

  public hook_data_post(next: HarakaNext, connection: HarakaConnection): void {
    const plugin = this;
    const transaction = connection.transaction;

    if (!transaction || !transaction.notes.security_checks) {
      return next(next.OK);
    }

    try {
      // Perform DKIM check if enabled
      if (this.dkimEnabled) {
        this.checkDKIM(connection, transaction).then(result => {
          transaction.notes.security_checks.dkim = result;
          
          if (result.result === 'FAIL' && this.blockOnFailure) {
            connection.logwarn(plugin, `DKIM check failed: ${result.reason}`);
            this.metrics.recordError('security', 'dkim_fail');
            this.logSecurityEvent('DKIM_FAILURE', connection, { dkimResult: result });
          }
        }).catch(error => {
          connection.logerror(plugin, `DKIM check error: ${error}`);
          transaction.notes.security_checks.dkim = { result: 'TEMPERROR', reason: error.message };
        });
      }

      // Perform DMARC check if enabled
      if (this.dmarcEnabled) {
        this.checkDMARC(connection, transaction).then(result => {
          transaction.notes.security_checks.dmarc = result;
          
          if (result.result === 'FAIL' && this.blockOnFailure) {
            connection.logwarn(plugin, `DMARC check failed: ${result.reason}`);
            this.metrics.recordError('security', 'dmarc_fail');
            this.logSecurityEvent('DMARC_FAILURE', connection, { dmarcResult: result });
          }
        }).catch(error => {
          connection.logerror(plugin, `DMARC check error: ${error}`);
          transaction.notes.security_checks.dmarc = { result: 'TEMPERROR', reason: error.message };
        });
      }

      // Perform additional security checks
      this.performAdditionalChecks(connection, transaction);

      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Security checks data_post error: ${error}`);
      this.logger.error('Security checks data_post error', error);
      return next(next.OK);
    }
  }

  private async checkSPF(connection: HarakaConnection, transaction: HarakaTransaction): Promise<any> {
    try {
      const senderDomain = transaction.mail_from.host;
      const senderIP = connection.remote.ip;

      if (!senderDomain) {
        return { result: 'NONE', reason: 'No sender domain' };
      }

      // Get SPF record
      const spfRecord = await this.getSPFRecord(senderDomain);
      
      if (!spfRecord) {
        return { result: 'NONE', reason: 'No SPF record found' };
      }

      // Parse and evaluate SPF record
      const spfResult = await this.evaluateSPF(spfRecord, senderIP, senderDomain);
      
      this.logger.debug(`SPF check for ${senderDomain} from ${senderIP}: ${spfResult.result}`);
      
      return spfResult;

    } catch (error) {
      this.logger.error('SPF check error', error);
      return { result: 'TEMPERROR', reason: error.message };
    }
  }

  private async checkDKIM(connection: HarakaConnection, transaction: HarakaTransaction): Promise<any> {
    try {
      if (!transaction.header) {
        return { result: 'NONE', reason: 'No headers available' };
      }

      // Get DKIM-Signature header
      const dkimSignature = transaction.header.get('DKIM-Signature');
      
      if (!dkimSignature) {
        return { result: 'NONE', reason: 'No DKIM signature found' };
      }

      // Parse DKIM signature
      const dkimParams = this.parseDKIMSignature(dkimSignature);
      
      if (!dkimParams.d || !dkimParams.s) {
        return { result: 'FAIL', reason: 'Invalid DKIM signature format' };
      }

      // Get DKIM public key
      const publicKey = await this.getDKIMPublicKey(dkimParams.d, dkimParams.s);
      
      if (!publicKey) {
        return { result: 'FAIL', reason: 'DKIM public key not found' };
      }

      // Verify DKIM signature (simplified - in production use a proper DKIM library)
      const verified = await this.verifyDKIMSignature(dkimSignature, publicKey, transaction);
      
      return {
        result: verified ? 'PASS' : 'FAIL',
        reason: verified ? 'DKIM signature valid' : 'DKIM signature verification failed',
        domain: dkimParams.d,
        selector: dkimParams.s,
      };

    } catch (error) {
      this.logger.error('DKIM check error', error);
      return { result: 'TEMPERROR', reason: error.message };
    }
  }

  private async checkDMARC(connection: HarakaConnection, transaction: HarakaTransaction): Promise<any> {
    try {
      const fromDomain = this.extractDomainFromHeader(transaction.header?.get('From') || '');
      
      if (!fromDomain) {
        return { result: 'NONE', reason: 'No From domain found' };
      }

      // Get DMARC policy
      const dmarcPolicy = await this.getDMARCPolicy(fromDomain);
      
      if (!dmarcPolicy) {
        return { result: 'NONE', reason: 'No DMARC policy found' };
      }

      // Check SPF and DKIM alignment
      const spfResult = transaction.notes.security_checks?.spf;
      const dkimResult = transaction.notes.security_checks?.dkim;
      
      const spfAligned = this.checkSPFAlignment(transaction, fromDomain, spfResult);
      const dkimAligned = this.checkDKIMAlignment(transaction, fromDomain, dkimResult);

      // Evaluate DMARC policy
      const dmarcResult = this.evaluateDMARCPolicy(dmarcPolicy, spfAligned, dkimAligned);
      
      return {
        result: dmarcResult.result,
        reason: dmarcResult.reason,
        policy: dmarcPolicy.p,
        spfAligned,
        dkimAligned,
      };

    } catch (error) {
      this.logger.error('DMARC check error', error);
      return { result: 'TEMPERROR', reason: error.message };
    }
  }

  private async getSPFRecord(domain: string): Promise<string | null> {
    return new Promise((resolve) => {
      dns.resolveTxt(domain, (err, records) => {
        if (err) {
          resolve(null);
          return;
        }

        for (const record of records) {
          const txtRecord = Array.isArray(record) ? record.join('') : record;
          if (txtRecord.startsWith('v=spf1')) {
            resolve(txtRecord);
            return;
          }
        }

        resolve(null);
      });
    });
  }

  private async evaluateSPF(spfRecord: string, senderIP: string, domain: string): Promise<any> {
    // Simplified SPF evaluation - in production use a proper SPF library
    try {
      if (spfRecord.includes('include:') || spfRecord.includes('a:') || spfRecord.includes('mx:')) {
        // For now, assume PASS for complex SPF records
        return { result: 'PASS', reason: 'SPF record allows sender' };
      }

      if (spfRecord.includes('-all')) {
        return { result: 'FAIL', reason: 'SPF hard fail (-all)' };
      }

      if (spfRecord.includes('~all')) {
        return { result: 'SOFTFAIL', reason: 'SPF soft fail (~all)' };
      }

      return { result: 'NEUTRAL', reason: 'SPF neutral result' };

    } catch (error) {
      return { result: 'TEMPERROR', reason: error.message };
    }
  }

  private parseDKIMSignature(signature: string): Record<string, string> {
    const params: Record<string, string> = {};
    const pairs = signature.split(';');

    for (const pair of pairs) {
      const [key, value] = pair.split('=', 2);
      if (key && value) {
        params[key.trim()] = value.trim();
      }
    }

    return params;
  }

  private async getDKIMPublicKey(domain: string, selector: string): Promise<string | null> {
    return new Promise((resolve) => {
      const dkimDomain = `${selector}._domainkey.${domain}`;
      
      dns.resolveTxt(dkimDomain, (err, records) => {
        if (err) {
          resolve(null);
          return;
        }

        for (const record of records) {
          const txtRecord = Array.isArray(record) ? record.join('') : record;
          if (txtRecord.includes('p=')) {
            resolve(txtRecord);
            return;
          }
        }

        resolve(null);
      });
    });
  }

  private async verifyDKIMSignature(signature: string, publicKey: string, transaction: HarakaTransaction): Promise<boolean> {
    // Simplified DKIM verification - in production use a proper DKIM library
    // This is just a placeholder that returns true for demonstration
    return true;
  }

  private async getDMARCPolicy(domain: string): Promise<any> {
    return new Promise((resolve) => {
      const dmarcDomain = `_dmarc.${domain}`;
      
      dns.resolveTxt(dmarcDomain, (err, records) => {
        if (err) {
          resolve(null);
          return;
        }

        for (const record of records) {
          const txtRecord = Array.isArray(record) ? record.join('') : record;
          if (txtRecord.startsWith('v=DMARC1')) {
            resolve(this.parseDMARCRecord(txtRecord));
            return;
          }
        }

        resolve(null);
      });
    });
  }

  private parseDMARCRecord(record: string): Record<string, string> {
    const params: Record<string, string> = {};
    const pairs = record.split(';');

    for (const pair of pairs) {
      const [key, value] = pair.split('=', 2);
      if (key && value) {
        params[key.trim()] = value.trim();
      }
    }

    return params;
  }

  private extractDomainFromHeader(header: string): string | null {
    const match = header.match(/@([^>\s]+)/);
    return match ? match[1] : null;
  }

  private checkSPFAlignment(transaction: HarakaTransaction, fromDomain: string, spfResult: any): boolean {
    if (!spfResult || spfResult.result !== 'PASS') {
      return false;
    }

    // Check if mail from domain aligns with From header domain
    return transaction.mail_from.host === fromDomain;
  }

  private checkDKIMAlignment(transaction: HarakaTransaction, fromDomain: string, dkimResult: any): boolean {
    if (!dkimResult || dkimResult.result !== 'PASS') {
      return false;
    }

    // Check if DKIM signing domain aligns with From header domain
    return dkimResult.domain === fromDomain;
  }

  private evaluateDMARCPolicy(policy: any, spfAligned: boolean, dkimAligned: boolean): any {
    const policyAction = policy.p || 'none';
    
    // DMARC passes if either SPF or DKIM is aligned and passes
    const dmarcPass = spfAligned || dkimAligned;

    if (dmarcPass) {
      return { result: 'PASS', reason: 'DMARC alignment satisfied' };
    }

    switch (policyAction) {
      case 'reject':
        return { result: 'FAIL', reason: 'DMARC policy reject' };
      case 'quarantine':
        return { result: 'FAIL', reason: 'DMARC policy quarantine' };
      case 'none':
        return { result: 'FAIL', reason: 'DMARC policy none (monitoring only)' };
      default:
        return { result: 'FAIL', reason: 'Unknown DMARC policy' };
    }
  }

  private performAdditionalChecks(connection: HarakaConnection, transaction: HarakaTransaction): void {
    try {
      // Check for suspicious patterns
      this.checkSuspiciousPatterns(connection, transaction);
      
      // Check message size limits
      this.checkMessageSize(connection, transaction);
      
      // Check recipient limits
      this.checkRecipientLimits(connection, transaction);

    } catch (error) {
      this.logger.error('Additional security checks error', error);
    }
  }

  private checkSuspiciousPatterns(connection: HarakaConnection, transaction: HarakaTransaction): void {
    const subject = transaction.header?.get('Subject') || '';
    const from = transaction.header?.get('From') || '';

    // Check for suspicious subject patterns
    const suspiciousSubjects = [
      /urgent/i,
      /winner/i,
      /congratulations/i,
      /click here/i,
      /free money/i,
      /nigerian prince/i,
    ];

    for (const pattern of suspiciousSubjects) {
      if (pattern.test(subject)) {
        this.logSecurityEvent('SUSPICIOUS_SUBJECT', connection, { subject, pattern: pattern.source });
        break;
      }
    }

    // Check for suspicious sender patterns
    if (from.includes('noreply') && transaction.rcpt_to.length > 10) {
      this.logSecurityEvent('BULK_EMAIL_PATTERN', connection, { from, recipientCount: transaction.rcpt_to.length });
    }
  }

  private checkMessageSize(connection: HarakaConnection, transaction: HarakaTransaction): void {
    // This would be implemented based on your message size limits
    // For now, just log large messages
    const messageSize = transaction.notes.ironrelay_log?.messageSize || 0;
    
    if (messageSize > 10 * 1024 * 1024) { // 10MB
      this.logSecurityEvent('LARGE_MESSAGE', connection, { messageSize });
    }
  }

  private checkRecipientLimits(connection: HarakaConnection, transaction: HarakaTransaction): void {
    const recipientCount = transaction.rcpt_to.length;
    
    if (recipientCount > 50) {
      this.logSecurityEvent('HIGH_RECIPIENT_COUNT', connection, { recipientCount });
    }
  }

  private async logSecurityEvent(
    type: string,
    connection: HarakaConnection,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await this.database.createSecurityEvent({
        type,
        severity: 'MEDIUM',
        source: 'smtp_security_checks',
        description: `Security check event: ${type}`,
        ipAddress: connection.remote.ip,
        metadata: {
          connectionId: connection.uuid,
          hostname: connection.remote.host,
          ...metadata,
        },
        blocked: false,
        action: 'LOG_ONLY',
      });
    } catch (error) {
      this.logger.error('Failed to log security event', error);
    }
  }

  // Utility methods
  public getSecurityCheckResults(transaction: HarakaTransaction): any {
    return transaction.notes.security_checks || {};
  }

  public async reloadConfig(): Promise<void> {
    try {
      this.logger.info('Reloading security checks configuration...');
      this.loadConfig();
      this.logger.info('Security checks configuration reloaded successfully');
    } catch (error) {
      this.logger.error('Failed to reload security checks configuration', error);
      throw error;
    }
  }
}

// Export for Haraka plugin system
module.exports = new SecurityChecksPlugin();
