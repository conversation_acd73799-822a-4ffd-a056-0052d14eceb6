// IronRelay Access Control Plugin
// Handles IP/CIDR whitelisting and blacklisting

import * as ipaddr from 'ipaddr.js';
import { CIDRMatcher } from 'cidr-matcher';
import { Logger } from '../utils/logger';
import { DatabaseService } from '../services/database.service';
import { MetricsService } from '../services/metrics.service';

interface HarakaConnection {
  uuid: string;
  remote: {
    ip: string;
    host?: string;
  };
  notes: Record<string, any>;
  loginfo: (plugin: any, message: string) => void;
  logwarn: (plugin: any, message: string) => void;
  logerror: (plugin: any, message: string) => void;
}

interface HarakaNext {
  (action?: number, message?: string): void;
  DENY: number;
  DENYSOFT: number;
  DENYDISCONNECT: number;
  OK: number;
  CONT: number;
}

interface HarakaPlugin {
  config: any;
  register: () => void;
  hook_connect: (next: HarakaNext, connection: HarakaConnection) => void;
  hook_lookup_rdns: (next: HarakaNext, connection: HarakaConnection) => void;
}

class AccessControlPlugin implements HarakaPlugin {
  public config: any;
  private logger = Logger.getInstance();
  private database = DatabaseService.getInstance();
  private metrics = MetricsService.getInstance();
  private ipWhitelist: string[] = [];
  private ipBlacklist: string[] = [];
  private cidrWhitelist: CIDRMatcher;
  private cidrBlacklist: CIDRMatcher;

  constructor() {
    this.cidrWhitelist = new CIDRMatcher();
    this.cidrBlacklist = new CIDRMatcher();
  }

  public register(): void {
    const plugin = this;

    // Load configuration
    plugin.loadConfig();

    // Register hooks
    plugin.register_hook('connect', 'check_access_control');
    plugin.register_hook('lookup_rdns', 'check_rdns_access');
  }

  private loadConfig(): void {
    try {
      // Load from plugin configuration
      const config = this.config.get('ironrelay.access_control.ini');
      
      if (config.whitelist) {
        this.ipWhitelist = config.whitelist.ips ? config.whitelist.ips.split(',').filter(Boolean) : [];
        const cidrWhitelist = config.whitelist.cidrs ? config.whitelist.cidrs.split(',').filter(Boolean) : [];
        
        cidrWhitelist.forEach(cidr => {
          try {
            this.cidrWhitelist.addNetworkClass(cidr);
          } catch (error) {
            this.logger.error(`Invalid CIDR whitelist entry: ${cidr}`, error);
          }
        });
      }

      if (config.blacklist) {
        this.ipBlacklist = config.blacklist.ips ? config.blacklist.ips.split(',').filter(Boolean) : [];
        const cidrBlacklist = config.blacklist.cidrs ? config.blacklist.cidrs.split(',').filter(Boolean) : [];
        
        cidrBlacklist.forEach(cidr => {
          try {
            this.cidrBlacklist.addNetworkClass(cidr);
          } catch (error) {
            this.logger.error(`Invalid CIDR blacklist entry: ${cidr}`, error);
          }
        });
      }

      this.logger.info('Access control configuration loaded', {
        ipWhitelist: this.ipWhitelist.length,
        ipBlacklist: this.ipBlacklist.length,
        cidrWhitelist: this.cidrWhitelist.length,
        cidrBlacklist: this.cidrBlacklist.length,
      });
    } catch (error) {
      this.logger.error('Failed to load access control configuration', error);
    }
  }

  public hook_connect(next: HarakaNext, connection: HarakaConnection): void {
    const plugin = this;
    const remoteIP = connection.remote.ip;

    try {
      // Normalize IP address
      let normalizedIP: string;
      try {
        const addr = ipaddr.process(remoteIP);
        normalizedIP = addr.toString();
      } catch (error) {
        connection.logerror(plugin, `Invalid IP address: ${remoteIP}`);
        this.metrics.recordError('access_control', 'invalid_ip');
        return next(next.DENYDISCONNECT, 'Invalid IP address');
      }

      // Check blacklist first (highest priority)
      if (this.isBlacklisted(normalizedIP)) {
        connection.logwarn(plugin, `Connection denied - IP ${normalizedIP} is blacklisted`);
        this.metrics.recordConnection('rejected', normalizedIP);
        this.logSecurityEvent('IP_BLACKLISTED', normalizedIP, connection);
        return next(next.DENYDISCONNECT, 'Connection not allowed');
      }

      // Check whitelist (if configured)
      if (this.hasWhitelist() && !this.isWhitelisted(normalizedIP)) {
        connection.logwarn(plugin, `Connection denied - IP ${normalizedIP} not in whitelist`);
        this.metrics.recordConnection('rejected', normalizedIP);
        this.logSecurityEvent('IP_NOT_WHITELISTED', normalizedIP, connection);
        return next(next.DENYDISCONNECT, 'Connection not allowed');
      }

      // Store access control result in connection notes
      connection.notes.access_control = {
        ip: normalizedIP,
        whitelisted: this.isWhitelisted(normalizedIP),
        blacklisted: false,
        allowed: true,
      };

      connection.loginfo(plugin, `Connection allowed from ${normalizedIP}`);
      this.metrics.recordConnection('accepted', normalizedIP);
      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Access control error: ${error}`);
      this.logger.error('Access control plugin error', error);
      this.metrics.recordError('access_control', 'plugin_error');
      return next(next.DENYSOFT, 'Temporary access control failure');
    }
  }

  public hook_lookup_rdns(next: HarakaNext, connection: HarakaConnection): void {
    const plugin = this;
    const remoteIP = connection.remote.ip;
    const remoteHost = connection.remote.host;

    try {
      // Additional checks based on reverse DNS
      if (remoteHost) {
        // Check for suspicious hostnames
        const suspiciousPatterns = [
          /dynamic/i,
          /dial-?up/i,
          /dsl/i,
          /cable/i,
          /residential/i,
          /pool/i,
          /dhcp/i,
        ];

        const isSuspicious = suspiciousPatterns.some(pattern => pattern.test(remoteHost));
        
        if (isSuspicious) {
          connection.logwarn(plugin, `Suspicious hostname detected: ${remoteHost}`);
          this.logSecurityEvent('SUSPICIOUS_HOSTNAME', remoteIP, connection, { hostname: remoteHost });
          
          // Don't block, but log for monitoring
          connection.notes.access_control = {
            ...connection.notes.access_control,
            suspicious_hostname: true,
            hostname: remoteHost,
          };
        }

        // Check for missing reverse DNS
        if (!remoteHost || remoteHost === remoteIP) {
          connection.loginfo(plugin, `No reverse DNS for ${remoteIP}`);
          connection.notes.access_control = {
            ...connection.notes.access_control,
            no_rdns: true,
          };
        }
      }

      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `RDNS access control error: ${error}`);
      this.logger.error('RDNS access control error', error);
      return next(next.OK); // Don't block on RDNS errors
    }
  }

  private isWhitelisted(ip: string): boolean {
    // Check exact IP match
    if (this.ipWhitelist.includes(ip)) {
      return true;
    }

    // Check CIDR match
    try {
      return this.cidrWhitelist.contains(ip);
    } catch (error) {
      this.logger.error(`Error checking CIDR whitelist for IP ${ip}`, error);
      return false;
    }
  }

  private isBlacklisted(ip: string): boolean {
    // Check exact IP match
    if (this.ipBlacklist.includes(ip)) {
      return true;
    }

    // Check CIDR match
    try {
      return this.cidrBlacklist.contains(ip);
    } catch (error) {
      this.logger.error(`Error checking CIDR blacklist for IP ${ip}`, error);
      return false;
    }
  }

  private hasWhitelist(): boolean {
    return this.ipWhitelist.length > 0 || this.cidrWhitelist.length > 0;
  }

  private async logSecurityEvent(
    type: string,
    remoteIP: string,
    connection: HarakaConnection,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await this.database.createSecurityEvent({
        type,
        severity: 'MEDIUM',
        source: 'smtp_access_control',
        description: `Access control event: ${type}`,
        ipAddress: remoteIP,
        metadata: {
          connectionId: connection.uuid,
          hostname: connection.remote.host,
          ...metadata,
        },
        blocked: true,
        action: 'DENY_CONNECTION',
      });
    } catch (error) {
      this.logger.error('Failed to log security event', error);
    }
  }

  // Method to reload configuration (can be called via API)
  public async reloadConfig(): Promise<void> {
    try {
      this.logger.info('Reloading access control configuration...');
      
      // Clear existing configuration
      this.ipWhitelist = [];
      this.ipBlacklist = [];
      this.cidrWhitelist = new CIDRMatcher();
      this.cidrBlacklist = new CIDRMatcher();

      // Reload from database and configuration
      await this.loadConfigFromDatabase();
      this.loadConfig();

      this.logger.info('Access control configuration reloaded successfully');
    } catch (error) {
      this.logger.error('Failed to reload access control configuration', error);
      throw error;
    }
  }

  private async loadConfigFromDatabase(): Promise<void> {
    try {
      // Load SMTP configuration from database
      const smtpConfig = await this.database.getSMTPConfig() as any;
      
      if (smtpConfig) {
        // Update IP whitelist/blacklist from database
        if (smtpConfig.ipWhitelist) {
          this.ipWhitelist.push(...smtpConfig.ipWhitelist);
        }
        
        if (smtpConfig.ipBlacklist) {
          this.ipBlacklist.push(...smtpConfig.ipBlacklist);
        }

        if (smtpConfig.cidrWhitelist) {
          smtpConfig.cidrWhitelist.forEach((cidr: string) => {
            try {
              this.cidrWhitelist.addNetworkClass(cidr);
            } catch (error) {
              this.logger.error(`Invalid CIDR whitelist entry from database: ${cidr}`, error);
            }
          });
        }

        if (smtpConfig.cidrBlacklist) {
          smtpConfig.cidrBlacklist.forEach((cidr: string) => {
            try {
              this.cidrBlacklist.addNetworkClass(cidr);
            } catch (error) {
              this.logger.error(`Invalid CIDR blacklist entry from database: ${cidr}`, error);
            }
          });
        }
      }
    } catch (error) {
      this.logger.error('Failed to load access control configuration from database', error);
    }
  }

  // Utility methods for external access
  public getWhitelistedIPs(): string[] {
    return [...this.ipWhitelist];
  }

  public getBlacklistedIPs(): string[] {
    return [...this.ipBlacklist];
  }

  public checkIPAccess(ip: string): { allowed: boolean; reason?: string } {
    try {
      const normalizedIP = ipaddr.process(ip).toString();

      if (this.isBlacklisted(normalizedIP)) {
        return { allowed: false, reason: 'IP is blacklisted' };
      }

      if (this.hasWhitelist() && !this.isWhitelisted(normalizedIP)) {
        return { allowed: false, reason: 'IP not in whitelist' };
      }

      return { allowed: true };
    } catch (error) {
      return { allowed: false, reason: 'Invalid IP address' };
    }
  }
}

// Export for Haraka plugin system
module.exports = new AccessControlPlugin();
