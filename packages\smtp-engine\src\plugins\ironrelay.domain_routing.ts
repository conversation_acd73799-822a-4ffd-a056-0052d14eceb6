// IronRelay Domain Routing Plugin
// Handles domain-based routing with priority and failover

import { Logger } from '../utils/logger';
import { DatabaseService } from '../services/database.service';
import { MetricsService } from '../services/metrics.service';

interface HarakaConnection {
  uuid: string;
  remote: {
    ip: string;
    host?: string;
  };
  notes: Record<string, any>;
  transaction?: HarakaTransaction;
  loginfo: (plugin: any, message: string) => void;
  logwarn: (plugin: any, message: string) => void;
  logerror: (plugin: any, message: string) => void;
}

interface HarakaTransaction {
  uuid: string;
  mail_from: {
    user: string;
    host: string;
  };
  rcpt_to: Array<{
    user: string;
    host: string;
  }>;
  notes: Record<string, any>;
}

interface HarakaNext {
  (action?: number, message?: string): void;
  DENY: number;
  DENYSOFT: number;
  DENYDISCONNECT: number;
  OK: number;
  CONT: number;
}

interface DomainRoute {
  id: string;
  domain: string;
  destinationHost: string;
  destinationPort: number;
  destinationTLS: boolean;
  destinationUsername?: string;
  destinationPassword?: string;
  priority: number;
  maxRetries: number;
  retryDelay: number;
  enabled: boolean;
}

interface HarakaPlugin {
  config: any;
  register: () => void;
  hook_rcpt: (next: HarakaNext, connection: HarakaConnection, params: any[]) => void;
  hook_get_mx: (next: HarakaNext, hmail: any, domain: string) => void;
}

class DomainRoutingPlugin implements HarakaPlugin {
  public config: any;
  private logger = Logger.getInstance();
  private database = DatabaseService.getInstance();
  private metrics = MetricsService.getInstance();
  private routeCache = new Map<string, DomainRoute[]>();
  private cacheExpiry = new Map<string, number>();
  private cacheTTL = 300000; // 5 minutes

  public register(): void {
    const plugin = this;

    // Register hooks
    plugin.register_hook('rcpt', 'check_domain_routing');
    plugin.register_hook('get_mx', 'route_domain');
  }

  public hook_rcpt(next: HarakaNext, connection: HarakaConnection, params: any[]): void {
    const plugin = this;
    const transaction = connection.transaction;

    if (!transaction) {
      return next(next.CONT);
    }

    try {
      const rcptTo = params[0];
      const domain = rcptTo.host.toLowerCase();

      // Check if user is authorized for this domain
      if (connection.notes.auth_user) {
        const authUser = connection.notes.auth_user;
        
        if (authUser.allowedDomains && authUser.allowedDomains.length > 0) {
          const isAllowed = authUser.allowedDomains.includes('*') || 
                           authUser.allowedDomains.includes(domain);
          
          if (!isAllowed) {
            connection.logwarn(plugin, `User ${authUser.username} not authorized for domain ${domain}`);
            this.logSecurityEvent('DOMAIN_NOT_AUTHORIZED', connection, { domain, username: authUser.username });
            return next(next.DENY, `Not authorized for domain ${domain}`);
          }
        }
      }

      // Store domain routing information for later use
      if (!transaction.notes.domain_routes) {
        transaction.notes.domain_routes = new Map();
      }

      // Get route for this domain (async, but don't wait here)
      this.getDomainRoute(domain).then(route => {
        if (route) {
          transaction.notes.domain_routes.set(domain, route);
          connection.loginfo(plugin, `Route found for domain ${domain}: ${route.destinationHost}:${route.destinationPort}`);
        } else {
          connection.logwarn(plugin, `No route found for domain ${domain}`);
        }
      }).catch(error => {
        connection.logerror(plugin, `Error getting route for domain ${domain}: ${error}`);
      });

      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Domain routing error: ${error}`);
      this.logger.error('Domain routing plugin error', error);
      return next(next.DENYSOFT, 'Temporary routing failure');
    }
  }

  public hook_get_mx(next: HarakaNext, hmail: any, domain: string): void {
    const plugin = this;

    try {
      const normalizedDomain = domain.toLowerCase();

      // Get route from cache or database
      this.getDomainRoute(normalizedDomain).then(route => {
        if (route) {
          // Create MX record for the route
          const mx = {
            priority: route.priority,
            exchange: route.destinationHost,
            port: route.destinationPort,
            using_tls: route.destinationTLS,
            auth_user: route.destinationUsername,
            auth_pass: route.destinationPassword,
          };

          // Store route information in hmail for delivery
          if (!hmail.notes) {
            hmail.notes = {};
          }
          hmail.notes.route = route;

          this.logger.info(`Domain routing: ${normalizedDomain} -> ${route.destinationHost}:${route.destinationPort}`);
          this.metrics.recordCustomMetric('domain_route_used', 1, { domain: normalizedDomain, destination: route.destinationHost });

          return next(next.OK, [mx]);
        } else {
          // No specific route found, let Haraka handle default MX lookup
          this.logger.debug(`No route found for domain ${normalizedDomain}, using default MX lookup`);
          return next(next.CONT);
        }
      }).catch(error => {
        this.logger.error(`Error getting MX route for domain ${normalizedDomain}`, error);
        this.metrics.recordError('domain_routing', 'mx_lookup_error');
        return next(next.DENYSOFT, 'Temporary routing failure');
      });

    } catch (error) {
      this.logger.error('MX routing error', error);
      this.metrics.recordError('domain_routing', 'mx_error');
      return next(next.DENYSOFT, 'Temporary routing failure');
    }
  }

  private async getDomainRoute(domain: string): Promise<DomainRoute | null> {
    try {
      // Check cache first
      const cached = this.getFromCache(domain);
      if (cached) {
        return cached;
      }

      // Get from database
      const route = await this.database.getDomainRoute(domain) as DomainRoute | null;
      
      if (route) {
        // Cache the result
        this.setCache(domain, [route]);
        return route;
      }

      // Try wildcard route
      const wildcardRoute = await this.database.getDomainRoute('*') as DomainRoute | null;
      if (wildcardRoute) {
        this.setCache(domain, [wildcardRoute]);
        return wildcardRoute;
      }

      return null;

    } catch (error) {
      this.logger.error(`Error getting domain route for ${domain}`, error);
      throw error;
    }
  }

  private async getAllDomainRoutes(domain: string): Promise<DomainRoute[]> {
    try {
      // Check cache first
      const cached = this.getFromCache(domain);
      if (cached) {
        return Array.isArray(cached) ? cached : [cached];
      }

      // Get all routes for domain (including fallbacks)
      const routes = await this.database.getDomainRoutes() as DomainRoute[];
      
      // Filter routes for this domain
      const domainRoutes = routes.filter(route => 
        route.domain === domain || route.domain === '*'
      ).sort((a, b) => a.priority - b.priority);

      // Cache the results
      this.setCache(domain, domainRoutes);
      
      return domainRoutes;

    } catch (error) {
      this.logger.error(`Error getting all domain routes for ${domain}`, error);
      throw error;
    }
  }

  private getFromCache(domain: string): DomainRoute | DomainRoute[] | null {
    const expiry = this.cacheExpiry.get(domain);
    if (expiry && Date.now() < expiry) {
      return this.routeCache.get(domain) || null;
    }

    // Cache expired, remove it
    this.routeCache.delete(domain);
    this.cacheExpiry.delete(domain);
    return null;
  }

  private setCache(domain: string, routes: DomainRoute[]): void {
    this.routeCache.set(domain, routes.length === 1 ? routes[0] : routes);
    this.cacheExpiry.set(domain, Date.now() + this.cacheTTL);
  }

  private clearCache(domain?: string): void {
    if (domain) {
      this.routeCache.delete(domain);
      this.cacheExpiry.delete(domain);
    } else {
      this.routeCache.clear();
      this.cacheExpiry.clear();
    }
  }

  private async logSecurityEvent(
    type: string,
    connection: HarakaConnection,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await this.database.createSecurityEvent({
        type,
        severity: 'MEDIUM',
        source: 'smtp_domain_routing',
        description: `Domain routing security event: ${type}`,
        ipAddress: connection.remote.ip,
        metadata: {
          connectionId: connection.uuid,
          hostname: connection.remote.host,
          ...metadata,
        },
        blocked: true,
        action: 'DENY_RECIPIENT',
      });
    } catch (error) {
      this.logger.error('Failed to log security event', error);
    }
  }

  // Method to reload routes (can be called via API)
  public async reloadRoutes(): Promise<void> {
    try {
      this.logger.info('Reloading domain routes...');
      this.clearCache();
      this.logger.info('Domain routes cache cleared');
    } catch (error) {
      this.logger.error('Failed to reload domain routes', error);
      throw error;
    }
  }

  // Method to get route statistics
  public getRouteStats(): Record<string, any> {
    return {
      cacheSize: this.routeCache.size,
      cacheEntries: Array.from(this.routeCache.keys()),
    };
  }

  // Method to validate domain routing configuration
  public async validateRoutes(): Promise<{ valid: boolean; errors: string[] }> {
    const errors: string[] = [];

    try {
      const routes = await this.database.getDomainRoutes() as DomainRoute[];
      
      for (const route of routes) {
        // Validate destination host
        if (!route.destinationHost || route.destinationHost.trim() === '') {
          errors.push(`Route ${route.id}: Destination host is required`);
        }

        // Validate port
        if (route.destinationPort < 1 || route.destinationPort > 65535) {
          errors.push(`Route ${route.id}: Invalid destination port ${route.destinationPort}`);
        }

        // Validate domain
        if (!route.domain || route.domain.trim() === '') {
          errors.push(`Route ${route.id}: Domain is required`);
        }

        // Validate priority
        if (route.priority < 0) {
          errors.push(`Route ${route.id}: Priority must be non-negative`);
        }

        // Validate retry settings
        if (route.maxRetries < 0) {
          errors.push(`Route ${route.id}: Max retries must be non-negative`);
        }

        if (route.retryDelay < 0) {
          errors.push(`Route ${route.id}: Retry delay must be non-negative`);
        }
      }

      return {
        valid: errors.length === 0,
        errors,
      };

    } catch (error) {
      this.logger.error('Error validating routes', error);
      return {
        valid: false,
        errors: [`Validation error: ${error}`],
      };
    }
  }

  // Method to test route connectivity
  public async testRoute(routeId: string): Promise<{ success: boolean; error?: string; responseTime?: number }> {
    try {
      const routes = await this.database.getDomainRoutes() as DomainRoute[];
      const route = routes.find(r => r.id === routeId);

      if (!route) {
        return { success: false, error: 'Route not found' };
      }

      // Simple connectivity test (you might want to implement actual SMTP connection test)
      const startTime = Date.now();
      
      // This is a simplified test - in production, you'd want to test actual SMTP connectivity
      const net = require('net');
      
      return new Promise((resolve) => {
        const socket = net.createConnection({
          host: route.destinationHost,
          port: route.destinationPort,
          timeout: 5000,
        });

        socket.on('connect', () => {
          const responseTime = Date.now() - startTime;
          socket.destroy();
          resolve({ success: true, responseTime });
        });

        socket.on('error', (error) => {
          resolve({ success: false, error: error.message });
        });

        socket.on('timeout', () => {
          socket.destroy();
          resolve({ success: false, error: 'Connection timeout' });
        });
      });

    } catch (error) {
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
    }
  }
}

// Export for Haraka plugin system
module.exports = new DomainRoutingPlugin();
