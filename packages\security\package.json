{"name": "@ironrelay/security", "version": "1.0.0", "description": "IronRelay Security & Protection Layer", "private": true, "scripts": {"build": "tsc", "build:production": "npm run build && npm run obfuscate && npm run package", "obfuscate": "node scripts/obfuscate.js", "package": "node scripts/package-sea.js", "clean": "<PERSON><PERSON><PERSON> dist obfuscated sea-dist", "test": "jest", "lint": "eslint . --ext .ts", "lint:fix": "eslint . --ext .ts --fix"}, "dependencies": {"@ironrelay/shared": "workspace:*", "crypto": "^1.0.1", "node-forge": "^1.3.1", "bcrypt": "^5.1.0", "jsonwebtoken": "^9.0.2", "helmet": "^7.0.0", "express-rate-limit": "^6.8.1", "express-slow-down": "^1.6.0", "hpp": "^0.2.3", "cors": "^2.8.5", "compression": "^1.7.4", "winston": "^3.10.0", "dotenv": "^16.3.1"}, "devDependencies": {"@types/node": "^20.4.5", "@types/bcrypt": "^5.0.0", "@types/jsonwebtoken": "^9.0.2", "@types/compression": "^1.7.2", "@types/cors": "^2.8.13", "@types/hpp": "^0.2.2", "typescript": "^5.1.6", "jest": "^29.6.1", "@types/jest": "^29.5.3", "ts-jest": "^29.1.1", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "rimraf": "^5.0.1", "javascript-obfuscator": "^4.0.2", "webpack": "^5.88.2", "webpack-cli": "^5.1.4", "webpack-obfuscator": "^3.5.1", "terser-webpack-plugin": "^5.3.9"}}