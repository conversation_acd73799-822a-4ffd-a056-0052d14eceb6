# Database Configuration
DATABASE_URL="postgresql://ironrelay:ironrelay@localhost:5432/ironrelay?schema=public"

# JWT Configuration
JWT_SECRET="your-super-secret-jwt-key-change-this-in-production"
JWT_EXPIRES_IN="1h"
JWT_REFRESH_EXPIRES_IN="7d"

# Server Configuration
NODE_ENV="development"
API_PORT=3000
API_HOST="0.0.0.0"

# SMTP Engine Configuration
SMTP_PORT=2525
SMTP_HOST="0.0.0.0"

# Frontend Configuration (for development)
FRONTEND_PORT=3001
FRONTEND_URL="http://localhost:3001"

# Logging Configuration
LOG_LEVEL="info"
LOG_DIR="./logs"

# Redis Configuration (optional, for caching and sessions)
REDIS_URL="redis://localhost:6379"

# TLS Configuration
TLS_CERT_PATH="./certs"
TLS_KEY_PATH="./certs"

# Monitoring Configuration
PROMETHEUS_PORT=9090
GRAFANA_PORT=3000

# Email Notifications (for system alerts)
SMTP_NOTIFICATION_HOST="smtp.gmail.com"
SMTP_NOTIFICATION_PORT=587
SMTP_NOTIFICATION_SECURE=true
SMTP_NOTIFICATION_USER="<EMAIL>"
SMTP_NOTIFICATION_PASS="your-app-password"
SMTP_NOTIFICATION_FROM="IronRelay <<EMAIL>>"

# Security Configuration
BCRYPT_ROUNDS=12
SESSION_SECRET="your-session-secret-change-this-in-production"
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# License Configuration
LICENSE_KEY=""
LICENSE_SERVER_URL="https://license.ironrelay.com"

# Backup Configuration
BACKUP_ENABLED=false
BACKUP_SCHEDULE="0 2 * * *"
BACKUP_RETENTION_DAYS=7
BACKUP_STORAGE_TYPE="local"
BACKUP_LOCAL_PATH="./backups"

# S3 Backup Configuration (if using S3)
AWS_ACCESS_KEY_ID=""
AWS_SECRET_ACCESS_KEY=""
AWS_REGION="us-east-1"
AWS_S3_BUCKET=""

# Development Configuration
ENABLE_SWAGGER=true
ENABLE_CORS=true
CORS_ORIGIN="http://localhost:3001"

# Health Check Configuration
HEALTH_CHECK_TIMEOUT=5000
HEALTH_CHECK_INTERVAL=30000

# Queue Configuration
QUEUE_REDIS_URL="redis://localhost:6379/1"
QUEUE_CONCURRENCY=10
QUEUE_MAX_ATTEMPTS=3
QUEUE_DELAY=5000

# Metrics Configuration
METRICS_ENABLED=true
METRICS_INTERVAL=60000
METRICS_RETENTION_DAYS=30
