module.exports = {
  apps: [
    {
      name: 'ironrelay-api',
      script: './packages/api/dist/index.js',
      instances: 'max',
      exec_mode: 'cluster',
      env: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      env_production: {
        NODE_ENV: 'production',
        PORT: 3000,
      },
      error_file: './logs/api-error.log',
      out_file: './logs/api-out.log',
      log_file: './logs/api-combined.log',
      time: true,
      max_memory_restart: '1G',
      node_args: '--max-old-space-size=1024',
      watch: false,
      ignore_watch: ['node_modules', 'logs'],
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
    },
    {
      name: 'ironrelay-smtp',
      script: './packages/smtp-engine/dist/index.js',
      instances: 1,
      exec_mode: 'fork',
      env: {
        NODE_ENV: 'production',
        SMTP_PORT: 25,
      },
      env_production: {
        NODE_ENV: 'production',
        SMTP_PORT: 25,
      },
      error_file: './logs/smtp-error.log',
      out_file: './logs/smtp-out.log',
      log_file: './logs/smtp-combined.log',
      time: true,
      max_memory_restart: '512M',
      node_args: '--max-old-space-size=512',
      watch: false,
      ignore_watch: ['node_modules', 'logs'],
      restart_delay: 4000,
      max_restarts: 10,
      min_uptime: '10s',
    },
  ],

  deploy: {
    production: {
      user: 'ironrelay',
      host: ['production-server-1', 'production-server-2'],
      ref: 'origin/main',
      repo: '**************:your-org/ironrelay.git',
      path: '/opt/ironrelay',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build:production && pm2 reload ecosystem.config.js --env production',
      'pre-setup': '',
      'ssh_options': 'ForwardAgent=yes',
    },
    staging: {
      user: 'ironrelay',
      host: 'staging-server',
      ref: 'origin/develop',
      repo: '**************:your-org/ironrelay.git',
      path: '/opt/ironrelay-staging',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run build && pm2 reload ecosystem.config.js --env staging',
      'pre-setup': '',
      'ssh_options': 'ForwardAgent=yes',
    },
  },
};
