{"version": 3, "file": "index-excluded.js", "mappings": ";;;;;;;;;AAAA;;;;;;UCAA;UACA;;UAEA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;UACA;;UAEA;UACA;;UAEA;UACA;UACA;;;;;;;;;ACtBA,mBAAO,CAAC,wCAAU;;AAElB;AACA;;AAEA;AACA;AACA;;AAEA;;AAEA;;AAEA,qBAAqB,YAAoB;AACzC;AACA,CAAC,I", "sources": ["webpack://webpack-obfuscator/./test/input/nested.js", "webpack://webpack-obfuscator/webpack/bootstrap", "webpack://webpack-obfuscator/./test/input/index-excluded.js"], "sourcesContent": ["module.exports = 'nested';", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\t// no module.id needed\n\t\t// no module.loaded needed\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId](module, module.exports, __webpack_require__);\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n", "require('./nested');\n\n(function () {\n    var test = 1;\n\n    function abc (cde) {\n        console.log(cde);\n    }\n\n    abc('a');\n\n    console.log(test);\n\n    var processEnv = process.env.SOME_VAR;\n    console.log(processEnv);\n})();"], "names": [], "sourceRoot": ""}