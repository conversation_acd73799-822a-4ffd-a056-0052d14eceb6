{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../src/index.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC;;;AAkHlC,8DAEC;;AAlHD,gEAAsC;AAKtC,uFAAiF;AAEjF,yCAUqB;AATnB,gHAAA,mBAAmB,OAAA;AAEnB,yHAAA,4BAA4B,OAAA;AAC5B,oHAAA,uBAAuB,OAAA;AACvB,6HAAA,gCAAgC,OAAA;AAChC,uHAAA,0BAA0B,OAAA;AAC1B,2HAAA,8BAA8B,OAAA;AAC9B,wHAAA,2BAA2B,OAAA;AAK7B,4CAAiG;AAAxF,yHAAA,6BAA6B,OAAA;AAAE,2HAAA,+BAA+B,OAAA;AAevE,qFAAiF;AAAxE,mIAAA,sBAAsB,OAAA;AAE/B,qFAAiF;AAAxE,mIAAA,sBAAsB,OAAA;AAG/B,qFAAiF;AAAxE,mIAAA,sBAAsB,OAAA;AAO/B,mFAA+E;AAAtE,iIAAA,qBAAqB,OAAA;AAG9B,+FAA2F;AAAlF,6IAAA,2BAA2B,OAAA;AAOpC,2FAAuF;AAA9E,yIAAA,yBAAyB,OAAA;AAGlC,6EAAyE;AAAhE,2HAAA,kBAAkB,OAAA;AAE3B,+FAA2F;AAAlF,6IAAA,2BAA2B,OAAA;AAEpC,iGAA6F;AAApF,+IAAA,4BAA4B,OAAA;AAMrC,6EAA6F;AAApF,qHAAA,yBAAyB,OAAA;AAMlC,iFAA6E;AAApE,+HAAA,oBAAoB,OAAA;AAM7B,yFAAiH;AAAxG,uIAAA,wBAAwB,OAA4B;AAE7D,+FAA2F;AAAlF,6IAAA,2BAA2B,OAAA;AAEpC,2FAAuF;AAA9E,yIAAA,yBAAyB,OAAA;AAQlC,6FAAyF;AAAhF,2IAAA,0BAA0B,OAAA;AAEnC,6FAAyF;AAAhF,2IAAA,0BAA0B,OAAA;AAEnC,iFAA6E;AAApE,+HAAA,oBAAoB,OAAA;AAC7B,6FAAyF;AAAhF,2IAAA,0BAA0B,OAAA;AAMnC,gDAA2C;AAAlC,oGAAA,MAAM,OAAA;AAEf,+CAAqD;AAA5C,mHAAA,mBAAmB,OAAA;AAE5B;;GAEG;AACH,SAAgB,yBAAyB;IACvC,OAAO,IAAI,kDAAsB,EAAE,CAAC;AACtC,CAAC;AAED,uDAA2F;AAAlF,0HAAA,sBAAsB,OAAA", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nexport * from \"./plugins/consumer.js\";\n\nexport { IdentityPlugin } from \"./plugins/provider.js\";\n\nimport type { TokenCredential } from \"@azure/core-auth\";\nimport { DefaultAzureCredential } from \"./credentials/defaultAzureCredential.js\";\n\nexport {\n  AuthenticationError,\n  ErrorResponse,\n  AggregateAuthenticationError,\n  AuthenticationErrorName,\n  AggregateAuthenticationErrorName,\n  CredentialUnavailableError,\n  CredentialUnavailableErrorName,\n  AuthenticationRequiredError,\n  AuthenticationRequiredErrorOptions,\n} from \"./errors.js\";\n\nexport { AuthenticationRecord } from \"./msal/types.js\";\nexport { serializeAuthenticationRecord, deserializeAuthenticationRecord } from \"./msal/utils.js\";\nexport { TokenCredentialOptions } from \"./tokenCredentialOptions.js\";\nexport { MultiTenantTokenCredentialOptions } from \"./credentials/multiTenantTokenCredentialOptions.js\";\nexport { AuthorityValidationOptions } from \"./credentials/authorityValidationOptions.js\";\n// TODO: Export again once we're ready to release this feature.\n// export { RegionalAuthority } from \"./regionalAuthority\";\n\nexport { BrokerAuthOptions } from \"./credentials/brokerAuthOptions.js\";\nexport {\n  BrokerOptions,\n  BrokerEnabledOptions,\n  BrokerDisabledOptions,\n} from \"./msal/nodeFlows/brokerOptions.js\";\nexport { InteractiveCredentialOptions } from \"./credentials/interactiveCredentialOptions.js\";\n\nexport { ChainedTokenCredential } from \"./credentials/chainedTokenCredential.js\";\n\nexport { ClientSecretCredential } from \"./credentials/clientSecretCredential.js\";\nexport { ClientSecretCredentialOptions } from \"./credentials/clientSecretCredentialOptions.js\";\n\nexport { DefaultAzureCredential } from \"./credentials/defaultAzureCredential.js\";\nexport {\n  DefaultAzureCredentialOptions,\n  DefaultAzureCredentialClientIdOptions,\n  DefaultAzureCredentialResourceIdOptions,\n} from \"./credentials/defaultAzureCredentialOptions.js\";\n\nexport { EnvironmentCredential } from \"./credentials/environmentCredential.js\";\nexport { EnvironmentCredentialOptions } from \"./credentials/environmentCredentialOptions.js\";\n\nexport { ClientCertificateCredential } from \"./credentials/clientCertificateCredential.js\";\nexport {\n  ClientCertificateCredentialPEMConfiguration,\n  ClientCertificatePEMCertificatePath,\n  ClientCertificatePEMCertificate,\n} from \"./credentials/clientCertificateCredentialModels.js\";\nexport { ClientCertificateCredentialOptions } from \"./credentials/clientCertificateCredentialOptions.js\";\nexport { ClientAssertionCredential } from \"./credentials/clientAssertionCredential.js\";\nexport { ClientAssertionCredentialOptions } from \"./credentials/clientAssertionCredentialOptions.js\";\nexport { CredentialPersistenceOptions } from \"./credentials/credentialPersistenceOptions.js\";\nexport { AzureCliCredential } from \"./credentials/azureCliCredential.js\";\nexport { AzureCliCredentialOptions } from \"./credentials/azureCliCredentialOptions.js\";\nexport { AzureDeveloperCliCredential } from \"./credentials/azureDeveloperCliCredential.js\";\nexport { AzureDeveloperCliCredentialOptions } from \"./credentials/azureDeveloperCliCredentialOptions.js\";\nexport { InteractiveBrowserCredential } from \"./credentials/interactiveBrowserCredential.js\";\nexport {\n  InteractiveBrowserCredentialNodeOptions,\n  InteractiveBrowserCredentialInBrowserOptions,\n  BrowserLoginStyle,\n} from \"./credentials/interactiveBrowserCredentialOptions.js\";\nexport { ManagedIdentityCredential } from \"./credentials/managedIdentityCredential/index.js\";\nexport {\n  ManagedIdentityCredentialClientIdOptions,\n  ManagedIdentityCredentialResourceIdOptions,\n  ManagedIdentityCredentialObjectIdOptions,\n} from \"./credentials/managedIdentityCredential/options.js\";\nexport { DeviceCodeCredential } from \"./credentials/deviceCodeCredential.js\";\nexport {\n  DeviceCodePromptCallback,\n  DeviceCodeInfo,\n} from \"./credentials/deviceCodeCredentialOptions.js\";\nexport { DeviceCodeCredentialOptions } from \"./credentials/deviceCodeCredentialOptions.js\";\nexport { AzurePipelinesCredential as AzurePipelinesCredential } from \"./credentials/azurePipelinesCredential.js\";\nexport { AzurePipelinesCredentialOptions as AzurePipelinesCredentialOptions } from \"./credentials/azurePipelinesCredentialOptions.js\";\nexport { AuthorizationCodeCredential } from \"./credentials/authorizationCodeCredential.js\";\nexport { AuthorizationCodeCredentialOptions } from \"./credentials/authorizationCodeCredentialOptions.js\";\nexport { AzurePowerShellCredential } from \"./credentials/azurePowerShellCredential.js\";\nexport { AzurePowerShellCredentialOptions } from \"./credentials/azurePowerShellCredentialOptions.js\";\nexport {\n  OnBehalfOfCredentialOptions,\n  OnBehalfOfCredentialSecretOptions,\n  OnBehalfOfCredentialCertificateOptions,\n  OnBehalfOfCredentialAssertionOptions,\n} from \"./credentials/onBehalfOfCredentialOptions.js\";\nexport { UsernamePasswordCredential } from \"./credentials/usernamePasswordCredential.js\";\nexport { UsernamePasswordCredentialOptions } from \"./credentials/usernamePasswordCredentialOptions.js\";\nexport { VisualStudioCodeCredential } from \"./credentials/visualStudioCodeCredential.js\";\nexport { VisualStudioCodeCredentialOptions } from \"./credentials/visualStudioCodeCredentialOptions.js\";\nexport { OnBehalfOfCredential } from \"./credentials/onBehalfOfCredential.js\";\nexport { WorkloadIdentityCredential } from \"./credentials/workloadIdentityCredential.js\";\nexport { WorkloadIdentityCredentialOptions } from \"./credentials/workloadIdentityCredentialOptions.js\";\nexport { BrowserCustomizationOptions } from \"./credentials/browserCustomizationOptions.js\";\nexport { TokenCachePersistenceOptions } from \"./msal/nodeFlows/tokenCachePersistenceOptions.js\";\n\nexport { TokenCredential, GetTokenOptions, AccessToken } from \"@azure/core-auth\";\nexport { logger } from \"./util/logging.js\";\n\nexport { AzureAuthorityHosts } from \"./constants.js\";\n\n/**\n * Returns a new instance of the {@link DefaultAzureCredential}.\n */\nexport function getDefaultAzureCredential(): TokenCredential {\n  return new DefaultAzureCredential();\n}\n\nexport { getBearerTokenProvider, GetBearerTokenProviderOptions } from \"./tokenProvider.js\";\n"]}