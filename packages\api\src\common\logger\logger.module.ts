import { Global, Module } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { Logger } from './logger.service';

@Global()
@Module({
  providers: [
    {
      provide: Logger,
      useFactory: (configService: ConfigService) => {
        return new Logger('App', configService);
      },
      inject: [ConfigService],
    },
  ],
  exports: [Logger],
})
export class LoggerModule {}
