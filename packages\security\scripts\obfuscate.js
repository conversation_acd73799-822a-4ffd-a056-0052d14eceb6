#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const JavaScriptObfuscator = require('javascript-obfuscator');

/**
 * IronRelay Code Obfuscation Script
 * 
 * This script obfuscates the compiled JavaScript code to protect against
 * reverse engineering and unauthorized modifications.
 * 
 * Features:
 * - Multiple obfuscation levels
 * - Control flow flattening
 * - String array encoding
 * - Dead code injection
 * - Anti-debugging protection
 * - Domain locking
 * - Self-defending code
 */

const OBFUSCATION_LEVELS = {
  LOW: {
    compact: true,
    controlFlowFlattening: false,
    deadCodeInjection: false,
    debugProtection: false,
    disableConsoleOutput: false,
    identifierNamesGenerator: 'hexadecimal',
    log: false,
    numbersToExpressions: false,
    renameGlobals: false,
    selfDefending: false,
    simplify: true,
    splitStrings: false,
    stringArray: true,
    stringArrayCallsTransform: false,
    stringArrayEncoding: [],
    stringArrayIndexShift: true,
    stringArrayRotate: true,
    stringArrayShuffle: true,
    stringArrayWrappersCount: 1,
    stringArrayWrappersChainedCalls: true,
    stringArrayWrappersParametersMaxCount: 2,
    stringArrayWrappersType: 'variable',
    stringArrayThreshold: 0.75,
    unicodeEscapeSequence: false
  },
  
  MEDIUM: {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 0.75,
    deadCodeInjection: true,
    deadCodeInjectionThreshold: 0.4,
    debugProtection: true,
    debugProtectionInterval: 2000,
    disableConsoleOutput: true,
    identifierNamesGenerator: 'hexadecimal',
    log: false,
    numbersToExpressions: true,
    numbersToExpressionsThreshold: 0.5,
    renameGlobals: false,
    selfDefending: true,
    simplify: true,
    splitStrings: true,
    splitStringsChunkLength: 5,
    stringArray: true,
    stringArrayCallsTransform: true,
    stringArrayCallsTransformThreshold: 0.5,
    stringArrayEncoding: ['base64'],
    stringArrayIndexShift: true,
    stringArrayRotate: true,
    stringArrayShuffle: true,
    stringArrayWrappersCount: 2,
    stringArrayWrappersChainedCalls: true,
    stringArrayWrappersParametersMaxCount: 4,
    stringArrayWrappersType: 'function',
    stringArrayThreshold: 0.75,
    transformObjectKeys: true,
    unicodeEscapeSequence: false
  },
  
  HIGH: {
    compact: true,
    controlFlowFlattening: true,
    controlFlowFlatteningThreshold: 1,
    deadCodeInjection: true,
    deadCodeInjectionThreshold: 0.6,
    debugProtection: true,
    debugProtectionInterval: 1000,
    disableConsoleOutput: true,
    identifierNamesGenerator: 'mangled-shuffled',
    log: false,
    numbersToExpressions: true,
    numbersToExpressionsThreshold: 0.9,
    renameGlobals: true,
    selfDefending: true,
    simplify: true,
    splitStrings: true,
    splitStringsChunkLength: 3,
    stringArray: true,
    stringArrayCallsTransform: true,
    stringArrayCallsTransformThreshold: 0.9,
    stringArrayEncoding: ['base64', 'rc4'],
    stringArrayIndexShift: true,
    stringArrayRotate: true,
    stringArrayShuffle: true,
    stringArrayWrappersCount: 5,
    stringArrayWrappersChainedCalls: true,
    stringArrayWrappersParametersMaxCount: 5,
    stringArrayWrappersType: 'function',
    stringArrayThreshold: 0.9,
    transformObjectKeys: true,
    unicodeEscapeSequence: true
  }
};

class CodeObfuscator {
  constructor(options = {}) {
    this.inputDir = options.inputDir || './dist';
    this.outputDir = options.outputDir || './obfuscated';
    this.level = options.level || 'MEDIUM';
    this.domainLock = options.domainLock || [];
    this.excludeFiles = options.excludeFiles || [];
    this.preserveFiles = options.preserveFiles || ['package.json', '*.d.ts'];
    this.verbose = options.verbose || false;
  }

  async obfuscate() {
    console.log('🔒 Starting code obfuscation...');
    console.log(`📁 Input directory: ${this.inputDir}`);
    console.log(`📁 Output directory: ${this.outputDir}`);
    console.log(`🎯 Obfuscation level: ${this.level}`);

    if (!fs.existsSync(this.inputDir)) {
      throw new Error(`Input directory does not exist: ${this.inputDir}`);
    }

    // Create output directory
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }

    // Get obfuscation options
    const obfuscationOptions = this.getObfuscationOptions();

    // Process files
    const stats = await this.processDirectory(this.inputDir, this.outputDir, obfuscationOptions);

    console.log('\n✅ Obfuscation completed!');
    console.log(`📊 Files processed: ${stats.processed}`);
    console.log(`📊 Files obfuscated: ${stats.obfuscated}`);
    console.log(`📊 Files copied: ${stats.copied}`);
    console.log(`📊 Files skipped: ${stats.skipped}`);

    return stats;
  }

  getObfuscationOptions() {
    const baseOptions = OBFUSCATION_LEVELS[this.level] || OBFUSCATION_LEVELS.MEDIUM;
    
    return {
      ...baseOptions,
      domainLock: this.domainLock,
      target: 'node',
      seed: Math.floor(Math.random() * 1000000),
    };
  }

  async processDirectory(inputDir, outputDir, options, stats = { processed: 0, obfuscated: 0, copied: 0, skipped: 0 }) {
    const items = fs.readdirSync(inputDir);

    for (const item of items) {
      const inputPath = path.join(inputDir, item);
      const outputPath = path.join(outputDir, item);
      const stat = fs.statSync(inputPath);

      if (stat.isDirectory()) {
        // Create directory and process recursively
        if (!fs.existsSync(outputPath)) {
          fs.mkdirSync(outputPath, { recursive: true });
        }
        await this.processDirectory(inputPath, outputPath, options, stats);
      } else {
        await this.processFile(inputPath, outputPath, options, stats);
      }
    }

    return stats;
  }

  async processFile(inputPath, outputPath, options, stats) {
    const fileName = path.basename(inputPath);
    const ext = path.extname(inputPath);
    
    stats.processed++;

    // Check if file should be excluded
    if (this.shouldExcludeFile(fileName)) {
      if (this.verbose) {
        console.log(`⏭️  Skipping: ${fileName}`);
      }
      stats.skipped++;
      return;
    }

    // Check if file should be preserved (copied without obfuscation)
    if (this.shouldPreserveFile(fileName)) {
      fs.copyFileSync(inputPath, outputPath);
      if (this.verbose) {
        console.log(`📋 Copied: ${fileName}`);
      }
      stats.copied++;
      return;
    }

    // Obfuscate JavaScript files
    if (ext === '.js') {
      try {
        const code = fs.readFileSync(inputPath, 'utf8');
        const obfuscatedCode = this.obfuscateCode(code, options);
        
        fs.writeFileSync(outputPath, obfuscatedCode);
        
        if (this.verbose) {
          console.log(`🔒 Obfuscated: ${fileName}`);
        }
        stats.obfuscated++;
      } catch (error) {
        console.error(`❌ Failed to obfuscate ${fileName}:`, error.message);
        // Copy original file as fallback
        fs.copyFileSync(inputPath, outputPath);
        stats.copied++;
      }
    } else {
      // Copy non-JavaScript files
      fs.copyFileSync(inputPath, outputPath);
      if (this.verbose) {
        console.log(`📋 Copied: ${fileName}`);
      }
      stats.copied++;
    }
  }

  obfuscateCode(code, options) {
    // Add custom protection headers
    const protectionHeader = this.generateProtectionHeader();
    const protectedCode = protectionHeader + '\n' + code;

    // Obfuscate the code
    const obfuscationResult = JavaScriptObfuscator.obfuscate(protectedCode, options);
    
    return obfuscationResult.getObfuscatedCode();
  }

  generateProtectionHeader() {
    const timestamp = new Date().toISOString();
    const checksum = this.generateChecksum();
    
    return `
// IronRelay Protected Code - ${timestamp}
// Unauthorized modification, reverse engineering, or distribution is prohibited
// Checksum: ${checksum}
(function() {
  'use strict';
  
  // Anti-debugging protection
  var _0x1a2b = function() {
    var _0x3c4d = function() {
      return 'dev';
    };
    var _0x5e6f = function() {
      return 'window';
    };
    var _0x7890 = function() {
      var _0xabcd = new RegExp('\\\\w+ *\\\\(\\\\) *{\\\\w+ *[\\\'|\\"].+[\\\'|\\"];? *}');
      return !_0xabcd.test(_0x3c4d.toString());
    };
    var _0xef12 = function() {
      var _0x3456 = new RegExp('(\\\\\\\\[x|u](\\\\w){2,4})+');
      return _0x3456.test(_0x5e6f.toString());
    };
    var _0x7890ab = function(_0xcd) {
      var _0xef = ~-1 >> 1 + 255 % 0;
      if (_0xcd.indexOf('i' === _0xef)) {
        _0x123456(_0xcd);
      }
    };
    var _0x123456 = function(_0x789) {
      var _0xabc = ~-4 >> 1 + 255 % 0;
      if (_0x789.indexOf((!![] + '')[3]) !== _0xabc) {
        _0x7890ab(_0x789);
      }
    };
    if (!_0x7890()) {
      if (!_0xef12()) {
        _0x7890ab('indеxOf');
      } else {
        _0x7890ab('indexOf');
      }
    } else {
      _0x7890ab('indеxOf');
    }
  };
  
  // Runtime integrity check
  var _0xintegrity = function() {
    var _0xstart = Date.now();
    setTimeout(function() {
      if (Date.now() - _0xstart > 100) {
        throw new Error('Runtime integrity violation detected');
      }
    }, 50);
  };
  
  // Initialize protection
  _0x1a2b();
  _0xintegrity();
  
  // Environment validation
  if (typeof process !== 'undefined' && process.env.NODE_ENV === 'development') {
    // Allow development mode
  } else if (typeof window !== 'undefined') {
    throw new Error('This code is not intended for browser execution');
  }
})();
    `.trim();
  }

  generateChecksum() {
    const crypto = require('crypto');
    const data = Date.now().toString() + Math.random().toString();
    return crypto.createHash('sha256').update(data).digest('hex').substring(0, 16);
  }

  shouldExcludeFile(fileName) {
    return this.excludeFiles.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(fileName);
      }
      return fileName === pattern;
    });
  }

  shouldPreserveFile(fileName) {
    return this.preserveFiles.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(fileName);
      }
      return fileName === pattern;
    });
  }
}

// CLI execution
if (require.main === module) {
  const args = process.argv.slice(2);
  const options = {};

  // Parse command line arguments
  for (let i = 0; i < args.length; i += 2) {
    const key = args[i].replace(/^--/, '');
    const value = args[i + 1];
    
    switch (key) {
      case 'input':
        options.inputDir = value;
        break;
      case 'output':
        options.outputDir = value;
        break;
      case 'level':
        options.level = value.toUpperCase();
        break;
      case 'domain-lock':
        options.domainLock = value.split(',');
        break;
      case 'exclude':
        options.excludeFiles = value.split(',');
        break;
      case 'verbose':
        options.verbose = true;
        i--; // No value for this flag
        break;
    }
  }

  const obfuscator = new CodeObfuscator(options);
  
  obfuscator.obfuscate()
    .then(stats => {
      console.log('\n🎉 Obfuscation process completed successfully!');
      process.exit(0);
    })
    .catch(error => {
      console.error('\n❌ Obfuscation failed:', error.message);
      if (options.verbose) {
        console.error(error.stack);
      }
      process.exit(1);
    });
}

module.exports = CodeObfuscator;
