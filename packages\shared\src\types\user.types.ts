import { z } from 'zod';
import { UUI<PERSON>, <PERSON>tamp, <PERSON><PERSON>Address, IPAddress, BaseConfig } from './common.types';

// User roles and permissions
export enum UserRole {
  SUPER_ADMIN = 'SUPER_ADMIN',
  ADMIN = 'ADMIN',
  OPERATOR = 'OPERATOR',
  VIEWER = 'VIEWER',
}

export enum Permission {
  // System permissions
  SYSTEM_ADMIN = 'SYSTEM_ADMIN',
  SYSTEM_CONFIG = 'SYSTEM_CONFIG',
  SYSTEM_MONITOR = 'SYSTEM_MONITOR',
  
  // SMTP permissions
  SMTP_CONFIG = 'SMTP_CONFIG',
  SMTP_USERS = 'SMTP_USERS',
  SMTP_ROUTES = 'SMTP_ROUTES',
  SMTP_MONITOR = 'SMTP_MONITOR',
  SMTP_LOGS = 'SMTP_LOGS',
  
  // User management permissions
  USER_CREATE = 'USER_CREATE',
  USER_UPDATE = 'USER_UPDATE',
  USER_DELETE = 'USER_DELETE',
  USER_VIEW = 'USER_VIEW',
  
  // Certificate permissions
  CERT_UPLOAD = 'CERT_UPLOAD',
  CERT_MANAGE = 'CERT_MANAGE',
  CERT_VIEW = 'CERT_VIEW',
  
  // Audit permissions
  AUDIT_VIEW = 'AUDIT_VIEW',
  AUDIT_EXPORT = 'AUDIT_EXPORT',
  
  // API permissions
  API_ACCESS = 'API_ACCESS',
  API_ADMIN = 'API_ADMIN',
}

// Role-based permission mapping
export const ROLE_PERMISSIONS: Record<UserRole, Permission[]> = {
  [UserRole.SUPER_ADMIN]: Object.values(Permission),
  [UserRole.ADMIN]: [
    Permission.SYSTEM_CONFIG,
    Permission.SYSTEM_MONITOR,
    Permission.SMTP_CONFIG,
    Permission.SMTP_USERS,
    Permission.SMTP_ROUTES,
    Permission.SMTP_MONITOR,
    Permission.SMTP_LOGS,
    Permission.USER_CREATE,
    Permission.USER_UPDATE,
    Permission.USER_VIEW,
    Permission.CERT_UPLOAD,
    Permission.CERT_MANAGE,
    Permission.CERT_VIEW,
    Permission.AUDIT_VIEW,
    Permission.AUDIT_EXPORT,
    Permission.API_ACCESS,
  ],
  [UserRole.OPERATOR]: [
    Permission.SYSTEM_MONITOR,
    Permission.SMTP_CONFIG,
    Permission.SMTP_USERS,
    Permission.SMTP_ROUTES,
    Permission.SMTP_MONITOR,
    Permission.SMTP_LOGS,
    Permission.USER_VIEW,
    Permission.CERT_VIEW,
    Permission.AUDIT_VIEW,
    Permission.API_ACCESS,
  ],
  [UserRole.VIEWER]: [
    Permission.SYSTEM_MONITOR,
    Permission.SMTP_MONITOR,
    Permission.SMTP_LOGS,
    Permission.USER_VIEW,
    Permission.CERT_VIEW,
    Permission.AUDIT_VIEW,
  ],
};

// User entity
export interface User extends BaseConfig {
  email: EmailAddress;
  firstName: string;
  lastName: string;
  passwordHash: string;
  role: UserRole;
  isActive: boolean;
  emailVerified: boolean;
  
  // Security
  mfaEnabled: boolean;
  mfaSecret?: string;
  
  // Session management
  lastLogin?: Timestamp;
  lastLoginIP?: IPAddress;
  loginCount: number;
  failedLoginAttempts: number;
  lockedUntil?: Timestamp;
  
  // Password management
  passwordChangedAt: Timestamp;
  passwordResetToken?: string;
  passwordResetExpires?: Timestamp;
  
  // Email verification
  emailVerificationToken?: string;
  emailVerificationExpires?: Timestamp;
  
  // Preferences
  timezone: string;
  language: string;
  theme: 'light' | 'dark' | 'auto';
  
  // Notifications
  emailNotifications: boolean;
  securityAlerts: boolean;
}

// API Key for programmatic access
export interface ApiKey extends BaseConfig {
  keyId: string;
  keyHash: string;
  userId: UUID;
  
  // Permissions
  permissions: Permission[];
  
  // Usage tracking
  lastUsed?: Timestamp;
  usageCount: number;
  
  // Restrictions
  ipWhitelist?: IPAddress[];
  expiresAt?: Timestamp;
  
  // Rate limiting
  rateLimitPerHour?: number;
  rateLimitPerDay?: number;
}

// User session
export interface UserSession {
  id: UUID;
  userId: UUID;
  sessionToken: string;
  
  // Session details
  ipAddress: IPAddress;
  userAgent: string;
  
  // Timestamps
  createdAt: Timestamp;
  lastAccessedAt: Timestamp;
  expiresAt: Timestamp;
  
  // Security
  isActive: boolean;
  revokedAt?: Timestamp;
  revokedBy?: UUID;
  revokedReason?: string;
}

// Login attempt tracking
export interface LoginAttempt {
  id: UUID;
  email: EmailAddress;
  ipAddress: IPAddress;
  userAgent: string;
  
  // Attempt details
  success: boolean;
  failureReason?: string;
  
  // Timestamps
  attemptedAt: Timestamp;
  
  // Security
  blocked: boolean;
  blockReason?: string;
}

// User preferences
export interface UserPreferences {
  userId: UUID;
  
  // Dashboard preferences
  dashboardLayout: Record<string, unknown>;
  defaultPageSize: number;
  defaultSortOrder: 'asc' | 'desc';
  
  // Notification preferences
  emailDigestFrequency: 'never' | 'daily' | 'weekly' | 'monthly';
  alertThresholds: {
    errorRate: number;
    queueDepth: number;
    connectionFailures: number;
  };
  
  // Display preferences
  dateFormat: string;
  timeFormat: '12h' | '24h';
  numberFormat: string;
  
  // Advanced preferences
  logRetentionDays: number;
  autoRefreshInterval: number; // seconds
  
  updatedAt: Timestamp;
}

// Password policy
export interface PasswordPolicy {
  minLength: number;
  maxLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  preventReuse: number; // number of previous passwords to check
  maxAge: number; // days before password expires
  lockoutThreshold: number; // failed attempts before lockout
  lockoutDuration: number; // minutes
}

// MFA configuration
export interface MFAConfig {
  enabled: boolean;
  required: boolean;
  methods: MFAMethod[];
  backupCodes: boolean;
  gracePeriod: number; // days
}

export enum MFAMethod {
  TOTP = 'TOTP', // Time-based One-Time Password (Google Authenticator, etc.)
  SMS = 'SMS',
  EMAIL = 'EMAIL',
  BACKUP_CODES = 'BACKUP_CODES',
}

// User invitation
export interface UserInvitation {
  id: UUID;
  email: EmailAddress;
  role: UserRole;
  
  // Invitation details
  invitedBy: UUID;
  invitedAt: Timestamp;
  expiresAt: Timestamp;
  
  // Status
  status: InvitationStatus;
  acceptedAt?: Timestamp;
  
  // Token
  invitationToken: string;
  
  // Optional message
  message?: string;
}

export enum InvitationStatus {
  PENDING = 'PENDING',
  ACCEPTED = 'ACCEPTED',
  EXPIRED = 'EXPIRED',
  REVOKED = 'REVOKED',
}

// Validation schemas
export const UserCreateSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1).max(50),
  lastName: z.string().min(1).max(50),
  password: z.string().min(8).max(128),
  role: z.nativeEnum(UserRole),
  timezone: z.string().default('UTC'),
  language: z.string().default('en'),
});

export const UserUpdateSchema = z.object({
  firstName: z.string().min(1).max(50).optional(),
  lastName: z.string().min(1).max(50).optional(),
  role: z.nativeEnum(UserRole).optional(),
  isActive: z.boolean().optional(),
  timezone: z.string().optional(),
  language: z.string().optional(),
  theme: z.enum(['light', 'dark', 'auto']).optional(),
  emailNotifications: z.boolean().optional(),
  securityAlerts: z.boolean().optional(),
});

export const PasswordChangeSchema = z.object({
  currentPassword: z.string(),
  newPassword: z.string().min(8).max(128),
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const ApiKeyCreateSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  permissions: z.array(z.nativeEnum(Permission)),
  expiresAt: z.date().optional(),
  ipWhitelist: z.array(z.string().ip()).optional(),
  rateLimitPerHour: z.number().int().min(1).optional(),
  rateLimitPerDay: z.number().int().min(1).optional(),
});

export const LoginSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
  mfaCode: z.string().length(6).optional(),
  rememberMe: z.boolean().default(false),
});

export const InviteUserSchema = z.object({
  email: z.string().email(),
  role: z.nativeEnum(UserRole),
  message: z.string().max(500).optional(),
});
