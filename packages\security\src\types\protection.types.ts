export interface ProtectionConfig {
  enableCodeObfuscation: boolean;
  enableIntegrityChecks: boolean;
  enableAntiTamper: boolean;
  enableAntiDebug: boolean;
  enableRuntimeProtection: boolean;
  obfuscationLevel: ObfuscationLevel;
  integrityCheckInterval: number;
  tamperDetectionSensitivity: TamperSensitivity;
  protectionKeys: string[];
  allowedEnvironments: string[];
  debugModeAllowed: boolean;
  developmentMode: boolean;
}

export enum ObfuscationLevel {
  NONE = 'NONE',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  MAXIMUM = 'MAXIMUM',
}

export enum TamperSensitivity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  PARANOID = 'PARANOID',
}

export interface ObfuscationOptions {
  compact: boolean;
  controlFlowFlattening: boolean;
  controlFlowFlatteningThreshold: number;
  deadCodeInjection: boolean;
  deadCodeInjectionThreshold: number;
  debugProtection: boolean;
  debugProtectionInterval: number;
  disableConsoleOutput: boolean;
  domainLock: string[];
  identifierNamesGenerator: IdentifierNamesGenerator;
  identifiersPrefix: string;
  inputFileName: string;
  log: boolean;
  numbersToExpressions: boolean;
  numbersToExpressionsThreshold: number;
  optionsPreset: OptionsPreset;
  renameGlobals: boolean;
  renameProperties: boolean;
  renamePropertiesMode: RenamePropertiesMode;
  reservedNames: string[];
  reservedStrings: string[];
  seed: number;
  selfDefending: boolean;
  simplify: boolean;
  sourceMap: boolean;
  sourceMapBaseUrl: string;
  sourceMapFileName: string;
  sourceMapMode: SourceMapMode;
  splitStrings: boolean;
  splitStringsChunkLength: number;
  stringArray: boolean;
  stringArrayCallsTransform: boolean;
  stringArrayCallsTransformThreshold: number;
  stringArrayEncoding: StringArrayEncoding[];
  stringArrayIndexShift: boolean;
  stringArrayRotate: boolean;
  stringArrayShuffle: boolean;
  stringArrayWrappersCount: number;
  stringArrayWrappersChainedCalls: boolean;
  stringArrayWrappersParametersMaxCount: number;
  stringArrayWrappersType: StringArrayWrappersType;
  stringArrayThreshold: number;
  target: Target;
  transformObjectKeys: boolean;
  unicodeEscapeSequence: boolean;
}

export enum IdentifierNamesGenerator {
  DICTIONARY = 'dictionary',
  HEXADECIMAL = 'hexadecimal',
  MANGLED = 'mangled',
  MANGLED_SHUFFLED = 'mangled-shuffled',
}

export enum OptionsPreset {
  DEFAULT = 'default',
  LOW_OBFUSCATION = 'low-obfuscation',
  MEDIUM_OBFUSCATION = 'medium-obfuscation',
  HIGH_OBFUSCATION = 'high-obfuscation',
}

export enum RenamePropertiesMode {
  SAFE = 'safe',
  UNSAFE = 'unsafe',
}

export enum SourceMapMode {
  INLINE = 'inline',
  SEPARATE = 'separate',
}

export enum StringArrayEncoding {
  NONE = 'none',
  BASE64 = 'base64',
  RC4 = 'rc4',
}

export enum StringArrayWrappersType {
  VARIABLE = 'variable',
  FUNCTION = 'function',
}

export enum Target {
  BROWSER = 'browser',
  BROWSER_NO_EVAL = 'browser-no-eval',
  NODE = 'node',
}

export interface IntegrityCheck {
  id: string;
  type: IntegrityCheckType;
  target: string;
  expectedHash: string;
  actualHash?: string;
  status: IntegrityStatus;
  lastChecked: Date;
  checkCount: number;
  failureCount: number;
}

export enum IntegrityCheckType {
  FILE_HASH = 'FILE_HASH',
  MEMORY_HASH = 'MEMORY_HASH',
  CODE_SIGNATURE = 'CODE_SIGNATURE',
  RUNTIME_CHECKSUM = 'RUNTIME_CHECKSUM',
  CUSTOM = 'CUSTOM',
}

export enum IntegrityStatus {
  VALID = 'VALID',
  INVALID = 'INVALID',
  UNKNOWN = 'UNKNOWN',
  ERROR = 'ERROR',
}

export interface TamperDetection {
  id: string;
  type: TamperDetectionType;
  description: string;
  severity: TamperSeverity;
  detected: boolean;
  detectedAt?: Date;
  details?: Record<string, any>;
}

export enum TamperDetectionType {
  DEBUGGER_ATTACHED = 'DEBUGGER_ATTACHED',
  BREAKPOINT_DETECTED = 'BREAKPOINT_DETECTED',
  CODE_MODIFICATION = 'CODE_MODIFICATION',
  MEMORY_MODIFICATION = 'MEMORY_MODIFICATION',
  PROCESS_INJECTION = 'PROCESS_INJECTION',
  HOOK_DETECTION = 'HOOK_DETECTION',
  VIRTUAL_MACHINE = 'VIRTUAL_MACHINE',
  SANDBOX_DETECTION = 'SANDBOX_DETECTION',
  ANALYSIS_TOOL = 'ANALYSIS_TOOL',
  CUSTOM = 'CUSTOM',
}

export enum TamperSeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface ProtectionEvent {
  id: string;
  type: ProtectionEventType;
  severity: ProtectionSeverity;
  timestamp: Date;
  source: string;
  description: string;
  metadata?: Record<string, any>;
  action?: ProtectionAction;
}

export enum ProtectionEventType {
  OBFUSCATION_APPLIED = 'OBFUSCATION_APPLIED',
  OBFUSCATION_BYPASSED = 'OBFUSCATION_BYPASSED',
  INTEGRITY_CHECK_PASSED = 'INTEGRITY_CHECK_PASSED',
  INTEGRITY_CHECK_FAILED = 'INTEGRITY_CHECK_FAILED',
  TAMPER_DETECTED = 'TAMPER_DETECTED',
  ANTI_DEBUG_TRIGGERED = 'ANTI_DEBUG_TRIGGERED',
  PROTECTION_DISABLED = 'PROTECTION_DISABLED',
  PROTECTION_ENABLED = 'PROTECTION_ENABLED',
  CUSTOM = 'CUSTOM',
}

export enum ProtectionSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  ERROR = 'ERROR',
  CRITICAL = 'CRITICAL',
}

export interface ProtectionAction {
  type: ProtectionActionType;
  parameters?: Record<string, any>;
  executed: boolean;
  executedAt?: Date;
  result?: string;
}

export enum ProtectionActionType {
  LOG_EVENT = 'LOG_EVENT',
  ALERT_ADMIN = 'ALERT_ADMIN',
  BLOCK_EXECUTION = 'BLOCK_EXECUTION',
  TERMINATE_PROCESS = 'TERMINATE_PROCESS',
  DISABLE_FEATURE = 'DISABLE_FEATURE',
  QUARANTINE = 'QUARANTINE',
  CUSTOM = 'CUSTOM',
}

export interface ProtectionMetrics {
  obfuscationLevel: ObfuscationLevel;
  integrityChecks: number;
  integrityFailures: number;
  tamperAttempts: number;
  debuggerDetections: number;
  protectionEvents: number;
  uptime: number;
  lastCheck: Date;
}

export interface SEAConfig {
  entryPoint: string;
  outputPath: string;
  nodeVersion: string;
  platform: string;
  architecture: string;
  enableCompression: boolean;
  enableEncryption: boolean;
  encryptionKey?: string;
  includeNativeModules: boolean;
  nativeModules: string[];
  excludeModules: string[];
  minifyCode: boolean;
  obfuscateCode: boolean;
  signExecutable: boolean;
  certificatePath?: string;
  certificatePassword?: string;
  metadata: SEAMetadata;
}

export interface SEAMetadata {
  name: string;
  version: string;
  description: string;
  author: string;
  license: string;
  homepage?: string;
  repository?: string;
  buildDate: Date;
  nodeVersion: string;
  platform: string;
  architecture: string;
}

export interface SEABuildResult {
  success: boolean;
  outputPath?: string;
  size?: number;
  buildTime: number;
  warnings: string[];
  errors: string[];
  metadata: SEAMetadata;
}
