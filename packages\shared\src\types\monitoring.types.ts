import { UUID, Timestamp } from './common.types';

// Real-time metrics
export interface SystemMetrics {
  timestamp: Timestamp;
  
  // System resources
  cpu: {
    usage: number; // percentage
    loadAverage: number[];
    cores: number;
  };
  
  memory: {
    total: number; // bytes
    used: number; // bytes
    free: number; // bytes
    usage: number; // percentage
  };
  
  disk: {
    total: number; // bytes
    used: number; // bytes
    free: number; // bytes
    usage: number; // percentage
  };
  
  network: {
    bytesIn: number;
    bytesOut: number;
    packetsIn: number;
    packetsOut: number;
    errors: number;
  };
  
  // Process metrics
  processes: {
    total: number;
    running: number;
    sleeping: number;
    zombie: number;
  };
  
  // Uptime
  uptime: number; // seconds
}

// SMTP service metrics
export interface SMTPMetrics {
  timestamp: Timestamp;
  
  // Connection metrics
  connections: {
    total: number;
    active: number;
    rejected: number;
    maxConcurrent: number;
    averageDuration: number; // seconds
  };
  
  // Message metrics
  messages: {
    received: number;
    delivered: number;
    failed: number;
    queued: number;
    bounced: number;
    rejected: number;
    
    // Rates (per minute)
    receiveRate: number;
    deliveryRate: number;
    failureRate: number;
  };
  
  // Performance metrics
  performance: {
    averageProcessingTime: number; // milliseconds
    averageDeliveryTime: number; // milliseconds
    averageQueueTime: number; // milliseconds
    p95ProcessingTime: number; // milliseconds
    p99ProcessingTime: number; // milliseconds
  };
  
  // Queue metrics
  queue: {
    depth: number;
    processing: number;
    pending: number;
    deadLetter: number;
    oldestMessage: number; // seconds
  };
  
  // Security metrics
  security: {
    authAttempts: number;
    authFailures: number;
    spfFailures: number;
    dkimFailures: number;
    dmarcFailures: number;
    rateLimitHits: number;
    blockedIPs: number;
  };
  
  // Error metrics
  errors: {
    total: number;
    byType: Record<string, number>;
    byCode: Record<string, number>;
  };
}

// API service metrics
export interface APIMetrics {
  timestamp: Timestamp;
  
  // Request metrics
  requests: {
    total: number;
    successful: number;
    failed: number;
    
    // By status code
    byStatusCode: Record<string, number>;
    
    // By endpoint
    byEndpoint: Record<string, number>;
    
    // Rates
    requestRate: number; // per minute
    errorRate: number; // percentage
  };
  
  // Performance metrics
  performance: {
    averageResponseTime: number; // milliseconds
    p95ResponseTime: number; // milliseconds
    p99ResponseTime: number; // milliseconds
    slowestEndpoints: Array<{
      endpoint: string;
      averageTime: number;
    }>;
  };
  
  // Authentication metrics
  authentication: {
    attempts: number;
    successful: number;
    failed: number;
    rateLimited: number;
  };
  
  // Database metrics
  database: {
    connections: {
      active: number;
      idle: number;
      total: number;
    };
    queries: {
      total: number;
      slow: number;
      failed: number;
      averageTime: number; // milliseconds
    };
  };
}

// Alert definitions
export interface Alert {
  id: UUID;
  name: string;
  description: string;
  severity: AlertSeverity;
  
  // Trigger conditions
  metric: string;
  operator: AlertOperator;
  threshold: number;
  duration: number; // seconds
  
  // Status
  status: AlertStatus;
  triggeredAt?: Timestamp;
  resolvedAt?: Timestamp;
  acknowledgedAt?: Timestamp;
  acknowledgedBy?: UUID;
  
  // Notification
  notificationSent: boolean;
  lastNotificationAt?: Timestamp;
  
  // Metadata
  metadata?: Record<string, unknown>;
  tags: string[];
}

export enum AlertSeverity {
  INFO = 'INFO',
  WARNING = 'WARNING',
  CRITICAL = 'CRITICAL',
  EMERGENCY = 'EMERGENCY',
}

export enum AlertOperator {
  GREATER_THAN = 'GREATER_THAN',
  LESS_THAN = 'LESS_THAN',
  EQUALS = 'EQUALS',
  NOT_EQUALS = 'NOT_EQUALS',
  GREATER_THAN_OR_EQUAL = 'GREATER_THAN_OR_EQUAL',
  LESS_THAN_OR_EQUAL = 'LESS_THAN_OR_EQUAL',
}

export enum AlertStatus {
  ACTIVE = 'ACTIVE',
  RESOLVED = 'RESOLVED',
  ACKNOWLEDGED = 'ACKNOWLEDGED',
  SUPPRESSED = 'SUPPRESSED',
}

// Dashboard configuration
export interface Dashboard {
  id: UUID;
  name: string;
  description?: string;
  
  // Layout
  layout: DashboardLayout;
  
  // Widgets
  widgets: DashboardWidget[];
  
  // Permissions
  isPublic: boolean;
  allowedUsers: UUID[];
  allowedRoles: string[];
  
  // Metadata
  createdBy: UUID;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  
  // Settings
  autoRefresh: boolean;
  refreshInterval: number; // seconds
}

export interface DashboardLayout {
  columns: number;
  rows: number;
  gap: number;
}

export interface DashboardWidget {
  id: UUID;
  type: WidgetType;
  title: string;
  
  // Position and size
  x: number;
  y: number;
  width: number;
  height: number;
  
  // Configuration
  config: WidgetConfig;
  
  // Data source
  dataSource: string;
  query: string;
  
  // Refresh
  autoRefresh: boolean;
  refreshInterval: number; // seconds
}

export enum WidgetType {
  LINE_CHART = 'LINE_CHART',
  BAR_CHART = 'BAR_CHART',
  PIE_CHART = 'PIE_CHART',
  GAUGE = 'GAUGE',
  COUNTER = 'COUNTER',
  TABLE = 'TABLE',
  LOG_VIEWER = 'LOG_VIEWER',
  STATUS_INDICATOR = 'STATUS_INDICATOR',
  HEATMAP = 'HEATMAP',
}

export interface WidgetConfig {
  // Chart configuration
  xAxis?: string;
  yAxis?: string;
  series?: string[];
  colors?: string[];
  
  // Display options
  showLegend?: boolean;
  showGrid?: boolean;
  showTooltip?: boolean;
  
  // Thresholds
  thresholds?: Array<{
    value: number;
    color: string;
    label?: string;
  }>;
  
  // Formatting
  numberFormat?: string;
  dateFormat?: string;
  
  // Limits
  maxDataPoints?: number;
  timeRange?: string;
}

// Log entry for structured logging
export interface LogEntry {
  timestamp: Timestamp;
  level: LogLevel;
  message: string;
  
  // Context
  service: string;
  component?: string;
  requestId?: string;
  userId?: UUID;
  sessionId?: string;
  
  // Structured data
  data?: Record<string, unknown>;
  
  // Error information
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string;
  };
  
  // Performance
  duration?: number; // milliseconds
  
  // Tags for filtering
  tags: string[];
}

export enum LogLevel {
  ERROR = 'ERROR',
  WARN = 'WARN',
  INFO = 'INFO',
  DEBUG = 'DEBUG',
  TRACE = 'TRACE',
}

// Performance monitoring
export interface PerformanceMetric {
  timestamp: Timestamp;
  name: string;
  value: number;
  unit: string;
  
  // Context
  service: string;
  component?: string;
  operation?: string;
  
  // Tags for grouping
  tags: Record<string, string>;
  
  // Additional metadata
  metadata?: Record<string, unknown>;
}

// Health check result
export interface HealthCheck {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: Timestamp;
  responseTime: number; // milliseconds
  
  // Details
  message?: string;
  error?: string;
  
  // Dependencies
  dependencies?: HealthCheck[];
  
  // Metadata
  version?: string;
  uptime?: number; // seconds
  metadata?: Record<string, unknown>;
}

// Monitoring configuration
export interface MonitoringConfig {
  // Metrics collection
  metricsEnabled: boolean;
  metricsInterval: number; // seconds
  metricsRetention: number; // days
  
  // Logging
  logLevel: LogLevel;
  logRetention: number; // days
  structuredLogging: boolean;
  
  // Alerting
  alertingEnabled: boolean;
  alertRetention: number; // days
  
  // Health checks
  healthCheckInterval: number; // seconds
  healthCheckTimeout: number; // seconds
  
  // Performance monitoring
  performanceMonitoring: boolean;
  slowQueryThreshold: number; // milliseconds
  
  // External integrations
  prometheusEnabled: boolean;
  grafanaEnabled: boolean;
  elasticsearchEnabled: boolean;
}
