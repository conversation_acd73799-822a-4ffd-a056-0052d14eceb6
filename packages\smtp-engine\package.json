{"name": "@ironrelay/smtp-engine", "version": "1.0.0", "description": "IronRelay SMTP Engine based on Haraka", "main": "dist/index.js", "scripts": {"build": "tsc", "dev": "nodemon --exec ts-node src/index.ts", "start": "node dist/index.js", "start:prod": "node dist/index.js", "lint": "eslint \"src/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "clean": "<PERSON><PERSON><PERSON> dist"}, "dependencies": {"haraka": "^3.0.2", "@ironrelay/shared": "workspace:*", "@prisma/client": "^5.1.1", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "bcrypt": "^5.1.0", "nodemailer": "^6.9.4", "redis": "^4.6.7", "prom-client": "^14.2.0", "joi": "^17.9.2", "lodash": "^4.17.21", "moment": "^2.29.4", "uuid": "^9.0.0", "ipaddr.js": "^2.1.0", "cidr-matcher": "^2.1.1", "dns": "^0.2.2", "tls": "^0.0.1", "crypto": "^1.0.1"}, "devDependencies": {"@types/node": "^20.4.5", "@types/jest": "^29.5.3", "@types/bcrypt": "^5.0.0", "@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "jest": "^29.6.1", "ts-jest": "^29.1.1", "nodemon": "^3.0.1", "ts-node": "^10.9.1", "rimraf": "^5.0.1", "typescript": "^5.1.6"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/**/*.test.ts", "!src/**/*.spec.ts"]}}