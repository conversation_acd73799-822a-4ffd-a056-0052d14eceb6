import { faker } from '@faker-js/faker';
import { v4 as uuidv4 } from 'uuid';
import type { User, SMTPConfig, DomainRoute, SMTPUser, EmailTransaction } from '@ironrelay/shared';

export class TestDataFactory {
  static createUser(overrides: Partial<User> = {}): User {
    return {
      id: uuidv4(),
      email: faker.internet.email(),
      firstName: faker.person.firstName(),
      lastName: faker.person.lastName(),
      role: faker.helpers.arrayElement(['SUPER_ADMIN', 'ADMIN', 'OPERATOR', 'VIEWER']),
      isActive: true,
      isVerified: true,
      mfaEnabled: false,
      lastLoginAt: faker.date.recent(),
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      preferences: {
        theme: 'auto',
        language: 'en',
        timezone: 'UTC',
        emailNotifications: true,
        dashboardRefreshInterval: 30,
      },
      ...overrides,
    };
  }

  static createSMTPConfig(overrides: Partial<SMTPConfig> = {}): SMTPConfig {
    return {
      id: uuidv4(),
      name: faker.company.name() + ' SMTP',
      description: faker.lorem.sentence(),
      hostname: faker.internet.domainName(),
      port: faker.helpers.arrayElement([25, 587, 465, 2525]),
      enabled: true,
      tlsEnabled: true,
      tlsRequired: false,
      authRequired: true,
      authMethods: ['PLAIN', 'LOGIN'],
      maxConnections: faker.number.int({ min: 10, max: 1000 }),
      maxConnectionsPerIP: faker.number.int({ min: 1, max: 10 }),
      connectionTimeout: 30000,
      rateLimitEnabled: true,
      rateLimitPerIP: faker.number.int({ min: 10, max: 100 }),
      rateLimitPerUser: faker.number.int({ min: 50, max: 500 }),
      rateLimitWindow: 3600,
      maxMessageSize: 26214400, // 25MB
      maxRecipients: 100,
      queueEnabled: true,
      queueRetryAttempts: 3,
      queueRetryDelay: 300,
      queueMaxAge: 86400,
      ipWhitelist: [],
      ipBlacklist: [],
      cidrWhitelist: [],
      cidrBlacklist: [],
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      createdBy: uuidv4(),
      updatedBy: uuidv4(),
      ...overrides,
    };
  }

  static createDomainRoute(overrides: Partial<DomainRoute> = {}): DomainRoute {
    return {
      id: uuidv4(),
      domain: faker.internet.domainName(),
      enabled: true,
      priority: faker.number.int({ min: 1, max: 100 }),
      targetHost: faker.internet.domainName(),
      targetPort: faker.helpers.arrayElement([25, 587, 465]),
      useTLS: true,
      requireAuth: false,
      username: faker.internet.userName(),
      password: faker.internet.password(),
      maxRetries: 3,
      retryDelay: 300,
      timeout: 30000,
      fallbackRoutes: [],
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      createdBy: uuidv4(),
      updatedBy: uuidv4(),
      ...overrides,
    };
  }

  static createSMTPUser(overrides: Partial<SMTPUser> = {}): SMTPUser {
    return {
      id: uuidv4(),
      username: faker.internet.userName(),
      email: faker.internet.email(),
      password: faker.internet.password(),
      enabled: true,
      quotaDaily: faker.number.int({ min: 100, max: 10000 }),
      quotaMonthly: faker.number.int({ min: 1000, max: 100000 }),
      quotaUsedDaily: 0,
      quotaUsedMonthly: 0,
      lastUsedAt: faker.date.recent(),
      ipWhitelist: [],
      domainWhitelist: [],
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      createdBy: uuidv4(),
      updatedBy: uuidv4(),
      ...overrides,
    };
  }

  static createEmailTransaction(overrides: Partial<EmailTransaction> = {}): EmailTransaction {
    const status = faker.helpers.arrayElement(['RECEIVED', 'QUEUED', 'PROCESSING', 'DELIVERED', 'FAILED', 'BOUNCED']);
    
    return {
      id: uuidv4(),
      messageId: faker.string.alphanumeric(32),
      from: faker.internet.email(),
      to: [faker.internet.email()],
      cc: [],
      bcc: [],
      subject: faker.lorem.sentence(),
      size: faker.number.int({ min: 1000, max: 1000000 }),
      status,
      priority: faker.helpers.arrayElement(['LOW', 'NORMAL', 'HIGH']),
      attempts: status === 'FAILED' ? faker.number.int({ min: 1, max: 3 }) : 1,
      lastAttemptAt: faker.date.recent(),
      nextAttemptAt: status === 'FAILED' ? faker.date.future() : null,
      deliveredAt: status === 'DELIVERED' ? faker.date.recent() : null,
      bouncedAt: status === 'BOUNCED' ? faker.date.recent() : null,
      bounceReason: status === 'BOUNCED' ? faker.lorem.sentence() : null,
      smtpResponse: faker.lorem.sentence(),
      clientIP: faker.internet.ip(),
      clientHostname: faker.internet.domainName(),
      authUser: faker.internet.userName(),
      route: faker.internet.domainName(),
      headers: {
        'Message-ID': `<${faker.string.alphanumeric(32)}@${faker.internet.domainName()}>`,
        'Date': faker.date.recent().toISOString(),
        'From': faker.internet.email(),
        'To': faker.internet.email(),
        'Subject': faker.lorem.sentence(),
      },
      createdAt: faker.date.past(),
      updatedAt: faker.date.recent(),
      ...overrides,
    };
  }

  static createMultipleUsers(count: number, overrides: Partial<User> = {}): User[] {
    return Array.from({ length: count }, () => this.createUser(overrides));
  }

  static createMultipleSMTPConfigs(count: number, overrides: Partial<SMTPConfig> = {}): SMTPConfig[] {
    return Array.from({ length: count }, () => this.createSMTPConfig(overrides));
  }

  static createMultipleDomainRoutes(count: number, overrides: Partial<DomainRoute> = {}): DomainRoute[] {
    return Array.from({ length: count }, () => this.createDomainRoute(overrides));
  }

  static createMultipleSMTPUsers(count: number, overrides: Partial<SMTPUser> = {}): SMTPUser[] {
    return Array.from({ length: count }, () => this.createSMTPUser(overrides));
  }

  static createMultipleEmailTransactions(count: number, overrides: Partial<EmailTransaction> = {}): EmailTransaction[] {
    return Array.from({ length: count }, () => this.createEmailTransaction(overrides));
  }

  // Realistic test scenarios
  static createRealisticEmailFlow(): {
    user: User;
    smtpConfig: SMTPConfig;
    smtpUser: SMTPUser;
    domainRoute: DomainRoute;
    transactions: EmailTransaction[];
  } {
    const user = this.createUser({ role: 'ADMIN' });
    const smtpConfig = this.createSMTPConfig({ createdBy: user.id });
    const smtpUser = this.createSMTPUser({ createdBy: user.id });
    const domainRoute = this.createDomainRoute({ createdBy: user.id });
    
    const transactions = this.createMultipleEmailTransactions(10, {
      authUser: smtpUser.username,
      route: domainRoute.domain,
    });

    return {
      user,
      smtpConfig,
      smtpUser,
      domainRoute,
      transactions,
    };
  }

  static createHighVolumeScenario(): {
    users: User[];
    smtpConfigs: SMTPConfig[];
    smtpUsers: SMTPUser[];
    domainRoutes: DomainRoute[];
    transactions: EmailTransaction[];
  } {
    const users = this.createMultipleUsers(5);
    const smtpConfigs = this.createMultipleSMTPConfigs(3);
    const smtpUsers = this.createMultipleSMTPUsers(20);
    const domainRoutes = this.createMultipleDomainRoutes(10);
    const transactions = this.createMultipleEmailTransactions(1000);

    return {
      users,
      smtpConfigs,
      smtpUsers,
      domainRoutes,
      transactions,
    };
  }

  static createFailureScenario(): EmailTransaction[] {
    return [
      this.createEmailTransaction({
        status: 'FAILED',
        attempts: 3,
        bounceReason: 'Recipient address rejected',
        smtpResponse: '550 5.1.1 User unknown',
      }),
      this.createEmailTransaction({
        status: 'BOUNCED',
        bouncedAt: faker.date.recent(),
        bounceReason: 'Mailbox full',
        smtpResponse: '552 5.2.2 Mailbox full',
      }),
      this.createEmailTransaction({
        status: 'FAILED',
        attempts: 2,
        bounceReason: 'Connection timeout',
        smtpResponse: 'Connection timed out',
      }),
    ];
  }

  // Performance test data
  static createPerformanceTestData(emailCount: number = 10000): {
    smtpUsers: SMTPUser[];
    transactions: EmailTransaction[];
  } {
    const smtpUsers = this.createMultipleSMTPUsers(100);
    const transactions = Array.from({ length: emailCount }, (_, index) => {
      const smtpUser = faker.helpers.arrayElement(smtpUsers);
      return this.createEmailTransaction({
        authUser: smtpUser.username,
        createdAt: new Date(Date.now() - (emailCount - index) * 1000), // Spread over time
      });
    });

    return { smtpUsers, transactions };
  }

  // Security test data
  static createSecurityTestData(): {
    maliciousEmails: EmailTransaction[];
    suspiciousUsers: SMTPUser[];
    blockedIPs: string[];
  } {
    const maliciousEmails = [
      this.createEmailTransaction({
        subject: 'URGENT: Verify your account immediately',
        from: '<EMAIL>',
        clientIP: '*************',
      }),
      this.createEmailTransaction({
        subject: '<script>alert("XSS")</script>',
        from: '<EMAIL>',
        clientIP: '*********',
      }),
      this.createEmailTransaction({
        subject: 'Re: '.repeat(100), // Subject line flooding
        from: '<EMAIL>',
        clientIP: '***********',
      }),
    ];

    const suspiciousUsers = [
      this.createSMTPUser({
        username: 'admin',
        email: '<EMAIL>',
        quotaUsedDaily: 9999,
        quotaDaily: 100,
      }),
      this.createSMTPUser({
        username: 'test123',
        email: '<EMAIL>',
        quotaUsedMonthly: 50000,
        quotaMonthly: 1000,
      }),
    ];

    const blockedIPs = [
      '*************',
      '*********',
      '***********',
      '***********/24',
    ];

    return {
      maliciousEmails,
      suspiciousUsers,
      blockedIPs,
    };
  }

  // Load test data
  static createLoadTestScenario(concurrentUsers: number = 100): {
    users: User[];
    smtpUsers: SMTPUser[];
    emailBatches: EmailTransaction[][];
  } {
    const users = this.createMultipleUsers(concurrentUsers);
    const smtpUsers = this.createMultipleSMTPUsers(concurrentUsers);
    
    const emailBatches = users.map((user, index) => {
      const smtpUser = smtpUsers[index];
      return this.createMultipleEmailTransactions(
        faker.number.int({ min: 10, max: 100 }),
        { authUser: smtpUser.username }
      );
    });

    return {
      users,
      smtpUsers,
      emailBatches,
    };
  }
}
