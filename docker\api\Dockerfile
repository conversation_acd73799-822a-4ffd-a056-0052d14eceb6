# Multi-stage Dockerfile for IronRelay API Service

# Base stage with Node.js
FROM node:18-alpine AS base
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

# Copy package files
COPY package*.json ./
COPY packages/api/package*.json ./packages/api/
COPY packages/shared/package*.json ./packages/shared/

# Development stage
FROM base AS development

# Install all dependencies (including dev dependencies)
RUN npm ci

# Copy source code
COPY packages/api ./packages/api
COPY packages/shared ./packages/shared
COPY tsconfig.json ./

# Generate Prisma client
WORKDIR /app/packages/api
RUN npx prisma generate

# Expose ports
EXPOSE 3000 2525

# Set user for security
USER node

# Start development server
CMD ["dumb-init", "npm", "run", "dev"]

# Build stage
FROM base AS build

# Install all dependencies
RUN npm ci

# Copy source code
COPY packages/api ./packages/api
COPY packages/shared ./packages/shared
COPY tsconfig.json ./

# Build shared package
WORKDIR /app/packages/shared
RUN npm run build

# Build API package
WORKDIR /app/packages/api
RUN npx prisma generate
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Install system dependencies
RUN apk add --no-cache \
    curl \
    dumb-init \
    && rm -rf /var/cache/apk/*

WORKDIR /app

# Copy package files
COPY package*.json ./
COPY packages/api/package*.json ./packages/api/
COPY packages/shared/package*.json ./packages/shared/

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=build /app/packages/api/dist ./packages/api/dist
COPY --from=build /app/packages/api/prisma ./packages/api/prisma
COPY --from=build /app/packages/shared/dist ./packages/shared/dist

# Copy Prisma client
COPY --from=build /app/packages/api/node_modules/.prisma ./packages/api/node_modules/.prisma

# Create non-root user
RUN addgroup -g 1001 -S nodejs
RUN adduser -S ironrelay -u 1001

# Create directories and set permissions
RUN mkdir -p /app/logs /app/certs /app/uploads && \
    chown -R ironrelay:nodejs /app

# Switch to non-root user
USER ironrelay

# Expose ports
EXPOSE 3000 2525

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3000/health || exit 1

# Start application
CMD ["dumb-init", "node", "packages/api/dist/main.js"]
