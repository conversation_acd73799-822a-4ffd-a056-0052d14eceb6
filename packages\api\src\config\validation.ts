import * as Jo<PERSON> from 'joi';

export const validationSchema = Joi.object({
  // Application
  NODE_ENV: Joi.string().valid('development', 'staging', 'production').default('development'),
  API_PORT: Joi.number().port().default(3000),
  API_HOST: Joi.string().default('0.0.0.0'),

  // Database
  DATABASE_URL: Joi.string().required(),

  // Redis
  REDIS_URL: Joi.string().optional(),

  // JWT
  JWT_SECRET: Joi.string().min(32).required(),
  JWT_EXPIRES_IN: Joi.string().default('1h'),
  JWT_REFRESH_EXPIRES_IN: Joi.string().default('7d'),

  // Security
  BCRYPT_ROUNDS: Joi.number().integer().min(10).max(15).default(12),
  SESSION_SECRET: Joi.string().min(32).required(),
  RATE_LIMIT_WINDOW_MS: Joi.number().integer().positive().default(900000),
  RATE_LIMIT_MAX_REQUESTS: Joi.number().integer().positive().default(100),

  // SMTP
  SMTP_PORT: Joi.number().port().default(2525),
  SMTP_HOST: Joi.string().default('0.0.0.0'),

  // Frontend
  FRONTEND_PORT: Joi.number().port().default(3001),
  FRONTEND_URL: Joi.string().uri().default('http://localhost:3001'),

  // Logging
  LOG_LEVEL: Joi.string().valid('error', 'warn', 'info', 'debug', 'verbose').default('info'),
  LOG_DIR: Joi.string().default('./logs'),

  // TLS
  TLS_CERT_PATH: Joi.string().optional(),
  TLS_KEY_PATH: Joi.string().optional(),

  // Monitoring
  PROMETHEUS_PORT: Joi.number().port().default(9090),
  GRAFANA_PORT: Joi.number().port().default(3000),

  // Email notifications
  SMTP_NOTIFICATION_HOST: Joi.string().optional(),
  SMTP_NOTIFICATION_PORT: Joi.number().port().default(587),
  SMTP_NOTIFICATION_SECURE: Joi.boolean().default(true),
  SMTP_NOTIFICATION_USER: Joi.string().optional(),
  SMTP_NOTIFICATION_PASS: Joi.string().optional(),
  SMTP_NOTIFICATION_FROM: Joi.string().email().optional(),

  // License
  LICENSE_KEY: Joi.string().optional(),
  LICENSE_SERVER_URL: Joi.string().uri().optional(),

  // Backup
  BACKUP_ENABLED: Joi.boolean().default(false),
  BACKUP_SCHEDULE: Joi.string().default('0 2 * * *'),
  BACKUP_RETENTION_DAYS: Joi.number().integer().positive().default(7),
  BACKUP_STORAGE_TYPE: Joi.string().valid('local', 's3', 'azure', 'gcp').default('local'),
  BACKUP_LOCAL_PATH: Joi.string().default('./backups'),

  // AWS S3 (for backup)
  AWS_ACCESS_KEY_ID: Joi.string().optional(),
  AWS_SECRET_ACCESS_KEY: Joi.string().optional(),
  AWS_REGION: Joi.string().default('us-east-1'),
  AWS_S3_BUCKET: Joi.string().optional(),

  // Development
  ENABLE_SWAGGER: Joi.boolean().default(true),
  ENABLE_CORS: Joi.boolean().default(true),
  CORS_ORIGIN: Joi.string().default('http://localhost:3001'),

  // Health checks
  HEALTH_CHECK_TIMEOUT: Joi.number().integer().positive().default(5000),
  HEALTH_CHECK_INTERVAL: Joi.number().integer().positive().default(30000),

  // Queue
  QUEUE_REDIS_URL: Joi.string().optional(),
  QUEUE_CONCURRENCY: Joi.number().integer().positive().default(10),
  QUEUE_MAX_ATTEMPTS: Joi.number().integer().positive().default(3),
  QUEUE_DELAY: Joi.number().integer().positive().default(5000),

  // Metrics
  METRICS_ENABLED: Joi.boolean().default(true),
  METRICS_INTERVAL: Joi.number().integer().positive().default(60000),
  METRICS_RETENTION_DAYS: Joi.number().integer().positive().default(30),
});
