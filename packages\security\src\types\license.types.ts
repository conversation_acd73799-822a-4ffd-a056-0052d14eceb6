export interface LicenseInfo {
  id: string;
  key: string;
  type: LicenseType;
  status: LicenseStatus;
  issuedTo: string;
  issuedBy: string;
  issuedAt: Date;
  expiresAt?: Date;
  features: LicenseFeature[];
  limits: LicenseLimits;
  metadata: Record<string, any>;
  signature: string;
  version: string;
}

export enum LicenseType {
  TRIAL = 'TRIAL',
  STANDARD = 'STANDARD',
  PROFESSIONAL = 'PROFESSIONAL',
  ENTERPRISE = 'ENTERPRISE',
  UNLIMITED = 'UNLIMITED',
  DEVELOPER = 'DEVELOPER',
  CUSTOM = 'CUSTOM',
}

export enum LicenseStatus {
  VALID = 'VALID',
  EXPIRED = 'EXPIRED',
  INVALID = 'INVALID',
  REVOKED = 'REVOKED',
  SUSPENDED = 'SUSPENDED',
  PENDING = 'PENDING',
  UNKNOWN = 'UNKNOWN',
}

export interface LicenseFeature {
  name: string;
  enabled: boolean;
  parameters?: Record<string, any>;
}

export interface LicenseLimits {
  maxUsers?: number;
  maxDomains?: number;
  maxEmailsPerDay?: number;
  maxEmailsPerMonth?: number;
  maxConnections?: number;
  maxInstances?: number;
  allowedEnvironments?: string[];
  allowedIPs?: string[];
  allowedDomains?: string[];
  customLimits?: Record<string, number>;
}

export interface LicenseValidationRequest {
  licenseKey: string;
  instanceId: string;
  environment: string;
  version: string;
  features?: string[];
  metadata?: Record<string, any>;
}

export interface LicenseValidationResponse {
  valid: boolean;
  license?: LicenseInfo;
  error?: string;
  warnings?: string[];
  nextValidation?: Date;
  serverTime: Date;
  signature: string;
}

export interface LicenseUsage {
  instanceId: string;
  licenseKey: string;
  startTime: Date;
  lastHeartbeat: Date;
  environment: string;
  version: string;
  features: string[];
  metrics: LicenseUsageMetrics;
}

export interface LicenseUsageMetrics {
  usersCount: number;
  domainsCount: number;
  emailsSentToday: number;
  emailsSentThisMonth: number;
  activeConnections: number;
  peakConnections: number;
  uptime: number;
  customMetrics?: Record<string, number>;
}

export interface LicenseServer {
  url: string;
  publicKey: string;
  timeout: number;
  retryAttempts: number;
  retryDelay: number;
  cacheTimeout: number;
}

export interface LicenseCache {
  licenseKey: string;
  validationResponse: LicenseValidationResponse;
  cachedAt: Date;
  expiresAt: Date;
}

export interface LicenseEvent {
  id: string;
  type: LicenseEventType;
  licenseKey: string;
  instanceId: string;
  timestamp: Date;
  description: string;
  metadata?: Record<string, any>;
}

export enum LicenseEventType {
  VALIDATION_SUCCESS = 'VALIDATION_SUCCESS',
  VALIDATION_FAILURE = 'VALIDATION_FAILURE',
  LICENSE_EXPIRED = 'LICENSE_EXPIRED',
  LICENSE_REVOKED = 'LICENSE_REVOKED',
  LICENSE_SUSPENDED = 'LICENSE_SUSPENDED',
  FEATURE_DISABLED = 'FEATURE_DISABLED',
  LIMIT_EXCEEDED = 'LIMIT_EXCEEDED',
  HEARTBEAT_SENT = 'HEARTBEAT_SENT',
  HEARTBEAT_FAILED = 'HEARTBEAT_FAILED',
  CACHE_HIT = 'CACHE_HIT',
  CACHE_MISS = 'CACHE_MISS',
  CACHE_EXPIRED = 'CACHE_EXPIRED',
}

export interface LicenseConfig {
  serverUrl: string;
  publicKey: string;
  licenseKey: string;
  instanceId: string;
  environment: string;
  version: string;
  heartbeatInterval: number;
  validationInterval: number;
  cacheTimeout: number;
  retryAttempts: number;
  retryDelay: number;
  timeout: number;
  offlineGracePeriod: number;
  enableCaching: boolean;
  enableHeartbeat: boolean;
  enableMetrics: boolean;
  strictMode: boolean;
}

export interface LicenseManager {
  initialize(config: LicenseConfig): Promise<void>;
  validate(): Promise<LicenseValidationResponse>;
  getLicenseInfo(): LicenseInfo | null;
  isFeatureEnabled(featureName: string): boolean;
  checkLimit(limitName: string, currentValue: number): boolean;
  sendHeartbeat(): Promise<void>;
  shutdown(): Promise<void>;
  getUsageMetrics(): LicenseUsageMetrics;
  getEvents(): LicenseEvent[];
}
