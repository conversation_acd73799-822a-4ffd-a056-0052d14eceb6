export declare function isLength(value: string): boolean;
export declare function isArbitraryLength(value: string): boolean;
export declare function isArbitrarySize(value: string): boolean;
export declare function isArbitraryPosition(value: string): boolean;
export declare function isArbitraryUrl(value: string): boolean;
export declare function isArbitraryNumber(value: string): boolean;
/**
 * @deprecated Will be removed in next major version. Use `isArbitraryNumber` instead.
 */
export declare const isArbitraryWeight: typeof isArbitraryNumber;
export declare function isNumber(value: string): boolean;
export declare function isPercent(value: string): boolean;
export declare function isInteger(value: string): boolean;
export declare function isArbitraryValue(value: string): boolean;
export declare function isAny(): boolean;
export declare function isTshirtSize(value: string): boolean;
export declare function isArbitraryShadow(value: string): boolean;
