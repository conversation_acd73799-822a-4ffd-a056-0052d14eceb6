import React from 'react';
import { useParams } from 'react-router-dom';
import { Helmet } from 'react-helmet-async';

export default function UserDetailPage() {
  const { id } = useParams<{ id: string }>();

  return (
    <>
      <Helmet>
        <title>User Details - IronRelay</title>
      </Helmet>

      <div className="space-y-6">
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            User Details
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            User ID: {id}
          </p>
        </div>

        <div className="card">
          <div className="card-body">
            <p className="text-gray-500 dark:text-gray-400 text-center py-8">
              User details interface coming soon...
            </p>
          </div>
        </div>
      </div>
    </>
  );
}
