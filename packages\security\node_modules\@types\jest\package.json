{"name": "@types/jest", "version": "29.5.14", "description": "TypeScript definitions for jest", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/jest", "license": "MIT", "contributors": [{"name": "<PERSON><PERSON> (https://asana.com)\n//                 Ivo Stratev", "githubUsername": "NoHomey", "url": "https://github.com/NoHomey"}, {"name": "jwbay", "githubUsername": "jwbay", "url": "https://github.com/jwbay"}, {"name": "<PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/asvet<PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "alexjoverm", "url": "https://github.com/alexjoverm"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON>", "url": "https://github.com/epicallan"}, {"name": "<PERSON><PERSON>", "githubUsername": "ikatyang", "url": "https://github.com/ikatyang"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "wsmd", "url": "https://github.com/wsmd"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/<PERSON>"}, {"name": "<PERSON>", "githubUsername": "douglasduteil", "url": "https://github.com/douglasduteil"}, {"name": "Ahn", "githubUsername": "ahnpnl", "url": "https://github.com/ahnpnl"}, {"name": "<PERSON>", "githubUsername": "UselessPickles", "url": "https://github.com/UselessPickles"}, {"name": "<PERSON>", "githubUsername": "r3nya", "url": "https://github.com/r3nya"}, {"name": "<PERSON>", "githubUsername": "hotell", "url": "https://github.com/hotell"}, {"name": "<PERSON>", "githubUsername": "sebald", "url": "https://github.com/sebald"}, {"name": "<PERSON>", "githubUsername": "andys8", "url": "https://github.com/andys8"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/antoinebrault"}, {"name": "<PERSON>", "githubUsername": "gstamac", "url": "https://github.com/gstamac"}, {"name": "ExE Boss", "githubUsername": "ExE-Boss", "url": "https://github.com/ExE-Boss"}, {"name": "<PERSON>", "githubUsername": "quassnoi", "url": "https://github.com/quassnoi"}, {"name": "<PERSON>", "githubUsername": "Belco90", "url": "https://github.com/Belco90"}, {"name": "<PERSON>", "githubUsername": "tony<PERSON><PERSON>", "url": "https://github.com/tonyhallett"}, {"name": "<PERSON>", "githubUsername": "ycmjas<PERSON>", "url": "https://github.com/ycmjason"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "pawfa", "url": "https://github.com/pawfa"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/gerkindev"}, {"name": "<PERSON>", "githubUsername": "domdomegg", "url": "https://github.com/domdomegg"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/mraz<PERSON><PERSON>"}], "main": "", "types": "index.d.ts", "exports": {".": {"types": "./index.d.ts"}, "./package.json": "./package.json"}, "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/jest"}, "scripts": {}, "dependencies": {"expect": "^29.0.0", "pretty-format": "^29.0.0"}, "peerDependencies": {}, "typesPublisherContentHash": "03b921cd51b4ea0ab99ff3733f9e799bed833eccc13adaa2aaeb088345807f4d", "typeScriptVersion": "4.8"}