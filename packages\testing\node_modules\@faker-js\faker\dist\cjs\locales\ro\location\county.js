"use strict";var u=Object.defineProperty;var l=Object.getOwnPropertyDescriptor;var o=Object.getOwnPropertyNames;var s=Object.prototype.hasOwnProperty;var n=(i,a)=>{for(var e in a)u(i,e,{get:a[e],enumerable:!0})},B=(i,a,e,t)=>{if(a&&typeof a=="object"||typeof a=="function")for(let r of o(a))!s.call(i,r)&&r!==e&&u(i,r,{get:()=>a[r],enumerable:!(t=l(a,r))||t.enumerable});return i};var v=i=>B(u({},"__esModule",{value:!0}),i);var m={};n(m,{default:()=>c});module.exports=v(m);var c=["Alba","Arad","Arges","Bacau","Bihor","Bistrita-Na<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON>ures<PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Caras-Sever<PERSON>","<PERSON><PERSON>j","<PERSON>stant<PERSON>","Covasna","Dam<PERSON><PERSON><PERSON>","<PERSON>lj","<PERSON><PERSON>","Giurgiu","<PERSON>rj","<PERSON>rg<PERSON>","<PERSON>nedoara","Ialomita","Iasi","<PERSON>fov","<PERSON>mures","<PERSON>hedinti","<PERSON>res","Neamt","<PERSON>lt","<PERSON><PERSON>ova","<PERSON>aj","<PERSON>tu-<PERSON>","<PERSON><PERSON>u","<PERSON><PERSON>va","Teleorman","Timis","Tulcea","Valcea","Vaslui","Vrancea"];
