import * as path from 'path';
import * as fs from 'fs';
import { Logger } from '../utils/logger';

export interface SMTPEngineConfig {
  smtp: {
    port: number;
    host: string;
    hostname: string;
    maxConnections: number;
    maxConnectionsPerIP: number;
    connectionTimeout: number;
  };
  tls: {
    enabled: boolean;
    required: boolean;
    certPath?: string;
    keyPath?: string;
    ciphers: string[];
  };
  auth: {
    required: boolean;
    methods: string[];
  };
  security: {
    ipWhitelist: string[];
    ipBlacklist: string[];
    cidrWhitelist: string[];
    cidrBlacklist: string[];
  };
  rateLimit: {
    enabled: boolean;
    perIP: number;
    perUser: number;
    window: number; // seconds
  };
  message: {
    maxSize: number; // bytes
    maxRecipients: number;
  };
  queue: {
    enabled: boolean;
    retryAttempts: number;
    retryDelay: number; // seconds
    maxAge: number; // seconds
  };
  database: {
    url: string;
  };
  redis: {
    url?: string;
  };
  logging: {
    level: string;
    dir: string;
  };
  metrics: {
    enabled: boolean;
    port: number;
  };
  api: {
    url: string;
  };
}

export class Config {
  private static instance: Config;
  private config: SMTPEngineConfig;
  private logger = Logger.getInstance();

  private constructor() {
    this.config = this.getDefaultConfig();
  }

  public static getInstance(): Config {
    if (!Config.instance) {
      Config.instance = new Config();
    }
    return Config.instance;
  }

  public async load(): Promise<void> {
    try {
      // Load from environment variables
      this.loadFromEnvironment();

      // Load from database if available
      await this.loadFromDatabase();

      this.logger.info('Configuration loaded successfully');
    } catch (error) {
      this.logger.error('Failed to load configuration:', error);
      throw error;
    }
  }

  public get<T = unknown>(key: string): T {
    const keys = key.split('.');
    let value: unknown = this.config;

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = (value as Record<string, unknown>)[k];
      } else {
        return undefined as T;
      }
    }

    return value as T;
  }

  public set(key: string, value: unknown): void {
    const keys = key.split('.');
    let current: unknown = this.config;

    for (let i = 0; i < keys.length - 1; i++) {
      const k = keys[i];
      if (!current || typeof current !== 'object' || !(k in current)) {
        (current as Record<string, unknown>)[k] = {};
      }
      current = (current as Record<string, unknown>)[k];
    }

    if (current && typeof current === 'object') {
      (current as Record<string, unknown>)[keys[keys.length - 1]] = value;
    }
  }

  public getAll(): SMTPEngineConfig {
    return { ...this.config };
  }

  private getDefaultConfig(): SMTPEngineConfig {
    return {
      smtp: {
        port: parseInt(process.env.SMTP_PORT || '2525', 10),
        host: process.env.SMTP_HOST || '0.0.0.0',
        hostname: process.env.SMTP_HOSTNAME || 'ironrelay.local',
        maxConnections: parseInt(process.env.SMTP_MAX_CONNECTIONS || '100', 10),
        maxConnectionsPerIP: parseInt(process.env.SMTP_MAX_CONNECTIONS_PER_IP || '10', 10),
        connectionTimeout: parseInt(process.env.SMTP_CONNECTION_TIMEOUT || '30000', 10),
      },
      tls: {
        enabled: process.env.TLS_ENABLED === 'true',
        required: process.env.TLS_REQUIRED === 'true',
        certPath: process.env.TLS_CERT_PATH,
        keyPath: process.env.TLS_KEY_PATH,
        ciphers: process.env.TLS_CIPHERS?.split(',') || [
          'ECDHE-RSA-AES128-GCM-SHA256',
          'ECDHE-RSA-AES256-GCM-SHA384',
          'ECDHE-RSA-AES128-SHA256',
          'ECDHE-RSA-AES256-SHA384',
        ],
      },
      auth: {
        required: process.env.AUTH_REQUIRED === 'true',
        methods: process.env.AUTH_METHODS?.split(',') || ['PLAIN', 'LOGIN'],
      },
      security: {
        ipWhitelist: process.env.IP_WHITELIST?.split(',') || [],
        ipBlacklist: process.env.IP_BLACKLIST?.split(',') || [],
        cidrWhitelist: process.env.CIDR_WHITELIST?.split(',') || [],
        cidrBlacklist: process.env.CIDR_BLACKLIST?.split(',') || [],
      },
      rateLimit: {
        enabled: process.env.RATE_LIMIT_ENABLED !== 'false',
        perIP: parseInt(process.env.RATE_LIMIT_PER_IP || '100', 10),
        perUser: parseInt(process.env.RATE_LIMIT_PER_USER || '1000', 10),
        window: parseInt(process.env.RATE_LIMIT_WINDOW || '3600', 10),
      },
      message: {
        maxSize: parseInt(process.env.MAX_MESSAGE_SIZE || '26214400', 10), // 25MB
        maxRecipients: parseInt(process.env.MAX_RECIPIENTS || '100', 10),
      },
      queue: {
        enabled: process.env.QUEUE_ENABLED !== 'false',
        retryAttempts: parseInt(process.env.QUEUE_RETRY_ATTEMPTS || '3', 10),
        retryDelay: parseInt(process.env.QUEUE_RETRY_DELAY || '300', 10),
        maxAge: parseInt(process.env.QUEUE_MAX_AGE || '86400', 10),
      },
      database: {
        url: process.env.DATABASE_URL || 'postgresql://ironrelay:ironrelay@localhost:5432/ironrelay',
      },
      redis: {
        url: process.env.REDIS_URL,
      },
      logging: {
        level: process.env.LOG_LEVEL || 'info',
        dir: process.env.LOG_DIR || './logs',
      },
      metrics: {
        enabled: process.env.METRICS_ENABLED !== 'false',
        port: parseInt(process.env.METRICS_PORT || '9090', 10),
      },
      api: {
        url: process.env.API_URL || 'http://localhost:3000',
      },
    };
  }

  private loadFromEnvironment(): void {
    // Configuration is already loaded from environment in getDefaultConfig
    this.logger.debug('Configuration loaded from environment variables');
  }

  private async loadFromDatabase(): Promise<void> {
    try {
      // TODO: Load configuration from database
      // This will be implemented when database service is ready
      this.logger.debug('Database configuration loading skipped (not implemented yet)');
    } catch (error) {
      this.logger.warn('Failed to load configuration from database:', error);
      // Continue with environment configuration
    }
  }

  public async reload(): Promise<void> {
    this.logger.info('Reloading configuration...');
    await this.load();
    this.logger.info('Configuration reloaded successfully');
  }

  public validate(): boolean {
    try {
      // Validate required configuration
      if (!this.config.smtp.port || this.config.smtp.port < 1 || this.config.smtp.port > 65535) {
        throw new Error('Invalid SMTP port');
      }

      if (!this.config.smtp.host) {
        throw new Error('SMTP host is required');
      }

      if (!this.config.database.url) {
        throw new Error('Database URL is required');
      }

      if (this.config.tls.enabled) {
        if (this.config.tls.certPath && !fs.existsSync(this.config.tls.certPath)) {
          throw new Error(`TLS certificate file not found: ${this.config.tls.certPath}`);
        }

        if (this.config.tls.keyPath && !fs.existsSync(this.config.tls.keyPath)) {
          throw new Error(`TLS key file not found: ${this.config.tls.keyPath}`);
        }
      }

      this.logger.info('Configuration validation passed');
      return true;
    } catch (error) {
      this.logger.error('Configuration validation failed:', error);
      return false;
    }
  }
}
