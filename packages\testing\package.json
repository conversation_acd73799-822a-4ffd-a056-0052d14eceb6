{"name": "@ironrelay/testing", "version": "1.0.0", "description": "IronRelay Testing Framework", "private": true, "scripts": {"test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "jest --config jest.integration.config.js", "test:e2e": "jest --config jest.e2e.config.js", "test:performance": "jest --config jest.performance.config.js", "test:security": "jest --config jest.security.config.js", "test:all": "npm run test && npm run test:integration && npm run test:e2e", "test:ci": "jest --ci --coverage --watchAll=false", "lint": "eslint . --ext .ts,.js", "lint:fix": "eslint . --ext .ts,.js --fix", "clean": "rimraf coverage dist"}, "dependencies": {"@ironrelay/shared": "workspace:*", "@ironrelay/api": "workspace:*", "@ironrelay/smtp-engine": "workspace:*", "@ironrelay/security": "workspace:*", "jest": "^29.6.1", "supertest": "^6.3.3", "nodemailer": "^6.9.4", "smtp-server": "^3.12.0", "mailparser": "^3.6.5", "puppeteer": "^20.8.0", "playwright": "^1.36.2", "artillery": "^2.0.0", "k6": "^0.45.0", "newman": "^5.3.2", "axios": "^1.4.0", "ws": "^8.13.0", "ioredis": "^5.3.2", "pg": "^8.11.1", "mongodb": "^5.7.0", "faker": "^6.6.6", "@faker-js/faker": "^8.0.2", "chance": "^1.1.11", "uuid": "^9.0.0", "lodash": "^4.17.21", "moment": "^2.29.4", "csv-parser": "^3.0.0", "json2csv": "^6.1.0"}, "devDependencies": {"@types/jest": "^29.5.3", "@types/supertest": "^2.0.12", "@types/nodemailer": "^6.4.8", "@types/ws": "^8.5.5", "@types/pg": "^8.10.2", "@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "typescript": "^5.1.6", "ts-jest": "^29.1.1", "eslint": "^8.45.0", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "rimraf": "^5.0.1", "cross-env": "^7.0.3"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.test.ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/test-utils/**", "!src/fixtures/**"], "coverageDirectory": "coverage", "coverageReporters": ["text", "lcov", "html"], "setupFilesAfterEnv": ["<rootDir>/src/test-utils/setup.ts"], "testTimeout": 30000, "maxWorkers": "50%"}}