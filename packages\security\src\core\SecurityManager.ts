import { EventEmitter } from 'events';
import * as crypto from 'crypto';
import * as os from 'os';
import * as path from 'path';

import {
  SecurityConfig,
  SecurityContext,
  SecurityEvent,
  SecurityEventType,
  SecuritySeverity,
  SecurityMetrics,
  SecurityAlert,
} from '../types/security.types';

import { LicenseValidator } from '../license/LicenseValidator';
import { CodeProtection } from '../protection/CodeProtection';
import { IntegrityChecker } from '../integrity/IntegrityChecker';
import { AntiTamper } from '../protection/AntiTamper';
import { SecurityLogger } from '../utils/SecurityLogger';
import { CryptoUtils } from '../utils/CryptoUtils';

export class SecurityManager extends EventEmitter {
  private config: SecurityConfig;
  private context: SecurityContext;
  private licenseValidator: LicenseValidator;
  private codeProtection: CodeProtection;
  private integrityChecker: IntegrityChecker;
  private antiTamper: AntiTamper;
  private logger: SecurityLogger;
  private cryptoUtils: CryptoUtils;
  
  private events: SecurityEvent[] = [];
  private alerts: SecurityAlert[] = [];
  private metrics: SecurityMetrics;
  private initialized = false;
  private running = false;
  
  private heartbeatInterval?: NodeJS.Timeout;
  private integrityInterval?: NodeJS.Timeout;
  private tamperInterval?: NodeJS.Timeout;

  constructor(config: SecurityConfig) {
    super();
    this.config = config;
    this.context = this.createSecurityContext();
    this.metrics = this.initializeMetrics();
    
    this.logger = new SecurityLogger();
    this.cryptoUtils = new CryptoUtils();
    
    this.licenseValidator = new LicenseValidator({
      serverUrl: config.licenseServerUrl || '',
      licenseKey: config.licenseKey || '',
      instanceId: this.context.instanceId,
      environment: this.context.environment,
      heartbeatInterval: config.heartbeatInterval,
    });
    
    this.codeProtection = new CodeProtection({
      enableObfuscation: config.enableObfuscation,
      debugMode: config.debugMode,
    });
    
    this.integrityChecker = new IntegrityChecker({
      enabled: config.enableIntegrityChecks,
      checkInterval: 60000, // 1 minute
    });
    
    this.antiTamper = new AntiTamper({
      enabled: config.enableAntiTamper,
      sensitivity: 'MEDIUM',
      checkInterval: 30000, // 30 seconds
    });
    
    this.setupEventHandlers();
  }

  public async initialize(): Promise<void> {
    if (this.initialized) {
      throw new Error('SecurityManager already initialized');
    }

    try {
      this.logger.info('Initializing SecurityManager', { context: this.context });

      // Validate environment
      await this.validateEnvironment();

      // Initialize components
      await this.licenseValidator.initialize();
      await this.codeProtection.initialize();
      await this.integrityChecker.initialize();
      await this.antiTamper.initialize();

      // Validate license
      if (this.config.licenseKey) {
        const licenseValid = await this.licenseValidator.validate();
        if (!licenseValid.valid) {
          throw new Error(`License validation failed: ${licenseValid.error}`);
        }
      }

      // Start monitoring
      this.startMonitoring();

      this.initialized = true;
      this.running = true;

      this.recordEvent({
        type: SecurityEventType.LICENSE_VALIDATION,
        severity: SecuritySeverity.LOW,
        source: 'SecurityManager',
        description: 'SecurityManager initialized successfully',
      });

      this.logger.info('SecurityManager initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize SecurityManager', error);
      throw error;
    }
  }

  public async shutdown(): Promise<void> {
    if (!this.running) {
      return;
    }

    try {
      this.logger.info('Shutting down SecurityManager');

      this.running = false;

      // Stop monitoring
      this.stopMonitoring();

      // Shutdown components
      await this.licenseValidator.shutdown();
      await this.codeProtection.shutdown();
      await this.integrityChecker.shutdown();
      await this.antiTamper.shutdown();

      this.recordEvent({
        type: SecurityEventType.LICENSE_VALIDATION,
        severity: SecuritySeverity.LOW,
        source: 'SecurityManager',
        description: 'SecurityManager shutdown completed',
      });

      this.logger.info('SecurityManager shutdown completed');
    } catch (error) {
      this.logger.error('Error during SecurityManager shutdown', error);
      throw error;
    }
  }

  public isInitialized(): boolean {
    return this.initialized;
  }

  public isRunning(): boolean {
    return this.running;
  }

  public getContext(): SecurityContext {
    return { ...this.context };
  }

  public getMetrics(): SecurityMetrics {
    return {
      ...this.metrics,
      uptime: Date.now() - this.context.startTime.getTime(),
    };
  }

  public getEvents(limit?: number): SecurityEvent[] {
    const events = [...this.events].sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
    return limit ? events.slice(0, limit) : events;
  }

  public getAlerts(unacknowledgedOnly = false): SecurityAlert[] {
    const alerts = [...this.alerts];
    return unacknowledgedOnly ? alerts.filter(alert => !alert.acknowledged) : alerts;
  }

  public async acknowledgeAlert(alertId: string, acknowledgedBy: string): Promise<void> {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.acknowledged = true;
      alert.acknowledgedBy = acknowledgedBy;
      alert.acknowledgedAt = new Date();
      
      this.logger.info('Security alert acknowledged', { alertId, acknowledgedBy });
    }
  }

  public async resolveAlert(alertId: string, resolvedBy: string): Promise<void> {
    const alert = this.alerts.find(a => a.id === alertId);
    if (alert) {
      alert.resolved = true;
      alert.resolvedBy = resolvedBy;
      alert.resolvedAt = new Date();
      
      this.logger.info('Security alert resolved', { alertId, resolvedBy });
    }
  }

  public isFeatureEnabled(featureName: string): boolean {
    return this.licenseValidator.isFeatureEnabled(featureName);
  }

  public checkLimit(limitName: string, currentValue: number): boolean {
    return this.licenseValidator.checkLimit(limitName, currentValue);
  }

  private createSecurityContext(): SecurityContext {
    return {
      instanceId: crypto.randomUUID(),
      startTime: new Date(),
      environment: process.env.NODE_ENV || 'production',
      nodeVersion: process.version,
      platform: os.platform(),
      architecture: os.arch(),
      processId: process.pid,
      parentProcessId: process.ppid,
      workingDirectory: process.cwd(),
      executablePath: process.execPath,
      commandLineArgs: process.argv,
    };
  }

  private initializeMetrics(): SecurityMetrics {
    return {
      totalEvents: 0,
      eventsByType: {} as Record<SecurityEventType, number>,
      eventsBySeverity: {} as Record<SecuritySeverity, number>,
      uptime: 0,
      integrityChecks: 0,
      licenseValidations: 0,
      tamperingAttempts: 0,
    };
  }

  private async validateEnvironment(): Promise<void> {
    const { allowedEnvironments } = this.config;
    
    if (allowedEnvironments.length > 0 && !allowedEnvironments.includes(this.context.environment)) {
      const error = `Environment '${this.context.environment}' is not allowed. Allowed: ${allowedEnvironments.join(', ')}`;
      
      this.recordEvent({
        type: SecurityEventType.ENVIRONMENT_VIOLATION,
        severity: SecuritySeverity.CRITICAL,
        source: 'SecurityManager',
        description: error,
      });
      
      throw new Error(error);
    }

    // Check for debugging tools
    if (!this.config.debugMode && this.isDebuggerAttached()) {
      const error = 'Debugger detected in production environment';
      
      this.recordEvent({
        type: SecurityEventType.DEBUG_MODE_DETECTED,
        severity: SecuritySeverity.HIGH,
        source: 'SecurityManager',
        description: error,
      });
      
      throw new Error(error);
    }
  }

  private isDebuggerAttached(): boolean {
    // Check for common debugging indicators
    const debuggerIndicators = [
      'inspector' in process,
      process.env.NODE_OPTIONS?.includes('--inspect'),
      process.execArgv.some(arg => arg.includes('--inspect')),
      typeof (global as any).v8debug !== 'undefined',
    ];

    return debuggerIndicators.some(indicator => indicator);
  }

  private setupEventHandlers(): void {
    // License validator events
    this.licenseValidator.on('validation', (result) => {
      this.metrics.licenseValidations++;
      
      if (!result.valid) {
        this.recordEvent({
          type: SecurityEventType.LICENSE_INVALID,
          severity: SecuritySeverity.CRITICAL,
          source: 'LicenseValidator',
          description: `License validation failed: ${result.error}`,
          metadata: { result },
        });
      }
    });

    // Integrity checker events
    this.integrityChecker.on('check', (result) => {
      this.metrics.integrityChecks++;
      
      if (result.status === 'INVALID') {
        this.recordEvent({
          type: SecurityEventType.INTEGRITY_VIOLATION,
          severity: SecuritySeverity.HIGH,
          source: 'IntegrityChecker',
          description: `Integrity check failed for ${result.target}`,
          metadata: { result },
        });
      }
    });

    // Anti-tamper events
    this.antiTamper.on('tamper', (detection) => {
      this.metrics.tamperingAttempts++;
      
      this.recordEvent({
        type: SecurityEventType.TAMPERING_DETECTED,
        severity: SecuritySeverity.CRITICAL,
        source: 'AntiTamper',
        description: `Tampering detected: ${detection.description}`,
        metadata: { detection },
      });
    });

    // Process events
    process.on('SIGTERM', () => this.handleShutdown('SIGTERM'));
    process.on('SIGINT', () => this.handleShutdown('SIGINT'));
    process.on('uncaughtException', (error) => this.handleUncaughtException(error));
    process.on('unhandledRejection', (reason) => this.handleUnhandledRejection(reason));
  }

  private startMonitoring(): void {
    // Heartbeat monitoring
    if (this.config.heartbeatInterval > 0) {
      this.heartbeatInterval = setInterval(() => {
        this.sendHeartbeat().catch(error => {
          this.logger.error('Heartbeat failed', error);
        });
      }, this.config.heartbeatInterval);
    }

    // Integrity monitoring
    this.integrityInterval = setInterval(() => {
      this.integrityChecker.performChecks().catch(error => {
        this.logger.error('Integrity check failed', error);
      });
    }, 60000); // Every minute

    // Tamper monitoring
    this.tamperInterval = setInterval(() => {
      this.antiTamper.performChecks().catch(error => {
        this.logger.error('Tamper check failed', error);
      });
    }, 30000); // Every 30 seconds
  }

  private stopMonitoring(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval);
      this.heartbeatInterval = undefined;
    }

    if (this.integrityInterval) {
      clearInterval(this.integrityInterval);
      this.integrityInterval = undefined;
    }

    if (this.tamperInterval) {
      clearInterval(this.tamperInterval);
      this.tamperInterval = undefined;
    }
  }

  private async sendHeartbeat(): Promise<void> {
    try {
      await this.licenseValidator.sendHeartbeat();
    } catch (error) {
      this.recordEvent({
        type: SecurityEventType.HEARTBEAT_FAILURE,
        severity: SecuritySeverity.MEDIUM,
        source: 'SecurityManager',
        description: `Heartbeat failed: ${error.message}`,
        metadata: { error: error.message },
      });
    }
  }

  private recordEvent(eventData: Omit<SecurityEvent, 'id' | 'timestamp' | 'context'>): void {
    const event: SecurityEvent = {
      id: crypto.randomUUID(),
      timestamp: new Date(),
      context: this.context,
      ...eventData,
    };

    this.events.push(event);
    this.metrics.totalEvents++;
    
    // Update metrics
    this.metrics.eventsByType[event.type] = (this.metrics.eventsByType[event.type] || 0) + 1;
    this.metrics.eventsBySeverity[event.severity] = (this.metrics.eventsBySeverity[event.severity] || 0) + 1;
    this.metrics.lastEventTime = event.timestamp;

    // Create alert for high severity events
    if (event.severity === SecuritySeverity.HIGH || event.severity === SecuritySeverity.CRITICAL) {
      this.createAlert(event);
    }

    // Emit event
    this.emit('securityEvent', event);

    // Log event
    this.logger.logSecurityEvent(event);

    // Cleanup old events (keep last 1000)
    if (this.events.length > 1000) {
      this.events = this.events.slice(-1000);
    }
  }

  private createAlert(event: SecurityEvent): void {
    const alert: SecurityAlert = {
      id: crypto.randomUUID(),
      type: event.type,
      severity: event.severity,
      message: event.description,
      timestamp: event.timestamp,
      acknowledged: false,
      resolved: false,
      metadata: event.metadata,
    };

    this.alerts.push(alert);
    this.emit('securityAlert', alert);

    // Cleanup old alerts (keep last 100)
    if (this.alerts.length > 100) {
      this.alerts = this.alerts.slice(-100);
    }
  }

  private handleShutdown(signal: string): void {
    this.logger.info(`Received ${signal}, initiating graceful shutdown`);
    
    this.recordEvent({
      type: SecurityEventType.SUSPICIOUS_ACTIVITY,
      severity: SecuritySeverity.LOW,
      source: 'SecurityManager',
      description: `Application shutdown initiated by ${signal}`,
      metadata: { signal },
    });

    this.shutdown().finally(() => {
      process.exit(0);
    });
  }

  private handleUncaughtException(error: Error): void {
    this.logger.error('Uncaught exception', error);
    
    this.recordEvent({
      type: SecurityEventType.SUSPICIOUS_ACTIVITY,
      severity: SecuritySeverity.CRITICAL,
      source: 'SecurityManager',
      description: `Uncaught exception: ${error.message}`,
      metadata: { error: error.stack },
    });

    // Give time for logging before exit
    setTimeout(() => process.exit(1), 1000);
  }

  private handleUnhandledRejection(reason: any): void {
    this.logger.error('Unhandled rejection', reason);
    
    this.recordEvent({
      type: SecurityEventType.SUSPICIOUS_ACTIVITY,
      severity: SecuritySeverity.HIGH,
      source: 'SecurityManager',
      description: `Unhandled rejection: ${reason}`,
      metadata: { reason },
    });
  }
}
