"use strict";var i=Object.defineProperty;var n=Object.getOwnPropertyDescriptor;var t=Object.getOwnPropertyNames;var f=Object.prototype.hasOwnProperty;var y=(o,a)=>{for(var r in a)i(o,r,{get:a[r],enumerable:!0})},d=(o,a,r,l)=>{if(a&&typeof a=="object"||typeof a=="function")for(let e of t(a))!f.call(o,e)&&e!==r&&i(o,e,{get:()=>a[e],enumerable:!(l=n(a,e))||l.enumerable});return o};var m=o=>d(i({},"__esModule",{value:!0}),o);var g={};y(g,{default:()=>L});module.exports=m(g);var L=["Carlow","Cavan","Clare","Cork","Donegal","Dublin","Galway","Kerry","Kildare","Kilkenny","Laois","Leitrim","Limerick","Longford","Louth","Mayo","Meath","Monaghan","Offaly","Roscommon","Sligo","Tipperary","Waterford","Westmeath","Wexford","Wicklow"];
