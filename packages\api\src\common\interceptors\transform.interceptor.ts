import {
  Injectable,
  NestInterceptor,
  ExecutionContext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';

export interface Response<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
  path: string;
  method: string;
}

@Injectable()
export class TransformInterceptor<T> implements NestInterceptor<T, Response<T>> {
  intercept(context: ExecutionContext, next: CallHandler): Observable<Response<T>> {
    const request = context.switchToHttp().getRequest();
    
    return next.handle().pipe(
      map(data => {
        // Don't transform if data is already in the expected format
        if (data && typeof data === 'object' && 'success' in data) {
          return data;
        }

        // Don't transform certain responses
        if (this.shouldSkipTransform(request.url, data)) {
          return data;
        }

        return {
          success: true,
          data,
          timestamp: new Date().toISOString(),
          path: request.url,
          method: request.method,
        };
      }),
    );
  }

  private shouldSkipTransform(url: string, data: any): boolean {
    // Skip transformation for certain endpoints
    const skipPaths = [
      '/api/health',
      '/api/metrics',
      '/api/docs',
      '/api/swagger',
    ];

    // Skip if URL matches any skip path
    if (skipPaths.some(path => url.startsWith(path))) {
      return true;
    }

    // Skip if data is a stream or buffer
    if (Buffer.isBuffer(data) || data instanceof ReadableStream) {
      return true;
    }

    // Skip if data is a file download response
    if (data && typeof data === 'object' && data.pipe) {
      return true;
    }

    return false;
  }
}
