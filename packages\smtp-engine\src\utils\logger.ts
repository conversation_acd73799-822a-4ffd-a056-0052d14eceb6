import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import * as path from 'path';
import * as fs from 'fs';

export class Logger {
  private static instance: Logger;
  private logger: winston.Logger;

  private constructor() {
    this.logger = this.createLogger();
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  private createLogger(): winston.Logger {
    const logLevel = process.env.LOG_LEVEL || 'info';
    const logDir = process.env.LOG_DIR || './logs';

    // Ensure log directory exists
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Custom format for logs
    const logFormat = winston.format.combine(
      winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss.SSS',
      }),
      winston.format.errors({ stack: true }),
      winston.format.json(),
      winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
        let log = `${timestamp} [${level.toUpperCase()}] ${message}`;
        
        if (Object.keys(meta).length > 0) {
          log += ` ${JSON.stringify(meta)}`;
        }
        
        if (stack) {
          log += `\n${stack}`;
        }
        
        return log;
      })
    );

    // Console format for development
    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({
        format: 'HH:mm:ss',
      }),
      winston.format.printf(({ timestamp, level, message, ...meta }) => {
        let log = `${timestamp} ${level} ${message}`;
        
        if (Object.keys(meta).length > 0) {
          log += ` ${JSON.stringify(meta, null, 2)}`;
        }
        
        return log;
      })
    );

    const transports: winston.transport[] = [
      // Console transport
      new winston.transports.Console({
        level: logLevel,
        format: consoleFormat,
      }),

      // Error log file
      new DailyRotateFile({
        filename: path.join(logDir, 'error-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        format: logFormat,
        maxSize: '20m',
        maxFiles: '14d',
        zippedArchive: true,
      }),

      // Combined log file
      new DailyRotateFile({
        filename: path.join(logDir, 'combined-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        format: logFormat,
        maxSize: '20m',
        maxFiles: '30d',
        zippedArchive: true,
      }),

      // SMTP specific log file
      new DailyRotateFile({
        filename: path.join(logDir, 'smtp-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        format: logFormat,
        maxSize: '50m',
        maxFiles: '30d',
        zippedArchive: true,
        level: 'info',
      }),
    ];

    return winston.createLogger({
      level: logLevel,
      format: logFormat,
      transports,
      exitOnError: false,
    });
  }

  public info(message: string, meta?: unknown): void {
    this.logger.info(message, meta);
  }

  public error(message: string, error?: Error | unknown): void {
    if (error instanceof Error) {
      this.logger.error(message, { error: error.message, stack: error.stack });
    } else {
      this.logger.error(message, { error });
    }
  }

  public warn(message: string, meta?: unknown): void {
    this.logger.warn(message, meta);
  }

  public debug(message: string, meta?: unknown): void {
    this.logger.debug(message, meta);
  }

  public verbose(message: string, meta?: unknown): void {
    this.logger.verbose(message, meta);
  }

  public silly(message: string, meta?: unknown): void {
    this.logger.silly(message, meta);
  }

  // SMTP specific logging methods
  public smtpConnection(connectionId: string, remoteAddress: string, action: string, meta?: unknown): void {
    this.logger.info(`SMTP Connection [${connectionId}] ${remoteAddress}: ${action}`, meta);
  }

  public smtpTransaction(transactionId: string, action: string, meta?: unknown): void {
    this.logger.info(`SMTP Transaction [${transactionId}]: ${action}`, meta);
  }

  public smtpError(connectionId: string, error: string, meta?: unknown): void {
    this.logger.error(`SMTP Error [${connectionId}]: ${error}`, meta);
  }

  public smtpSecurity(event: string, remoteAddress: string, meta?: unknown): void {
    this.logger.warn(`SMTP Security [${remoteAddress}]: ${event}`, meta);
  }

  public smtpPerformance(operation: string, duration: number, meta?: unknown): void {
    this.logger.info(`SMTP Performance [${operation}]: ${duration}ms`, meta);
  }

  // Get the underlying winston logger for advanced usage
  public getWinstonLogger(): winston.Logger {
    return this.logger;
  }

  // Update log level dynamically
  public setLogLevel(level: string): void {
    this.logger.level = level;
    this.logger.transports.forEach(transport => {
      transport.level = level;
    });
    this.info(`Log level changed to: ${level}`);
  }

  // Create child logger with additional context
  public child(meta: Record<string, unknown>): winston.Logger {
    return this.logger.child(meta);
  }

  // Flush logs (useful for graceful shutdown)
  public async flush(): Promise<void> {
    return new Promise((resolve) => {
      this.logger.on('finish', resolve);
      this.logger.end();
    });
  }
}
