{"name": "@ironrelay/shared", "version": "1.0.0", "description": "Shared types, interfaces, and utilities for IronRelay", "main": "dist/index.js", "types": "dist/index.d.ts", "scripts": {"build": "tsc", "dev": "tsc --watch", "clean": "<PERSON><PERSON><PERSON> dist", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"zod": "^3.22.2", "class-validator": "^0.14.0", "class-transformer": "^0.5.1"}, "devDependencies": {"@types/node": "^20.4.5", "jest": "^29.6.1", "@types/jest": "^29.5.3", "ts-jest": "^29.1.1", "rimraf": "^5.0.1", "typescript": "^5.1.6"}, "jest": {"preset": "ts-jest", "testEnvironment": "node", "roots": ["<rootDir>/src"], "testMatch": ["**/__tests__/**/*.ts", "**/?(*.)+(spec|test).ts"], "collectCoverageFrom": ["src/**/*.ts", "!src/**/*.d.ts", "!src/**/*.test.ts", "!src/**/*.spec.ts"]}}