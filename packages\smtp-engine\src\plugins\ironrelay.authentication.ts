// IronRelay Authentication Plugin
// Handles SMTP authentication with database integration

import * as bcrypt from 'bcrypt';
import { Logger } from '../utils/logger';
import { DatabaseService } from '../services/database.service';
import { MetricsService } from '../services/metrics.service';

interface HarakaConnection {
  uuid: string;
  remote: {
    ip: string;
    host?: string;
  };
  notes: Record<string, any>;
  loginfo: (plugin: any, message: string) => void;
  logwarn: (plugin: any, message: string) => void;
  logerror: (plugin: any, message: string) => void;
}

interface HarakaNext {
  (action?: number, message?: string): void;
  DENY: number;
  DENYSOFT: number;
  DENYDISCONNECT: number;
  OK: number;
  CONT: number;
}

interface AuthCredentials {
  method: string;
  user: string;
  passwd: string;
}

interface HarakaPlugin {
  config: any;
  register: () => void;
  hook_capabilities: (next: HarakaNext, connection: HarakaConnection) => void;
  hook_unrecognized_command: (next: <PERSON><PERSON><PERSON>ext, connection: HarakaConnection, params: string[]) => void;
}

class AuthenticationPlugin implements HarakaPlugin {
  public config: any;
  private logger = Logger.getInstance();
  private database = DatabaseService.getInstance();
  private metrics = MetricsService.getInstance();
  private authRequired = false;
  private supportedMethods: string[] = ['PLAIN', 'LOGIN'];
  private maxAuthAttempts = 3;
  private authAttempts = new Map<string, number>();

  public register(): void {
    const plugin = this;

    // Load configuration
    plugin.loadConfig();

    // Register hooks
    plugin.register_hook('capabilities', 'advertise_auth');
    plugin.register_hook('unrecognized_command', 'auth_command');
    plugin.register_hook('mail', 'check_auth_required');
  }

  private loadConfig(): void {
    try {
      const config = this.config.get('ironrelay.authentication.ini') || {};
      
      this.authRequired = config.required === 'true' || config.required === true;
      this.supportedMethods = config.methods ? config.methods.split(',').map((m: string) => m.trim().toUpperCase()) : ['PLAIN', 'LOGIN'];
      this.maxAuthAttempts = parseInt(config.max_attempts || '3', 10);

      this.logger.info('Authentication configuration loaded', {
        required: this.authRequired,
        methods: this.supportedMethods,
        maxAttempts: this.maxAuthAttempts,
      });
    } catch (error) {
      this.logger.error('Failed to load authentication configuration', error);
    }
  }

  public hook_capabilities(next: HarakaNext, connection: HarakaConnection): void {
    const plugin = this;

    try {
      // Only advertise AUTH if TLS is active or not required
      const tlsActive = connection.notes.tls && connection.notes.tls.enabled;
      const tlsRequired = this.config.get('tls.required') === 'true';

      if (!tlsRequired || tlsActive) {
        // Add AUTH capability
        const authMethods = this.supportedMethods.join(' ');
        connection.capabilities.push(`AUTH ${authMethods}`);
        connection.loginfo(plugin, `Advertising AUTH capability: ${authMethods}`);
      } else {
        connection.loginfo(plugin, 'AUTH capability not advertised - TLS required but not active');
      }

      return next(next.OK);
    } catch (error) {
      connection.logerror(plugin, `Error in capabilities hook: ${error}`);
      return next(next.OK);
    }
  }

  public hook_unrecognized_command(next: HarakaNext, connection: HarakaConnection, params: string[]): void {
    const plugin = this;
    const command = params[0]?.toUpperCase();

    if (command !== 'AUTH') {
      return next(next.CONT);
    }

    try {
      // Check if already authenticated
      if (connection.notes.auth_user) {
        connection.respond(503, 'Already authenticated');
        return next(next.OK);
      }

      // Check TLS requirement
      const tlsActive = connection.notes.tls && connection.notes.tls.enabled;
      const tlsRequired = this.config.get('tls.required') === 'true';

      if (tlsRequired && !tlsActive) {
        connection.respond(538, 'Encryption required for authentication');
        return next(next.OK);
      }

      // Parse AUTH command
      const authParams = params.slice(1);
      if (authParams.length === 0) {
        connection.respond(501, 'Syntax: AUTH mechanism [initial-response]');
        return next(next.OK);
      }

      const mechanism = authParams[0].toUpperCase();
      const initialResponse = authParams[1];

      // Check if mechanism is supported
      if (!this.supportedMethods.includes(mechanism)) {
        connection.respond(504, `Authentication mechanism ${mechanism} not supported`);
        this.metrics.recordAuthAttempt(mechanism, 'failure');
        return next(next.OK);
      }

      // Handle authentication based on mechanism
      this.handleAuthentication(connection, mechanism, initialResponse, next);
      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Authentication error: ${error}`);
      connection.respond(454, 'Temporary authentication failure');
      this.metrics.recordError('authentication', 'plugin_error');
      return next(next.OK);
    }
  }

  public hook_mail(next: HarakaNext, connection: HarakaConnection): void {
    const plugin = this;

    try {
      // Check if authentication is required
      if (this.authRequired && !connection.notes.auth_user) {
        connection.logwarn(plugin, 'Authentication required but not provided');
        this.metrics.recordError('authentication', 'required_not_provided');
        return next(next.DENY, 'Authentication required');
      }

      return next(next.OK);
    } catch (error) {
      connection.logerror(plugin, `Mail hook error: ${error}`);
      return next(next.DENYSOFT, 'Temporary authentication failure');
    }
  }

  private async handleAuthentication(
    connection: HarakaConnection,
    mechanism: string,
    initialResponse: string | undefined,
    next: HarakaNext
  ): Promise<void> {
    const plugin = this;

    try {
      switch (mechanism) {
        case 'PLAIN':
          await this.handlePlainAuth(connection, initialResponse);
          break;
        case 'LOGIN':
          await this.handleLoginAuth(connection, initialResponse);
          break;
        default:
          connection.respond(504, `Authentication mechanism ${mechanism} not implemented`);
          this.metrics.recordAuthAttempt(mechanism, 'failure');
      }
    } catch (error) {
      connection.logerror(plugin, `Authentication handler error: ${error}`);
      connection.respond(454, 'Temporary authentication failure');
      this.metrics.recordError('authentication', 'handler_error');
    }
  }

  private async handlePlainAuth(connection: HarakaConnection, initialResponse?: string): Promise<void> {
    const plugin = this;

    if (initialResponse) {
      // Initial response provided
      await this.processPlainAuth(connection, initialResponse);
    } else {
      // Request credentials
      connection.respond(334, '');
      
      // Set up data handler for credentials
      connection.notes.auth_state = {
        mechanism: 'PLAIN',
        step: 'credentials',
      };
    }
  }

  private async handleLoginAuth(connection: HarakaConnection, initialResponse?: string): Promise<void> {
    const plugin = this;

    if (initialResponse) {
      // Username provided in initial response
      connection.notes.auth_state = {
        mechanism: 'LOGIN',
        step: 'password',
        username: Buffer.from(initialResponse, 'base64').toString(),
      };
      connection.respond(334, Buffer.from('Password:').toString('base64'));
    } else {
      // Request username
      connection.notes.auth_state = {
        mechanism: 'LOGIN',
        step: 'username',
      };
      connection.respond(334, Buffer.from('Username:').toString('base64'));
    }
  }

  private async processPlainAuth(connection: HarakaConnection, credentials: string): Promise<void> {
    const plugin = this;

    try {
      // Decode base64 credentials
      const decoded = Buffer.from(credentials, 'base64').toString();
      const parts = decoded.split('\0');

      if (parts.length !== 3) {
        connection.respond(535, 'Authentication failed');
        this.metrics.recordAuthAttempt('PLAIN', 'failure');
        await this.logAuthAttempt(connection, '', false, 'Invalid credentials format');
        return;
      }

      const [authzid, username, password] = parts;
      await this.authenticateUser(connection, username, password, 'PLAIN');

    } catch (error) {
      connection.logerror(plugin, `PLAIN auth processing error: ${error}`);
      connection.respond(454, 'Temporary authentication failure');
      this.metrics.recordError('authentication', 'plain_processing_error');
    }
  }

  private async authenticateUser(
    connection: HarakaConnection,
    username: string,
    password: string,
    method: string
  ): Promise<void> {
    const plugin = this;
    const remoteIP = connection.remote.ip;

    try {
      // Check rate limiting
      const attemptKey = `${remoteIP}:${username}`;
      const attempts = this.authAttempts.get(attemptKey) || 0;

      if (attempts >= this.maxAuthAttempts) {
        connection.respond(535, 'Too many authentication attempts');
        this.metrics.recordAuthAttempt(method, 'failure');
        await this.logAuthAttempt(connection, username, false, 'Too many attempts');
        return;
      }

      // Get user from database
      const user = await this.database.getSMTPUser(username) as any;

      if (!user) {
        connection.respond(535, 'Authentication failed');
        this.metrics.recordAuthAttempt(method, 'failure');
        this.authAttempts.set(attemptKey, attempts + 1);
        await this.logAuthAttempt(connection, username, false, 'User not found');
        return;
      }

      // Check if user is active
      if (!user.isActive || !user.enabled) {
        connection.respond(535, 'Account disabled');
        this.metrics.recordAuthAttempt(method, 'failure');
        this.authAttempts.set(attemptKey, attempts + 1);
        await this.logAuthAttempt(connection, username, false, 'Account disabled');
        return;
      }

      // Verify password
      const passwordValid = await bcrypt.compare(password, user.passwordHash);

      if (!passwordValid) {
        connection.respond(535, 'Authentication failed');
        this.metrics.recordAuthAttempt(method, 'failure');
        this.authAttempts.set(attemptKey, attempts + 1);
        await this.logAuthAttempt(connection, username, false, 'Invalid password');
        return;
      }

      // Check IP restrictions
      if (user.allowedIPs && user.allowedIPs.length > 0) {
        if (!user.allowedIPs.includes(remoteIP)) {
          connection.respond(535, 'Authentication failed');
          this.metrics.recordAuthAttempt(method, 'failure');
          await this.logAuthAttempt(connection, username, false, 'IP not allowed');
          return;
        }
      }

      // Authentication successful
      connection.notes.auth_user = {
        id: user.id,
        username: user.username,
        email: user.email,
        allowedDomains: user.allowedDomains,
        customRateLimit: user.customRateLimit,
        customRateLimitWindow: user.customRateLimitWindow,
      };

      connection.notes.auth_method = method;
      connection.respond(235, 'Authentication successful');
      
      this.metrics.recordAuthAttempt(method, 'success');
      this.authAttempts.delete(attemptKey); // Clear failed attempts
      
      connection.loginfo(plugin, `User ${username} authenticated successfully using ${method}`);
      
      // Update user login statistics
      await this.database.updateSMTPUserLogin(user.id);
      await this.logAuthAttempt(connection, username, true, 'Authentication successful');

    } catch (error) {
      connection.logerror(plugin, `User authentication error: ${error}`);
      connection.respond(454, 'Temporary authentication failure');
      this.metrics.recordError('authentication', 'user_auth_error');
    }
  }

  private async logAuthAttempt(
    connection: HarakaConnection,
    username: string,
    success: boolean,
    reason: string
  ): Promise<void> {
    try {
      await this.database.createSecurityEvent({
        type: success ? 'AUTH_SUCCESS' : 'AUTH_FAILURE',
        severity: success ? 'LOW' : 'MEDIUM',
        source: 'smtp_authentication',
        description: `SMTP authentication ${success ? 'successful' : 'failed'}: ${reason}`,
        ipAddress: connection.remote.ip,
        userAgent: `SMTP-${connection.notes.auth_method || 'unknown'}`,
        metadata: {
          connectionId: connection.uuid,
          username,
          method: connection.notes.auth_method,
          hostname: connection.remote.host,
        },
        blocked: !success,
        action: success ? 'ALLOW' : 'DENY',
      });
    } catch (error) {
      this.logger.error('Failed to log authentication attempt', error);
    }
  }

  // Method to reload configuration
  public async reloadConfig(): Promise<void> {
    try {
      this.logger.info('Reloading authentication configuration...');
      this.loadConfig();
      await this.loadConfigFromDatabase();
      this.logger.info('Authentication configuration reloaded successfully');
    } catch (error) {
      this.logger.error('Failed to reload authentication configuration', error);
      throw error;
    }
  }

  private async loadConfigFromDatabase(): Promise<void> {
    try {
      const smtpConfig = await this.database.getSMTPConfig() as any;
      
      if (smtpConfig) {
        this.authRequired = smtpConfig.authRequired;
        this.supportedMethods = smtpConfig.authMethods || ['PLAIN', 'LOGIN'];
      }
    } catch (error) {
      this.logger.error('Failed to load authentication configuration from database', error);
    }
  }

  // Utility methods
  public isAuthRequired(): boolean {
    return this.authRequired;
  }

  public getSupportedMethods(): string[] {
    return [...this.supportedMethods];
  }

  public getAuthAttempts(ip: string, username: string): number {
    return this.authAttempts.get(`${ip}:${username}`) || 0;
  }

  public clearAuthAttempts(ip: string, username: string): void {
    this.authAttempts.delete(`${ip}:${username}`);
  }
}

// Export for Haraka plugin system
module.exports = new AuthenticationPlugin();
