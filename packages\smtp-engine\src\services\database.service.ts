import { PrismaClient } from '@prisma/client';
import { Logger } from '../utils/logger';
import { Config } from '../config/config';

export class DatabaseService {
  private static instance: DatabaseService;
  private prisma: PrismaClient;
  private logger = Logger.getInstance();
  private config = Config.getInstance();

  private constructor() {
    this.prisma = new PrismaClient({
      datasources: {
        db: {
          url: this.config.get<string>('database.url'),
        },
      },
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
    });

    // Set up Prisma logging
    this.setupPrismaLogging();
  }

  public static getInstance(): DatabaseService {
    if (!DatabaseService.instance) {
      DatabaseService.instance = new DatabaseService();
    }
    return DatabaseService.instance;
  }

  private setupPrismaLogging(): void {
    this.prisma.$on('query', (e) => {
      this.logger.debug('Database Query', {
        query: e.query,
        params: e.params,
        duration: e.duration,
        target: e.target,
      });
    });

    this.prisma.$on('error', (e) => {
      this.logger.error('Database Error', {
        message: e.message,
        target: e.target,
      });
    });

    this.prisma.$on('info', (e) => {
      this.logger.info('Database Info', {
        message: e.message,
        target: e.target,
      });
    });

    this.prisma.$on('warn', (e) => {
      this.logger.warn('Database Warning', {
        message: e.message,
        target: e.target,
      });
    });
  }

  public async connect(): Promise<void> {
    try {
      await this.prisma.$connect();
      this.logger.info('Database connected successfully');
    } catch (error) {
      this.logger.error('Failed to connect to database', error);
      throw error;
    }
  }

  public async disconnect(): Promise<void> {
    try {
      await this.prisma.$disconnect();
      this.logger.info('Database disconnected successfully');
    } catch (error) {
      this.logger.error('Failed to disconnect from database', error);
      throw error;
    }
  }

  public async healthCheck(): Promise<boolean> {
    try {
      await this.prisma.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('Database health check failed', error);
      return false;
    }
  }

  // Get Prisma client for direct access
  public getClient(): PrismaClient {
    return this.prisma;
  }

  // SMTP Configuration methods
  public async getSMTPConfig(): Promise<unknown> {
    try {
      const config = await this.prisma.sMTPConfig.findFirst({
        where: { enabled: true },
        orderBy: { updatedAt: 'desc' },
      });
      return config;
    } catch (error) {
      this.logger.error('Failed to get SMTP configuration', error);
      throw error;
    }
  }

  // Domain routing methods
  public async getDomainRoutes(): Promise<unknown[]> {
    try {
      const routes = await this.prisma.domainRoute.findMany({
        where: { enabled: true },
        orderBy: { priority: 'asc' },
      });
      return routes;
    } catch (error) {
      this.logger.error('Failed to get domain routes', error);
      throw error;
    }
  }

  public async getDomainRoute(domain: string): Promise<unknown | null> {
    try {
      // First try exact match
      let route = await this.prisma.domainRoute.findFirst({
        where: {
          domain,
          enabled: true,
        },
      });

      // If no exact match, try wildcard
      if (!route) {
        route = await this.prisma.domainRoute.findFirst({
          where: {
            domain: '*',
            enabled: true,
          },
          orderBy: { priority: 'desc' },
        });
      }

      return route;
    } catch (error) {
      this.logger.error('Failed to get domain route', error);
      throw error;
    }
  }

  // SMTP User methods
  public async getSMTPUser(username: string): Promise<unknown | null> {
    try {
      const user = await this.prisma.sMTPUser.findFirst({
        where: {
          username,
          enabled: true,
          isActive: true,
        },
      });
      return user;
    } catch (error) {
      this.logger.error('Failed to get SMTP user', error);
      throw error;
    }
  }

  public async updateSMTPUserLogin(userId: string): Promise<void> {
    try {
      await this.prisma.sMTPUser.update({
        where: { id: userId },
        data: {
          lastLogin: new Date(),
          loginCount: {
            increment: 1,
          },
        },
      });
    } catch (error) {
      this.logger.error('Failed to update SMTP user login', error);
      throw error;
    }
  }

  // Email transaction methods
  public async createEmailTransaction(data: {
    messageId: string;
    senderIP: string;
    senderHostname?: string;
    mailFrom: string;
    rcptTo: string[];
    subject?: string;
    messageSize: number;
    authenticatedUserId?: string;
    authMethod?: string;
    status: string;
    processingTime?: number;
    headers?: Record<string, unknown>;
    metadata?: Record<string, unknown>;
  }): Promise<string> {
    try {
      const transaction = await this.prisma.emailTransaction.create({
        data: {
          ...data,
          receivedAt: new Date(),
        },
      });
      return transaction.id;
    } catch (error) {
      this.logger.error('Failed to create email transaction', error);
      throw error;
    }
  }

  public async updateEmailTransaction(
    id: string,
    data: {
      status?: string;
      statusMessage?: string;
      processedAt?: Date;
      deliveredAt?: Date;
      routeUsed?: string;
      destinationHost?: string;
      destinationPort?: number;
      spfResult?: string;
      dkimResult?: string;
      dmarcResult?: string;
      processingTime?: number;
      queueTime?: number;
      deliveryTime?: number;
      errorCode?: string;
      errorMessage?: string;
      retryCount?: number;
    }
  ): Promise<void> {
    try {
      await this.prisma.emailTransaction.update({
        where: { id },
        data,
      });
    } catch (error) {
      this.logger.error('Failed to update email transaction', error);
      throw error;
    }
  }

  // Queue management methods
  public async createQueuedEmail(data: {
    transactionId: string;
    messageData: string;
    maxAttempts: number;
    nextAttempt: Date;
    priority: number;
  }): Promise<string> {
    try {
      const queuedEmail = await this.prisma.queuedEmail.create({
        data,
      });
      return queuedEmail.id;
    } catch (error) {
      this.logger.error('Failed to create queued email', error);
      throw error;
    }
  }

  public async getQueuedEmails(limit: number = 10): Promise<unknown[]> {
    try {
      const queuedEmails = await this.prisma.queuedEmail.findMany({
        where: {
          status: 'PENDING',
          nextAttempt: {
            lte: new Date(),
          },
        },
        orderBy: [
          { priority: 'asc' },
          { queuedAt: 'asc' },
        ],
        take: limit,
        include: {
          transaction: true,
        },
      });
      return queuedEmails;
    } catch (error) {
      this.logger.error('Failed to get queued emails', error);
      throw error;
    }
  }

  public async updateQueuedEmail(
    id: string,
    data: {
      status?: string;
      attempts?: number;
      nextAttempt?: Date;
      lastAttemptAt?: Date;
      lastError?: string;
    }
  ): Promise<void> {
    try {
      await this.prisma.queuedEmail.update({
        where: { id },
        data,
      });
    } catch (error) {
      this.logger.error('Failed to update queued email', error);
      throw error;
    }
  }

  // Security event logging
  public async createSecurityEvent(data: {
    type: string;
    severity: string;
    source: string;
    description: string;
    userId?: string;
    sessionId?: string;
    ipAddress?: string;
    userAgent?: string;
    metadata?: Record<string, unknown>;
    blocked: boolean;
    action?: string;
  }): Promise<string> {
    try {
      const event = await this.prisma.securityEvent.create({
        data,
      });
      return event.id;
    } catch (error) {
      this.logger.error('Failed to create security event', error);
      throw error;
    }
  }

  // Audit logging
  public async createAuditLog(data: {
    action: string;
    resource: string;
    resourceId?: string;
    userId: string;
    userEmail: string;
    ipAddress: string;
    userAgent?: string;
    changes?: Record<string, unknown>;
    metadata?: Record<string, unknown>;
  }): Promise<string> {
    try {
      const auditLog = await this.prisma.auditLog.create({
        data,
      });
      return auditLog.id;
    } catch (error) {
      this.logger.error('Failed to create audit log', error);
      throw error;
    }
  }
}
