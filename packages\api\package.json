{"name": "@ironrelay/api", "version": "1.0.0", "description": "IronRelay Backend API Service", "main": "dist/main.js", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "migrate": "npm run build && npx prisma migrate deploy", "migrate:dev": "npx prisma migrate dev", "migrate:reset": "npx prisma migrate reset", "db:generate": "npx prisma generate", "db:studio": "npx prisma studio", "db:seed": "ts-node prisma/seed.ts"}, "dependencies": {"@nestjs/common": "^10.0.0", "@nestjs/core": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/jwt": "^10.1.0", "@nestjs/passport": "^10.0.0", "@nestjs/swagger": "^7.1.8", "@nestjs/throttler": "^4.2.1", "@nestjs/schedule": "^3.0.1", "@prisma/client": "^5.1.1", "prisma": "^5.1.1", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "bcrypt": "^5.1.0", "class-validator": "^0.14.0", "class-transformer": "^0.5.1", "helmet": "^7.0.0", "compression": "^1.7.4", "express-rate-limit": "^6.8.1", "winston": "^3.10.0", "winston-daily-rotate-file": "^4.7.1", "prom-client": "^14.2.0", "nodemailer": "^6.9.4", "multer": "^1.4.5-lts.1", "joi": "^17.9.2", "moment": "^2.29.4", "lodash": "^4.17.21", "uuid": "^9.0.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.1"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/node": "^20.3.1", "@types/supertest": "^2.0.12", "@types/bcrypt": "^5.0.0", "@types/passport-jwt": "^3.0.9", "@types/passport-local": "^1.0.35", "@types/multer": "^1.4.7", "@types/nodemailer": "^6.4.9", "@types/lodash": "^4.14.195", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}