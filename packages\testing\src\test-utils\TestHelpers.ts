import { spawn, ChildProcess } from 'child_process';
import { createConnection, Connection } from 'typeorm';
import Redis from 'ioredis';
import nodemailer from 'nodemailer';
import { SMTPServer } from 'smtp-server';
import { simpleParser } from 'mailparser';
import axios, { AxiosInstance } from 'axios';
import WebSocket from 'ws';

export class TestHelpers {
  private static processes: ChildProcess[] = [];
  private static connections: Connection[] = [];
  private static redisClients: Redis[] = [];
  private static smtpServers: SMTPServer[] = [];

  // Database helpers
  static async createTestDatabase(name: string = 'test'): Promise<Connection> {
    const connection = await createConnection({
      type: 'postgres',
      host: process.env.DB_HOST || 'localhost',
      port: parseInt(process.env.DB_PORT || '5432'),
      username: process.env.DB_USER || 'test',
      password: process.env.DB_PASSWORD || 'test',
      database: `ironrelay_${name}`,
      synchronize: true,
      dropSchema: true,
      logging: false,
    });

    this.connections.push(connection);
    return connection;
  }

  static async cleanupDatabase(connection: Connection): Promise<void> {
    if (connection.isConnected) {
      await connection.dropDatabase();
      await connection.close();
    }
  }

  static async seedDatabase(connection: Connection, data: any): Promise<void> {
    const queryRunner = connection.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();

    try {
      // Insert test data
      for (const [entityName, records] of Object.entries(data)) {
        if (Array.isArray(records)) {
          for (const record of records) {
            await queryRunner.manager.save(entityName, record);
          }
        }
      }

      await queryRunner.commitTransaction();
    } catch (error) {
      await queryRunner.rollbackTransaction();
      throw error;
    } finally {
      await queryRunner.release();
    }
  }

  // Redis helpers
  static async createTestRedis(db: number = 1): Promise<Redis> {
    const redis = new Redis({
      host: process.env.REDIS_HOST || 'localhost',
      port: parseInt(process.env.REDIS_PORT || '6379'),
      db,
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 1,
    });

    await redis.flushdb();
    this.redisClients.push(redis);
    return redis;
  }

  static async cleanupRedis(redis: Redis): Promise<void> {
    await redis.flushdb();
    await redis.quit();
  }

  // SMTP test server helpers
  static async createTestSMTPServer(port: number = 2525): Promise<{
    server: SMTPServer;
    messages: any[];
    port: number;
  }> {
    const messages: any[] = [];

    const server = new SMTPServer({
      secure: false,
      authOptional: true,
      onData(stream, session, callback) {
        simpleParser(stream, (err, parsed) => {
          if (err) {
            callback(err);
          } else {
            messages.push({
              session,
              parsed,
              timestamp: new Date(),
            });
            callback();
          }
        });
      },
      onAuth(auth, session, callback) {
        // Accept all authentication for testing
        callback(null, { user: auth.username });
      },
    });

    await new Promise<void>((resolve, reject) => {
      server.listen(port, (err?: Error) => {
        if (err) reject(err);
        else resolve();
      });
    });

    this.smtpServers.push(server);

    return { server, messages, port };
  }

  static async stopSMTPServer(server: SMTPServer): Promise<void> {
    return new Promise((resolve) => {
      server.close(resolve);
    });
  }

  // Email sending helpers
  static async sendTestEmail(options: {
    to: string;
    from?: string;
    subject?: string;
    text?: string;
    html?: string;
    smtpConfig?: any;
  }): Promise<any> {
    const transporter = nodemailer.createTransporter({
      host: options.smtpConfig?.host || 'localhost',
      port: options.smtpConfig?.port || 2525,
      secure: false,
      auth: options.smtpConfig?.auth,
    });

    const mailOptions = {
      from: options.from || '<EMAIL>',
      to: options.to,
      subject: options.subject || 'Test Email',
      text: options.text || 'This is a test email',
      html: options.html,
    };

    return await transporter.sendMail(mailOptions);
  }

  // HTTP client helpers
  static createAPIClient(baseURL: string = 'http://localhost:3000'): AxiosInstance {
    return axios.create({
      baseURL,
      timeout: 10000,
      validateStatus: () => true, // Don't throw on HTTP errors
    });
  }

  static async waitForAPI(url: string, timeout: number = 30000): Promise<void> {
    const start = Date.now();
    const client = this.createAPIClient();

    while (Date.now() - start < timeout) {
      try {
        const response = await client.get('/health');
        if (response.status === 200) {
          return;
        }
      } catch (error) {
        // Continue waiting
      }
      await this.sleep(1000);
    }

    throw new Error(`API not ready after ${timeout}ms`);
  }

  // WebSocket helpers
  static async createWebSocketClient(url: string): Promise<WebSocket> {
    return new Promise((resolve, reject) => {
      const ws = new WebSocket(url);
      
      ws.on('open', () => resolve(ws));
      ws.on('error', reject);
      
      setTimeout(() => reject(new Error('WebSocket connection timeout')), 5000);
    });
  }

  // Process helpers
  static async startProcess(command: string, args: string[] = [], options: any = {}): Promise<ChildProcess> {
    const process = spawn(command, args, {
      stdio: 'pipe',
      ...options,
    });

    this.processes.push(process);
    return process;
  }

  static async stopProcess(process: ChildProcess): Promise<void> {
    return new Promise((resolve) => {
      if (process.killed) {
        resolve();
        return;
      }

      process.on('exit', () => resolve());
      process.kill('SIGTERM');

      // Force kill after 5 seconds
      setTimeout(() => {
        if (!process.killed) {
          process.kill('SIGKILL');
        }
      }, 5000);
    });
  }

  // Utility helpers
  static async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  static async waitFor(
    condition: () => boolean | Promise<boolean>,
    timeout: number = 10000,
    interval: number = 100
  ): Promise<void> {
    const start = Date.now();

    while (Date.now() - start < timeout) {
      if (await condition()) {
        return;
      }
      await this.sleep(interval);
    }

    throw new Error(`Condition not met within ${timeout}ms`);
  }

  static async retry<T>(
    fn: () => Promise<T>,
    maxAttempts: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxAttempts; attempt++) {
      try {
        return await fn();
      } catch (error) {
        lastError = error as Error;
        if (attempt < maxAttempts) {
          await this.sleep(delay);
        }
      }
    }

    throw lastError!;
  }

  // Performance helpers
  static async measurePerformance<T>(fn: () => Promise<T>): Promise<{
    result: T;
    duration: number;
    memoryUsage: NodeJS.MemoryUsage;
  }> {
    const startTime = process.hrtime.bigint();
    const startMemory = process.memoryUsage();

    const result = await fn();

    const endTime = process.hrtime.bigint();
    const endMemory = process.memoryUsage();

    const duration = Number(endTime - startTime) / 1000000; // Convert to milliseconds

    return {
      result,
      duration,
      memoryUsage: {
        rss: endMemory.rss - startMemory.rss,
        heapTotal: endMemory.heapTotal - startMemory.heapTotal,
        heapUsed: endMemory.heapUsed - startMemory.heapUsed,
        external: endMemory.external - startMemory.external,
        arrayBuffers: endMemory.arrayBuffers - startMemory.arrayBuffers,
      },
    };
  }

  static async loadTest(
    fn: () => Promise<any>,
    concurrency: number = 10,
    duration: number = 10000
  ): Promise<{
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    averageResponseTime: number;
    requestsPerSecond: number;
    errors: Error[];
  }> {
    const startTime = Date.now();
    const endTime = startTime + duration;
    const errors: Error[] = [];
    let totalRequests = 0;
    let successfulRequests = 0;
    let failedRequests = 0;
    let totalResponseTime = 0;

    const workers = Array.from({ length: concurrency }, async () => {
      while (Date.now() < endTime) {
        const requestStart = Date.now();
        totalRequests++;

        try {
          await fn();
          successfulRequests++;
        } catch (error) {
          failedRequests++;
          errors.push(error as Error);
        }

        totalResponseTime += Date.now() - requestStart;
      }
    });

    await Promise.all(workers);

    const actualDuration = Date.now() - startTime;

    return {
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime: totalResponseTime / totalRequests,
      requestsPerSecond: (totalRequests / actualDuration) * 1000,
      errors,
    };
  }

  // Cleanup helpers
  static async cleanup(): Promise<void> {
    // Stop all processes
    await Promise.all(this.processes.map(process => this.stopProcess(process)));
    this.processes = [];

    // Close all database connections
    await Promise.all(this.connections.map(conn => this.cleanupDatabase(conn)));
    this.connections = [];

    // Close all Redis clients
    await Promise.all(this.redisClients.map(redis => this.cleanupRedis(redis)));
    this.redisClients = [];

    // Stop all SMTP servers
    await Promise.all(this.smtpServers.map(server => this.stopSMTPServer(server)));
    this.smtpServers = [];
  }

  // File helpers
  static async createTempFile(content: string, extension: string = '.txt'): Promise<string> {
    const fs = require('fs');
    const path = require('path');
    const os = require('os');

    const tempDir = os.tmpdir();
    const fileName = `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}${extension}`;
    const filePath = path.join(tempDir, fileName);

    fs.writeFileSync(filePath, content);
    return filePath;
  }

  static async deleteTempFile(filePath: string): Promise<void> {
    const fs = require('fs');
    if (fs.existsSync(filePath)) {
      fs.unlinkSync(filePath);
    }
  }

  // Network helpers
  static async findFreePort(startPort: number = 3000): Promise<number> {
    const net = require('net');

    return new Promise((resolve, reject) => {
      const server = net.createServer();
      
      server.listen(startPort, () => {
        const port = server.address()?.port;
        server.close(() => resolve(port));
      });

      server.on('error', (err: any) => {
        if (err.code === 'EADDRINUSE') {
          resolve(this.findFreePort(startPort + 1));
        } else {
          reject(err);
        }
      });
    });
  }

  // Assertion helpers
  static assertEmailFormat(email: string): void {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      throw new Error(`Invalid email format: ${email}`);
    }
  }

  static assertDateRange(date: Date, start: Date, end: Date): void {
    if (date < start || date > end) {
      throw new Error(`Date ${date.toISOString()} is not between ${start.toISOString()} and ${end.toISOString()}`);
    }
  }

  static assertResponseTime(duration: number, maxDuration: number): void {
    if (duration > maxDuration) {
      throw new Error(`Response time ${duration}ms exceeds maximum ${maxDuration}ms`);
    }
  }
}

// Global cleanup on process exit
process.on('exit', () => {
  TestHelpers.cleanup().catch(console.error);
});

process.on('SIGINT', () => {
  TestHelpers.cleanup().then(() => process.exit(0)).catch(() => process.exit(1));
});

process.on('SIGTERM', () => {
  TestHelpers.cleanup().then(() => process.exit(0)).catch(() => process.exit(1));
});
