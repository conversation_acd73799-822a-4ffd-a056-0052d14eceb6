{"version": 3, "file": "userAgentPlatform.js", "sourceRoot": "", "sources": ["../../../src/util/userAgentPlatform.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC;AAElC,OAAO,KAAK,EAAE,MAAM,SAAS,CAAC;AAC9B,OAAO,KAAK,OAAO,MAAM,cAAc,CAAC;AAUxC;;GAEG;AACH,MAAM,UAAU,aAAa;IAC3B,OAAO,YAAY,CAAC;AACtB,CAAC;AAED;;GAEG;AACH,MAAM,CAAC,KAAK,UAAU,uBAAuB,CAAC,GAAwB;IACpE,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,CAAC;QAChC,MAAM,QAAQ,GAAG,OAAO,CAAC,QAAoC,CAAC;QAC9D,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC;YACjB,GAAG,CAAC,GAAG,CAAC,KAAK,EAAE,QAAQ,CAAC,GAAG,CAAC,CAAC;QAC/B,CAAC;aAAM,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzB,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC;aAAM,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;YACzB,GAAG,CAAC,GAAG,CAAC,MAAM,EAAE,QAAQ,CAAC,IAAI,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;IAED,GAAG,CAAC,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC;AAC/D,CAAC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport * as os from \"node:os\";\nimport * as process from \"node:process\";\n\n/**\n * @internal\n */\ninterface ExtendedPlatformVersions extends NodeJS.ProcessVersions {\n  bun?: string;\n  deno?: string;\n}\n\n/**\n * @internal\n */\nexport function getHeaderName(): string {\n  return \"User-Agent\";\n}\n\n/**\n * @internal\n */\nexport async function setPlatformSpecificData(map: Map<string, string>): Promise<void> {\n  if (process && process.versions) {\n    const versions = process.versions as ExtendedPlatformVersions;\n    if (versions.bun) {\n      map.set(\"Bun\", versions.bun);\n    } else if (versions.deno) {\n      map.set(\"Deno\", versions.deno);\n    } else if (versions.node) {\n      map.set(\"Node\", versions.node);\n    }\n  }\n\n  map.set(\"OS\", `(${os.arch()}-${os.type()}-${os.release()})`);\n}\n"]}