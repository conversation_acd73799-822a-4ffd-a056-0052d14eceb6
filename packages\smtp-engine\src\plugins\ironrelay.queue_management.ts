// IronRelay Queue Management Plugin
// Handles email queuing, retry logic, and dead letter handling

import { Logger } from '../utils/logger';
import { DatabaseService } from '../services/database.service';
import { MetricsService } from '../services/metrics.service';

interface HarakaConnection {
  uuid: string;
  remote: {
    ip: string;
    host?: string;
  };
  notes: Record<string, any>;
  transaction?: HarakaTransaction;
  loginfo: (plugin: any, message: string) => void;
  logwarn: (plugin: any, message: string) => void;
  logerror: (plugin: any, message: string) => void;
}

interface HarakaTransaction {
  uuid: string;
  mail_from: {
    user: string;
    host: string;
    original: string;
  };
  rcpt_to: Array<{
    user: string;
    host: string;
    original: string;
  }>;
  message_stream: {
    get_data: () => Buffer;
  };
  notes: Record<string, any>;
}

interface HarakaNext {
  (action?: number, message?: string): void;
  DENY: number;
  DENYSOFT: number;
  DENYDISCONNECT: number;
  OK: number;
  CONT: number;
}

interface HarakaPlugin {
  config: any;
  register: () => void;
  hook_queue: (next: HarakaNext, connection: HarakaConnection) => void;
  hook_send_email: (next: HarakaNext, hmail: any) => void;
  hook_delivered: (next: HarakaNext, hmail: any, params: any[]) => void;
  hook_bounce: (next: HarakaNext, hmail: any, error: string) => void;
}

class QueueManagementPlugin implements HarakaPlugin {
  public config: any;
  private logger = Logger.getInstance();
  private database = DatabaseService.getInstance();
  private metrics = MetricsService.getInstance();
  
  // Configuration
  private enabled = true;
  private maxRetries = 3;
  private retryDelay = 300; // seconds
  private maxAge = 86400; // seconds (24 hours)
  private processingInterval?: NodeJS.Timeout;
  private processingActive = false;

  public register(): void {
    const plugin = this;

    // Load configuration
    plugin.loadConfig();

    // Register hooks
    plugin.register_hook('queue', 'queue_email');
    plugin.register_hook('send_email', 'process_queue');
    plugin.register_hook('delivered', 'handle_delivered');
    plugin.register_hook('bounce', 'handle_bounce');

    // Start queue processing
    if (this.enabled) {
      this.startQueueProcessing();
    }
  }

  private loadConfig(): void {
    try {
      const config = this.config.get('ironrelay.queue_management.ini') || {};
      
      this.enabled = config.enabled !== 'false';
      this.maxRetries = parseInt(config.retry_attempts || '3', 10);
      this.retryDelay = parseInt(config.retry_delay || '300', 10);
      this.maxAge = parseInt(config.max_age || '86400', 10);

      this.logger.info('Queue management configuration loaded', {
        enabled: this.enabled,
        maxRetries: this.maxRetries,
        retryDelay: this.retryDelay,
        maxAge: this.maxAge,
      });
    } catch (error) {
      this.logger.error('Failed to load queue management configuration', error);
    }
  }

  public hook_queue(next: HarakaNext, connection: HarakaConnection): void {
    const plugin = this;
    const transaction = connection.transaction;

    if (!this.enabled || !transaction) {
      return next(next.OK);
    }

    try {
      // Queue the email for processing
      this.queueEmail(connection, transaction).then(() => {
        connection.loginfo(plugin, `Email queued for transaction ${transaction.uuid}`);
        this.metrics.recordMessage('queued');
        return next(next.OK);
      }).catch(error => {
        connection.logerror(plugin, `Failed to queue email: ${error}`);
        this.metrics.recordError('queue', 'queue_failed');
        return next(next.DENYSOFT, 'Temporary queueing failure');
      });

    } catch (error) {
      connection.logerror(plugin, `Queue management error: ${error}`);
      this.logger.error('Queue management plugin error', error);
      return next(next.DENYSOFT, 'Temporary queueing failure');
    }
  }

  public hook_send_email(next: HarakaNext, hmail: any): void {
    const plugin = this;

    try {
      // This hook is called when Haraka is about to send an email
      // We can modify the delivery behavior here
      
      if (hmail.notes && hmail.notes.queue_info) {
        const queueInfo = hmail.notes.queue_info;
        
        // Update queue status to processing
        this.updateQueueStatus(queueInfo.queueId, 'PROCESSING').catch(error => {
          this.logger.error('Failed to update queue status to processing', error);
        });
        
        this.logger.debug(`Processing queued email: ${queueInfo.queueId}`);
      }

      return next(next.OK);

    } catch (error) {
      this.logger.error('Send email hook error', error);
      return next(next.OK);
    }
  }

  public hook_delivered(next: HarakaNext, hmail: any, params: any[]): void {
    const plugin = this;

    try {
      if (hmail.notes && hmail.notes.queue_info) {
        const queueInfo = hmail.notes.queue_info;
        
        // Update queue status to delivered
        this.updateQueueStatus(queueInfo.queueId, 'DELIVERED').then(() => {
          this.logger.info(`Email delivered successfully: ${queueInfo.queueId}`);
          this.metrics.recordMessage('delivered');
        }).catch(error => {
          this.logger.error('Failed to update queue status to delivered', error);
        });
      }

      return next(next.OK);

    } catch (error) {
      this.logger.error('Delivered hook error', error);
      return next(next.OK);
    }
  }

  public hook_bounce(next: HarakaNext, hmail: any, error: string): void {
    const plugin = this;

    try {
      if (hmail.notes && hmail.notes.queue_info) {
        const queueInfo = hmail.notes.queue_info;
        
        // Handle bounce - retry or move to dead letter
        this.handleBounce(queueInfo.queueId, error, hmail.num_failures || 0).then(() => {
          this.logger.warn(`Email bounced: ${queueInfo.queueId}, error: ${error}`);
          this.metrics.recordMessage('failed');
        }).catch(err => {
          this.logger.error('Failed to handle bounce', err);
        });
      }

      return next(next.OK);

    } catch (err) {
      this.logger.error('Bounce hook error', err);
      return next(next.OK);
    }
  }

  private async queueEmail(connection: HarakaConnection, transaction: HarakaTransaction): Promise<void> {
    try {
      // Get message data
      const messageData = transaction.message_stream.get_data();
      const messageBase64 = messageData.toString('base64');

      // Calculate next attempt time
      const nextAttempt = new Date(Date.now() + (this.retryDelay * 1000));

      // Create queue entry
      const queueId = await this.database.createQueuedEmail({
        transactionId: transaction.uuid,
        messageData: messageBase64,
        maxAttempts: this.maxRetries,
        nextAttempt,
        priority: this.calculatePriority(connection, transaction),
      });

      // Store queue info in transaction notes
      transaction.notes.queue_info = {
        queueId,
        attempts: 0,
        maxAttempts: this.maxRetries,
        nextAttempt,
      };

      this.logger.debug(`Email queued with ID: ${queueId}`);

    } catch (error) {
      this.logger.error('Failed to queue email', error);
      throw error;
    }
  }

  private calculatePriority(connection: HarakaConnection, transaction: HarakaTransaction): number {
    let priority = 5; // Default priority

    // Higher priority for authenticated users
    if (connection.notes.auth_user) {
      priority -= 2;
    }

    // Lower priority for bulk emails
    if (transaction.rcpt_to.length > 10) {
      priority += 2;
    }

    // Higher priority for smaller messages
    const messageSize = transaction.message_stream.get_data().length;
    if (messageSize < 1024 * 1024) { // Less than 1MB
      priority -= 1;
    }

    return Math.max(0, Math.min(10, priority));
  }

  private async updateQueueStatus(queueId: string, status: string): Promise<void> {
    try {
      const updateData: any = { status };

      if (status === 'PROCESSING') {
        updateData.lastAttemptAt = new Date();
      }

      await this.database.updateQueuedEmail(queueId, updateData);
    } catch (error) {
      this.logger.error('Failed to update queue status', error);
      throw error;
    }
  }

  private async handleBounce(queueId: string, error: string, attempts: number): Promise<void> {
    try {
      if (attempts >= this.maxRetries) {
        // Move to dead letter queue
        await this.database.updateQueuedEmail(queueId, {
          status: 'DEAD_LETTER',
          lastError: error,
          attempts: attempts + 1,
        });
        
        this.logger.warn(`Email moved to dead letter queue: ${queueId}`);
        this.metrics.setQueueDepth(await this.getQueueDepth('DEAD_LETTER'), 'failed');
      } else {
        // Schedule retry
        const nextAttempt = new Date(Date.now() + (this.retryDelay * 1000 * Math.pow(2, attempts))); // Exponential backoff
        
        await this.database.updateQueuedEmail(queueId, {
          status: 'PENDING',
          lastError: error,
          attempts: attempts + 1,
          nextAttempt,
        });
        
        this.logger.info(`Email scheduled for retry: ${queueId}, attempt ${attempts + 1}`);
      }
    } catch (err) {
      this.logger.error('Failed to handle bounce', err);
      throw err;
    }
  }

  private startQueueProcessing(): void {
    // Process queue every 30 seconds
    this.processingInterval = setInterval(() => {
      if (!this.processingActive) {
        this.processQueue().catch(error => {
          this.logger.error('Queue processing error', error);
        });
      }
    }, 30000);

    this.logger.info('Queue processing started');
  }

  private async processQueue(): Promise<void> {
    if (this.processingActive) {
      return;
    }

    this.processingActive = true;

    try {
      // Get pending emails
      const queuedEmails = await this.database.getQueuedEmails(10) as any[];
      
      if (queuedEmails.length === 0) {
        return;
      }

      this.logger.debug(`Processing ${queuedEmails.length} queued emails`);

      for (const queuedEmail of queuedEmails) {
        try {
          await this.processQueuedEmail(queuedEmail);
        } catch (error) {
          this.logger.error(`Failed to process queued email ${queuedEmail.id}`, error);
        }
      }

      // Update queue depth metrics
      await this.updateQueueMetrics();

    } catch (error) {
      this.logger.error('Queue processing error', error);
    } finally {
      this.processingActive = false;
    }
  }

  private async processQueuedEmail(queuedEmail: any): Promise<void> {
    try {
      // Check if email is too old
      const age = Date.now() - new Date(queuedEmail.queuedAt).getTime();
      if (age > this.maxAge * 1000) {
        await this.database.updateQueuedEmail(queuedEmail.id, {
          status: 'DEAD_LETTER',
          lastError: 'Message expired',
        });
        this.logger.warn(`Email expired and moved to dead letter: ${queuedEmail.id}`);
        return;
      }

      // Update status to processing
      await this.database.updateQueuedEmail(queuedEmail.id, {
        status: 'PROCESSING',
        lastAttemptAt: new Date(),
      });

      // Decode message data
      const messageData = Buffer.from(queuedEmail.messageData, 'base64');

      // Create a new email object for Haraka to process
      // This is a simplified approach - in production you'd want to integrate with Haraka's queue system
      this.logger.info(`Processing queued email: ${queuedEmail.id}`);

      // For now, just mark as delivered (in production, you'd actually send the email)
      await this.database.updateQueuedEmail(queuedEmail.id, {
        status: 'DELIVERED',
      });

    } catch (error) {
      // Handle processing error
      await this.handleBounce(queuedEmail.id, error.message, queuedEmail.attempts);
      throw error;
    }
  }

  private async updateQueueMetrics(): Promise<void> {
    try {
      const pendingCount = await this.getQueueDepth('PENDING');
      const processingCount = await this.getQueueDepth('PROCESSING');
      const failedCount = await this.getQueueDepth('DEAD_LETTER');

      this.metrics.setQueueDepth(pendingCount, 'pending');
      this.metrics.setQueueDepth(processingCount, 'processing');
      this.metrics.setQueueDepth(failedCount, 'failed');

    } catch (error) {
      this.logger.error('Failed to update queue metrics', error);
    }
  }

  private async getQueueDepth(status: string): Promise<number> {
    try {
      // This would need to be implemented in the database service
      // For now, return 0
      return 0;
    } catch (error) {
      this.logger.error('Failed to get queue depth', error);
      return 0;
    }
  }

  // Utility methods
  public async getQueueStats(): Promise<Record<string, any>> {
    try {
      const pending = await this.getQueueDepth('PENDING');
      const processing = await this.getQueueDepth('PROCESSING');
      const delivered = await this.getQueueDepth('DELIVERED');
      const failed = await this.getQueueDepth('DEAD_LETTER');

      return {
        pending,
        processing,
        delivered,
        failed,
        total: pending + processing + delivered + failed,
        enabled: this.enabled,
        config: {
          maxRetries: this.maxRetries,
          retryDelay: this.retryDelay,
          maxAge: this.maxAge,
        },
      };
    } catch (error) {
      this.logger.error('Failed to get queue stats', error);
      return { error: error.message };
    }
  }

  public async reprocessDeadLetters(): Promise<number> {
    try {
      // Get dead letter emails
      const deadLetters = await this.database.getQueuedEmails(100) as any[]; // This would need filtering
      let reprocessed = 0;

      for (const email of deadLetters) {
        if (email.status === 'DEAD_LETTER') {
          // Reset for reprocessing
          await this.database.updateQueuedEmail(email.id, {
            status: 'PENDING',
            attempts: 0,
            nextAttempt: new Date(),
            lastError: null,
          });
          reprocessed++;
        }
      }

      this.logger.info(`Reprocessed ${reprocessed} dead letter emails`);
      return reprocessed;

    } catch (error) {
      this.logger.error('Failed to reprocess dead letters', error);
      throw error;
    }
  }

  public async clearOldEmails(olderThanDays: number = 7): Promise<number> {
    try {
      const cutoffDate = new Date(Date.now() - (olderThanDays * 24 * 60 * 60 * 1000));
      
      // This would need to be implemented in the database service
      // For now, return 0
      this.logger.info(`Cleared old emails older than ${olderThanDays} days`);
      return 0;

    } catch (error) {
      this.logger.error('Failed to clear old emails', error);
      throw error;
    }
  }

  public async reloadConfig(): Promise<void> {
    try {
      this.logger.info('Reloading queue management configuration...');
      this.loadConfig();
      await this.loadConfigFromDatabase();
      this.logger.info('Queue management configuration reloaded successfully');
    } catch (error) {
      this.logger.error('Failed to reload queue management configuration', error);
      throw error;
    }
  }

  private async loadConfigFromDatabase(): Promise<void> {
    try {
      const smtpConfig = await this.database.getSMTPConfig() as any;
      
      if (smtpConfig) {
        this.enabled = smtpConfig.queueEnabled;
        this.maxRetries = smtpConfig.queueRetryAttempts;
        this.retryDelay = smtpConfig.queueRetryDelay;
        this.maxAge = smtpConfig.queueMaxAge;
      }
    } catch (error) {
      this.logger.error('Failed to load queue management configuration from database', error);
    }
  }

  // Cleanup on shutdown
  public shutdown(): void {
    if (this.processingInterval) {
      clearInterval(this.processingInterval);
    }
    this.processingActive = false;
    this.logger.info('Queue management shutdown completed');
  }
}

// Export for Haraka plugin system
module.exports = new QueueManagementPlugin();
