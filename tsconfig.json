{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "commonjs", "moduleResolution": "node", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "allowJs": true, "checkJs": false, "declaration": true, "declarationMap": true, "sourceMap": true, "outDir": "./dist", "rootDir": "./src", "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noImplicitOverride": true, "exactOptionalPropertyTypes": true, "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "isolatedModules": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@ironrelay/shared/*": ["packages/shared/src/*"], "@ironrelay/api/*": ["packages/api/src/*"], "@ironrelay/smtp-engine/*": ["packages/smtp-engine/src/*"], "@ironrelay/security/*": ["packages/security/src/*"]}}, "include": ["packages/*/src/**/*", "packages/*/test/**/*", "types/**/*"], "exclude": ["node_modules", "dist", "build", "coverage", "**/*.spec.ts", "**/*.test.ts"], "ts-node": {"require": ["tsconfig-paths/register"]}}