import { UUID, Timestamp, IPAddress } from './common.types';

// License management
export interface License {
  id: UUID;
  licenseKey: string;
  
  // License details
  type: LicenseType;
  edition: LicenseEdition;
  
  // Validity
  issuedAt: Timestamp;
  validFrom: Timestamp;
  validTo: Timestamp;
  
  // Limits
  maxDomains: number;
  maxThroughput: number; // emails per hour
  maxUsers: number;
  maxInstances: number;
  
  // Features
  features: LicenseFeature[];
  
  // Hardware binding
  hardwareFingerprint: string;
  allowedFingerprints: string[];
  
  // Status
  status: LicenseStatus;
  lastValidation: Timestamp;
  validationError?: string;
  
  // Usage tracking
  currentDomains: number;
  currentThroughput: number;
  currentUsers: number;
  currentInstances: number;
  
  // Customer information
  customerName: string;
  customerEmail: string;
  customerOrganization?: string;
  
  // Support
  supportLevel: SupportLevel;
  supportExpiry?: Timestamp;
}

export enum LicenseType {
  TRIAL = 'TRIAL',
  COMMERCIAL = 'COMMERCIAL',
  ENTERPRISE = 'ENTERPRISE',
  OEM = 'OEM',
  DEVELOPER = 'DEVELOPER',
}

export enum LicenseEdition {
  STARTER = 'STARTER',
  PROFESSIONAL = 'PROFESSIONAL',
  ENTERPRISE = 'ENTERPRISE',
  UNLIMITED = 'UNLIMITED',
}

export enum LicenseFeature {
  BASIC_SMTP = 'BASIC_SMTP',
  ADVANCED_ROUTING = 'ADVANCED_ROUTING',
  TLS_ENCRYPTION = 'TLS_ENCRYPTION',
  AUTHENTICATION = 'AUTHENTICATION',
  RATE_LIMITING = 'RATE_LIMITING',
  MONITORING = 'MONITORING',
  API_ACCESS = 'API_ACCESS',
  MULTI_INSTANCE = 'MULTI_INSTANCE',
  BACKUP_RESTORE = 'BACKUP_RESTORE',
  LDAP_INTEGRATION = 'LDAP_INTEGRATION',
  SSO_INTEGRATION = 'SSO_INTEGRATION',
  CUSTOM_BRANDING = 'CUSTOM_BRANDING',
  PRIORITY_SUPPORT = 'PRIORITY_SUPPORT',
  CLUSTERING = 'CLUSTERING',
  HIGH_AVAILABILITY = 'HIGH_AVAILABILITY',
}

export enum LicenseStatus {
  VALID = 'VALID',
  EXPIRED = 'EXPIRED',
  INVALID = 'INVALID',
  SUSPENDED = 'SUSPENDED',
  REVOKED = 'REVOKED',
  HARDWARE_MISMATCH = 'HARDWARE_MISMATCH',
}

export enum SupportLevel {
  COMMUNITY = 'COMMUNITY',
  STANDARD = 'STANDARD',
  PREMIUM = 'PREMIUM',
  ENTERPRISE = 'ENTERPRISE',
}

// Security event tracking
export interface SecurityEvent {
  id: UUID;
  type: SecurityEventType;
  severity: SecuritySeverity;
  
  // Event details
  timestamp: Timestamp;
  source: string;
  description: string;
  
  // Context
  userId?: UUID;
  sessionId?: string;
  ipAddress?: IPAddress;
  userAgent?: string;
  
  // Additional data
  metadata: Record<string, unknown>;
  
  // Response
  blocked: boolean;
  action: SecurityAction;
  
  // Investigation
  investigated: boolean;
  investigatedBy?: UUID;
  investigatedAt?: Timestamp;
  resolution?: string;
}

export enum SecurityEventType {
  LOGIN_FAILURE = 'LOGIN_FAILURE',
  BRUTE_FORCE_ATTACK = 'BRUTE_FORCE_ATTACK',
  SUSPICIOUS_LOGIN = 'SUSPICIOUS_LOGIN',
  PRIVILEGE_ESCALATION = 'PRIVILEGE_ESCALATION',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  DATA_BREACH = 'DATA_BREACH',
  MALWARE_DETECTED = 'MALWARE_DETECTED',
  DDOS_ATTACK = 'DDOS_ATTACK',
  SQL_INJECTION = 'SQL_INJECTION',
  XSS_ATTEMPT = 'XSS_ATTEMPT',
  CSRF_ATTEMPT = 'CSRF_ATTEMPT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INVALID_CERTIFICATE = 'INVALID_CERTIFICATE',
  ENCRYPTION_FAILURE = 'ENCRYPTION_FAILURE',
  LICENSE_VIOLATION = 'LICENSE_VIOLATION',
  CONFIGURATION_CHANGE = 'CONFIGURATION_CHANGE',
  SYSTEM_COMPROMISE = 'SYSTEM_COMPROMISE',
}

export enum SecuritySeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export enum SecurityAction {
  NONE = 'NONE',
  LOG_ONLY = 'LOG_ONLY',
  BLOCK_REQUEST = 'BLOCK_REQUEST',
  BLOCK_IP = 'BLOCK_IP',
  BLOCK_USER = 'BLOCK_USER',
  REQUIRE_MFA = 'REQUIRE_MFA',
  FORCE_PASSWORD_RESET = 'FORCE_PASSWORD_RESET',
  SUSPEND_ACCOUNT = 'SUSPEND_ACCOUNT',
  ALERT_ADMIN = 'ALERT_ADMIN',
  QUARANTINE = 'QUARANTINE',
}

// IP access control
export interface IPAccessRule {
  id: UUID;
  name: string;
  description?: string;
  
  // Rule definition
  type: IPRuleType;
  ipAddress?: IPAddress;
  cidrBlock?: string;
  ipRange?: {
    start: IPAddress;
    end: IPAddress;
  };
  
  // Action
  action: IPRuleAction;
  
  // Scope
  scope: IPRuleScope[];
  
  // Status
  enabled: boolean;
  priority: number;
  
  // Metadata
  createdAt: Timestamp;
  createdBy: UUID;
  updatedAt: Timestamp;
  updatedBy: UUID;
  
  // Usage tracking
  hitCount: number;
  lastHit?: Timestamp;
}

export enum IPRuleType {
  SINGLE_IP = 'SINGLE_IP',
  CIDR_BLOCK = 'CIDR_BLOCK',
  IP_RANGE = 'IP_RANGE',
  COUNTRY = 'COUNTRY',
  ASN = 'ASN',
}

export enum IPRuleAction {
  ALLOW = 'ALLOW',
  DENY = 'DENY',
  RATE_LIMIT = 'RATE_LIMIT',
  REQUIRE_AUTH = 'REQUIRE_AUTH',
  LOG_ONLY = 'LOG_ONLY',
}

export enum IPRuleScope {
  SMTP = 'SMTP',
  API = 'API',
  WEB = 'WEB',
  ALL = 'ALL',
}

// Encryption configuration
export interface EncryptionConfig {
  // Algorithm settings
  algorithm: EncryptionAlgorithm;
  keySize: number;
  
  // Key management
  keyRotationEnabled: boolean;
  keyRotationInterval: number; // days
  
  // TLS settings
  tlsMinVersion: TLSVersion;
  tlsMaxVersion: TLSVersion;
  tlsCipherSuites: string[];
  
  // Certificate settings
  certificateValidation: boolean;
  allowSelfSigned: boolean;
  
  // HSTS settings
  hstsEnabled: boolean;
  hstsMaxAge: number; // seconds
  hstsIncludeSubdomains: boolean;
}

export enum EncryptionAlgorithm {
  AES_256_GCM = 'AES_256_GCM',
  AES_256_CBC = 'AES_256_CBC',
  CHACHA20_POLY1305 = 'CHACHA20_POLY1305',
}

export enum TLSVersion {
  TLS_1_0 = 'TLS_1_0',
  TLS_1_1 = 'TLS_1_1',
  TLS_1_2 = 'TLS_1_2',
  TLS_1_3 = 'TLS_1_3',
}

// Security scan results
export interface SecurityScan {
  id: UUID;
  type: SecurityScanType;
  
  // Scan details
  startedAt: Timestamp;
  completedAt?: Timestamp;
  duration?: number; // seconds
  
  // Status
  status: ScanStatus;
  
  // Results
  vulnerabilities: SecurityVulnerability[];
  score: number; // 0-100
  
  // Configuration
  scanConfig: SecurityScanConfig;
  
  // Metadata
  triggeredBy: UUID;
  automated: boolean;
}

export enum SecurityScanType {
  VULNERABILITY_SCAN = 'VULNERABILITY_SCAN',
  PENETRATION_TEST = 'PENETRATION_TEST',
  COMPLIANCE_CHECK = 'COMPLIANCE_CHECK',
  CONFIGURATION_AUDIT = 'CONFIGURATION_AUDIT',
  DEPENDENCY_SCAN = 'DEPENDENCY_SCAN',
}

export enum ScanStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED',
}

export interface SecurityVulnerability {
  id: string;
  title: string;
  description: string;
  severity: VulnerabilitySeverity;
  
  // Classification
  category: VulnerabilityCategory;
  cveId?: string;
  cvssScore?: number;
  
  // Location
  component: string;
  version?: string;
  location?: string;
  
  // Remediation
  recommendation: string;
  fixAvailable: boolean;
  fixVersion?: string;
  
  // Status
  status: VulnerabilityStatus;
  acknowledgedBy?: UUID;
  acknowledgedAt?: Timestamp;
  fixedAt?: Timestamp;
}

export enum VulnerabilitySeverity {
  INFO = 'INFO',
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export enum VulnerabilityCategory {
  INJECTION = 'INJECTION',
  AUTHENTICATION = 'AUTHENTICATION',
  AUTHORIZATION = 'AUTHORIZATION',
  ENCRYPTION = 'ENCRYPTION',
  CONFIGURATION = 'CONFIGURATION',
  DEPENDENCY = 'DEPENDENCY',
  NETWORK = 'NETWORK',
  INPUT_VALIDATION = 'INPUT_VALIDATION',
  SESSION_MANAGEMENT = 'SESSION_MANAGEMENT',
  ERROR_HANDLING = 'ERROR_HANDLING',
}

export enum VulnerabilityStatus {
  OPEN = 'OPEN',
  ACKNOWLEDGED = 'ACKNOWLEDGED',
  FIXED = 'FIXED',
  FALSE_POSITIVE = 'FALSE_POSITIVE',
  ACCEPTED_RISK = 'ACCEPTED_RISK',
}

export interface SecurityScanConfig {
  // Scope
  includeNetworkScan: boolean;
  includeWebScan: boolean;
  includeDependencyScan: boolean;
  includeConfigScan: boolean;
  
  // Intensity
  scanIntensity: ScanIntensity;
  maxDuration: number; // minutes
  
  // Targets
  targetHosts: string[];
  excludeHosts: string[];
  
  // Authentication
  useAuthentication: boolean;
  credentials?: {
    username: string;
    password: string;
  };
}

export enum ScanIntensity {
  LIGHT = 'LIGHT',
  NORMAL = 'NORMAL',
  AGGRESSIVE = 'AGGRESSIVE',
}

// Compliance framework
export interface ComplianceFramework {
  id: UUID;
  name: string;
  version: string;
  description: string;
  
  // Requirements
  requirements: ComplianceRequirement[];
  
  // Status
  enabled: boolean;
  lastAssessment?: Timestamp;
  complianceScore?: number; // 0-100
}

export interface ComplianceRequirement {
  id: string;
  title: string;
  description: string;
  category: string;
  
  // Implementation
  controls: ComplianceControl[];
  
  // Status
  status: ComplianceStatus;
  lastCheck?: Timestamp;
  evidence?: string[];
}

export interface ComplianceControl {
  id: string;
  title: string;
  description: string;
  
  // Implementation
  implemented: boolean;
  automatedCheck: boolean;
  checkScript?: string;
  
  // Evidence
  evidence: string[];
  lastVerified?: Timestamp;
}

export enum ComplianceStatus {
  COMPLIANT = 'COMPLIANT',
  NON_COMPLIANT = 'NON_COMPLIANT',
  PARTIALLY_COMPLIANT = 'PARTIALLY_COMPLIANT',
  NOT_ASSESSED = 'NOT_ASSESSED',
}
