import * as path from 'path';
import * as fs from 'fs';
import { Logger } from '../utils/logger';
import { Config } from '../config/config';
import { DatabaseService } from '../services/database.service';
import { MetricsService } from '../services/metrics.service';

export class SMTPServer {
  private logger = Logger.getInstance();
  private config = Config.getInstance();
  private database = DatabaseService.getInstance();
  private metrics = MetricsService.getInstance();
  private haraka: unknown;
  private isRunning = false;

  constructor() {
    // Haraka will be initialized in the initialize method
  }

  public async initialize(): Promise<void> {
    try {
      this.logger.info('Initializing SMTP server...');

      // Validate configuration
      if (!this.config.validate()) {
        throw new Error('Invalid configuration');
      }

      // Set up Haraka configuration directory
      await this.setupHarakaConfig();

      // Initialize Haraka
      await this.initializeHaraka();

      this.logger.info('SMTP server initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize SMTP server', error);
      throw error;
    }
  }

  public async start(): Promise<void> {
    try {
      if (this.isRunning) {
        this.logger.warn('SMTP server is already running');
        return;
      }

      this.logger.info('Starting SMTP server...');

      // Start Haraka server
      await this.startHaraka();

      this.isRunning = true;
      this.logger.info(`SMTP server started on ${this.config.get('smtp.host')}:${this.config.get('smtp.port')}`);
    } catch (error) {
      this.logger.error('Failed to start SMTP server', error);
      throw error;
    }
  }

  public async stop(): Promise<void> {
    try {
      if (!this.isRunning) {
        this.logger.warn('SMTP server is not running');
        return;
      }

      this.logger.info('Stopping SMTP server...');

      // Stop Haraka server
      await this.stopHaraka();

      this.isRunning = false;
      this.logger.info('SMTP server stopped successfully');
    } catch (error) {
      this.logger.error('Failed to stop SMTP server', error);
      throw error;
    }
  }

  public async restart(): Promise<void> {
    this.logger.info('Restarting SMTP server...');
    await this.stop();
    await this.start();
    this.logger.info('SMTP server restarted successfully');
  }

  public isServerRunning(): boolean {
    return this.isRunning;
  }

  private async setupHarakaConfig(): Promise<void> {
    const configDir = path.join(process.cwd(), 'haraka-config');
    
    // Create config directory if it doesn't exist
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }

    // Create subdirectories
    const subdirs = ['plugins', 'config', 'docs_html'];
    for (const subdir of subdirs) {
      const dirPath = path.join(configDir, subdir);
      if (!fs.existsSync(dirPath)) {
        fs.mkdirSync(dirPath, { recursive: true });
      }
    }

    // Write main configuration files
    await this.writeHarakaConfigs(configDir);

    this.logger.info(`Haraka configuration set up in: ${configDir}`);
  }

  private async writeHarakaConfigs(configDir: string): Promise<void> {
    // smtp.ini - Main SMTP configuration
    const smtpConfig = `
[main]
listen=${this.config.get('smtp.host')}:${this.config.get('smtp.port')}
max_connections=${this.config.get('smtp.maxConnections')}
max_connections_per_ip=${this.config.get('smtp.maxConnectionsPerIP')}
connection_timeout=${this.config.get('smtp.connectionTimeout')}

[tls]
enabled=${this.config.get('tls.enabled')}
required=${this.config.get('tls.required')}
cert=${this.config.get('tls.certPath') || ''}
key=${this.config.get('tls.keyPath') || ''}
ciphers=${this.config.get<string[]>('tls.ciphers').join(':')}

[auth]
required=${this.config.get('auth.required')}
methods=${this.config.get<string[]>('auth.methods').join(',')}

[limits]
max_message_size=${this.config.get('message.maxSize')}
max_recipients=${this.config.get('message.maxRecipients')}
`;

    fs.writeFileSync(path.join(configDir, 'smtp.ini'), smtpConfig.trim());

    // plugins - List of plugins to load
    const plugins = `
# Core plugins
ironrelay.access_control
ironrelay.authentication
ironrelay.domain_routing
ironrelay.rate_limiting
ironrelay.transaction_logging
ironrelay.security_checks
ironrelay.queue_management

# Standard Haraka plugins
connect.geoip
connect.fcrdns
mail_from.is_resolvable
rcpt_to.in_host_list
data.headers
`;

    fs.writeFileSync(path.join(configDir, 'plugins'), plugins.trim());

    // host_list - Allowed recipient domains (for rcpt_to.in_host_list)
    const hostList = '*'; // Allow all domains for relay
    fs.writeFileSync(path.join(configDir, 'host_list'), hostList);

    // me - Server hostname
    const hostname = this.config.get<string>('smtp.hostname');
    fs.writeFileSync(path.join(configDir, 'me'), hostname);

    // Create plugin configuration files
    await this.writePluginConfigs(configDir);
  }

  private async writePluginConfigs(configDir: string): Promise<void> {
    const configPath = path.join(configDir, 'config');

    // ironrelay.access_control.ini
    const accessControlConfig = `
[whitelist]
ips=${this.config.get<string[]>('security.ipWhitelist').join(',')}
cidrs=${this.config.get<string[]>('security.cidrWhitelist').join(',')}

[blacklist]
ips=${this.config.get<string[]>('security.ipBlacklist').join(',')}
cidrs=${this.config.get<string[]>('security.cidrBlacklist').join(',')}
`;

    fs.writeFileSync(path.join(configPath, 'ironrelay.access_control.ini'), accessControlConfig.trim());

    // ironrelay.rate_limiting.ini
    const rateLimitConfig = `
[limits]
enabled=${this.config.get('rateLimit.enabled')}
per_ip=${this.config.get('rateLimit.perIP')}
per_user=${this.config.get('rateLimit.perUser')}
window=${this.config.get('rateLimit.window')}
`;

    fs.writeFileSync(path.join(configPath, 'ironrelay.rate_limiting.ini'), rateLimitConfig.trim());

    // ironrelay.queue_management.ini
    const queueConfig = `
[queue]
enabled=${this.config.get('queue.enabled')}
retry_attempts=${this.config.get('queue.retryAttempts')}
retry_delay=${this.config.get('queue.retryDelay')}
max_age=${this.config.get('queue.maxAge')}
`;

    fs.writeFileSync(path.join(configPath, 'ironrelay.queue_management.ini'), queueConfig.trim());
  }

  private async initializeHaraka(): Promise<void> {
    try {
      // Import Haraka dynamically
      const Haraka = require('haraka');
      
      // Set up Haraka with our configuration
      const configDir = path.join(process.cwd(), 'haraka-config');
      
      // Initialize Haraka server
      this.haraka = new Haraka.Server({
        config_dir: configDir,
      });

      // Set up event handlers
      this.setupHarakaEventHandlers();

      this.logger.info('Haraka initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Haraka', error);
      throw error;
    }
  }

  private setupHarakaEventHandlers(): void {
    if (!this.haraka) return;

    const server = this.haraka as any;

    // Connection events
    server.on('connect', (connection: any) => {
      this.logger.smtpConnection(connection.uuid, connection.remote.ip, 'connected');
      this.metrics.recordConnection('accepted', connection.remote.ip);
      this.metrics.incrementActiveConnections();
    });

    server.on('disconnect', (connection: any) => {
      this.logger.smtpConnection(connection.uuid, connection.remote.ip, 'disconnected');
      this.metrics.decrementActiveConnections();
      
      if (connection.start_time) {
        const duration = (Date.now() - connection.start_time) / 1000;
        this.metrics.recordConnectionDuration(duration);
      }
    });

    // Error events
    server.on('error', (error: Error) => {
      this.logger.error('SMTP server error', error);
      this.metrics.recordError('server_error', error.message);
    });

    // Log events
    server.on('log', (level: string, message: string, data?: any) => {
      switch (level) {
        case 'error':
          this.logger.error(`Haraka: ${message}`, data);
          break;
        case 'warn':
          this.logger.warn(`Haraka: ${message}`, data);
          break;
        case 'info':
          this.logger.info(`Haraka: ${message}`, data);
          break;
        case 'debug':
          this.logger.debug(`Haraka: ${message}`, data);
          break;
        default:
          this.logger.info(`Haraka [${level}]: ${message}`, data);
      }
    });
  }

  private async startHaraka(): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.haraka) {
        reject(new Error('Haraka not initialized'));
        return;
      }

      const server = this.haraka as any;

      server.on('listening', () => {
        resolve();
      });

      server.on('error', (error: Error) => {
        reject(error);
      });

      // Start the server
      server.start();
    });
  }

  private async stopHaraka(): Promise<void> {
    return new Promise((resolve) => {
      if (!this.haraka) {
        resolve();
        return;
      }

      const server = this.haraka as any;

      server.on('close', () => {
        resolve();
      });

      // Stop the server
      server.stop();
    });
  }

  // Health check method
  public async healthCheck(): Promise<{
    status: 'healthy' | 'unhealthy';
    details: Record<string, unknown>;
  }> {
    try {
      const details: Record<string, unknown> = {
        running: this.isRunning,
        uptime: process.uptime(),
        memoryUsage: process.memoryUsage(),
        config: {
          port: this.config.get('smtp.port'),
          host: this.config.get('smtp.host'),
          tlsEnabled: this.config.get('tls.enabled'),
          authRequired: this.config.get('auth.required'),
        },
      };

      // Check database connectivity
      const dbHealthy = await this.database.healthCheck();
      details.database = { healthy: dbHealthy };

      const status = this.isRunning && dbHealthy ? 'healthy' : 'unhealthy';

      return { status, details };
    } catch (error) {
      this.logger.error('Health check failed', error);
      return {
        status: 'unhealthy',
        details: { error: error instanceof Error ? error.message : 'Unknown error' },
      };
    }
  }

  // Get server statistics
  public getStats(): Record<string, unknown> {
    return {
      running: this.isRunning,
      uptime: process.uptime(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      config: this.config.getAll(),
    };
  }
}
