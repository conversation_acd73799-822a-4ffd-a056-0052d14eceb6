#!/usr/bin/env node

import { SMTPServer } from './server/smtp-server';
import { Logger } from './utils/logger';
import { Config } from './config/config';
import { DatabaseService } from './services/database.service';
import { MetricsService } from './services/metrics.service';

const logger = Logger.getInstance();

async function main(): Promise<void> {
  try {
    logger.info('🚀 Starting IronRelay SMTP Engine...');

    // Load configuration
    const config = Config.getInstance();
    await config.load();

    logger.info('📋 Configuration loaded successfully');

    // Initialize database connection
    const database = DatabaseService.getInstance();
    await database.connect();

    logger.info('🗄️ Database connected successfully');

    // Initialize metrics service
    const metrics = MetricsService.getInstance();
    await metrics.initialize();

    logger.info('📊 Metrics service initialized');

    // Create and start SMTP server
    const smtpServer = new SMTPServer();
    await smtpServer.initialize();
    await smtpServer.start();

    logger.info(`✅ IronRelay SMTP Engine started successfully on port ${config.get('smtp.port')}`);

    // Graceful shutdown handling
    const gracefulShutdown = async (signal: string): Promise<void> => {
      logger.info(`📡 Received ${signal}, starting graceful shutdown...`);

      try {
        // Stop SMTP server
        await smtpServer.stop();
        logger.info('🛑 SMTP server stopped');

        // Close database connection
        await database.disconnect();
        logger.info('🗄️ Database disconnected');

        // Stop metrics service
        await metrics.stop();
        logger.info('📊 Metrics service stopped');

        logger.info('✅ Graceful shutdown completed');
        process.exit(0);
      } catch (error) {
        logger.error('❌ Error during graceful shutdown:', error);
        process.exit(1);
      }
    };

    // Handle shutdown signals
    process.on('SIGTERM', () => gracefulShutdown('SIGTERM'));
    process.on('SIGINT', () => gracefulShutdown('SIGINT'));

    // Handle uncaught exceptions
    process.on('uncaughtException', (error) => {
      logger.error('💥 Uncaught Exception:', error);
      gracefulShutdown('uncaughtException').catch(() => process.exit(1));
    });

    process.on('unhandledRejection', (reason, promise) => {
      logger.error('💥 Unhandled Rejection at:', promise, 'reason:', reason);
      gracefulShutdown('unhandledRejection').catch(() => process.exit(1));
    });

  } catch (error) {
    logger.error('❌ Failed to start IronRelay SMTP Engine:', error);
    process.exit(1);
  }
}

// Start the application
if (require.main === module) {
  main().catch((error) => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}
