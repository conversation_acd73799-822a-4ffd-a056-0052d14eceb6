{"version": 3, "file": "schemes.js", "sourceRoot": "", "sources": ["../../../src/auth/schemes.ts"], "names": [], "mappings": ";AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\nimport type { OAuth2Flow } from \"./oauth2Flows.js\";\n\n/**\n * Represents HTTP Basic authentication scheme.\n * Basic authentication scheme requires a username and password to be provided with each request.\n * The credentials are encoded using Base64 and included in the Authorization header.\n */\nexport interface BasicAuthScheme {\n  /** Type of auth scheme */\n  kind: \"http\";\n  /** Basic authentication scheme */\n  scheme: \"basic\";\n}\n\n/**\n * Represents HTTP Bearer authentication scheme.\n * Bearer authentication scheme requires a bearer token to be provided with each request.\n * The token is included in the Authorization header with the \"Bearer\" prefix.\n */\nexport interface BearerAuthScheme {\n  /** Type of auth scheme */\n  kind: \"http\";\n  /** Bearer authentication scheme */\n  scheme: \"bearer\";\n}\n\n/**\n * Represents an endpoint or operation that requires no authentication.\n */\nexport interface NoAuthAuthScheme {\n  /** Type of auth scheme */\n  kind: \"noAuth\";\n}\n\n/**\n * Represents API Key authentication scheme.\n * API Key authentication requires a key to be provided with each request.\n * The key can be provided in different locations: query parameter, header, or cookie.\n */\nexport interface ApiKeyAuthScheme {\n  /** Type of auth scheme */\n  kind: \"apiKey\";\n  /** Location of the API key */\n  apiKeyLocation: \"query\" | \"header\" | \"cookie\";\n  /** Name of the API key parameter */\n  name: string;\n}\n\n/** Represents OAuth2 authentication scheme with specified flows */\nexport interface OAuth2AuthScheme<TFlows extends OAuth2Flow[]> {\n  /** Type of auth scheme */\n  kind: \"oauth2\";\n  /** Supported OAuth2 flows */\n  flows: TFlows;\n}\n\n/** Union type of all supported authentication schemes */\nexport type AuthScheme =\n  | BasicAuthScheme\n  | BearerAuthScheme\n  | NoAuthAuthScheme\n  | ApiKeyAuthScheme\n  | OAuth2AuthScheme<OAuth2Flow[]>;\n"]}