"use strict";var d=Object.create;var a=Object.defineProperty;var i=Object.getOwnPropertyDescriptor;var l=Object.getOwnPropertyNames;var s=Object.getPrototypeOf,u=Object.prototype.hasOwnProperty;var x=(t,e)=>{for(var o in e)a(t,o,{get:e[o],enumerable:!0})},m=(t,e,o,f)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of l(e))!u.call(t,r)&&r!==o&&a(t,r,{get:()=>e[r],enumerable:!(f=i(e,r))||f.enumerable});return t};var b=(t,e,o)=>(o=t!=null?d(s(t)):{},m(e||!t||!t.__esModule?a(o,"default",{value:t,enumerable:!0}):o,t)),c=t=>m(a({},"__esModule",{value:!0}),t);var h={};x(h,{default:()=>g});module.exports=c(h);var p=b(require("./state")),g=p.default;
