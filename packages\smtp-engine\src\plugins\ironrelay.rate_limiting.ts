// IronRelay Rate Limiting Plugin
// Handles rate limiting per IP and per user

import { Logger } from '../utils/logger';
import { DatabaseService } from '../services/database.service';
import { MetricsService } from '../services/metrics.service';

interface HarakaConnection {
  uuid: string;
  remote: {
    ip: string;
    host?: string;
  };
  notes: Record<string, any>;
  transaction?: HarakaTransaction;
  loginfo: (plugin: any, message: string) => void;
  logwarn: (plugin: any, message: string) => void;
  logerror: (plugin: any, message: string) => void;
}

interface HarakaTransaction {
  uuid: string;
  mail_from: {
    user: string;
    host: string;
  };
  rcpt_to: Array<{
    user: string;
    host: string;
  }>;
  notes: Record<string, any>;
}

interface HarakaNext {
  (action?: number, message?: string): void;
  DENY: number;
  DENYSOFT: number;
  DENYDISCONNECT: number;
  OK: number;
  CONT: number;
}

interface RateLimitEntry {
  count: number;
  resetTime: number;
  firstRequest: number;
}

interface HarakaPlugin {
  config: any;
  register: () => void;
  hook_connect: (next: HarakaNext, connection: HarakaConnection) => void;
  hook_mail: (next: HarakaNext, connection: HarakaConnection, params: any[]) => void;
  hook_rcpt: (next: HarakaNext, connection: HarakaConnection, params: any[]) => void;
}

class RateLimitingPlugin implements HarakaPlugin {
  public config: any;
  private logger = Logger.getInstance();
  private database = DatabaseService.getInstance();
  private metrics = MetricsService.getInstance();
  
  // Configuration
  private enabled = true;
  private perIPLimit = 100;
  private perUserLimit = 1000;
  private windowSeconds = 3600; // 1 hour
  
  // Rate limiting storage
  private ipLimits = new Map<string, RateLimitEntry>();
  private userLimits = new Map<string, RateLimitEntry>();
  private connectionLimits = new Map<string, RateLimitEntry>();
  
  // Cleanup interval
  private cleanupInterval?: NodeJS.Timeout;

  public register(): void {
    const plugin = this;

    // Load configuration
    plugin.loadConfig();

    // Register hooks
    plugin.register_hook('connect', 'check_connection_rate');
    plugin.register_hook('mail', 'check_mail_rate');
    plugin.register_hook('rcpt', 'check_recipient_rate');

    // Start cleanup timer
    this.startCleanupTimer();
  }

  private loadConfig(): void {
    try {
      const config = this.config.get('ironrelay.rate_limiting.ini') || {};
      
      this.enabled = config.enabled !== 'false' && config.enabled !== false;
      this.perIPLimit = parseInt(config.per_ip || '100', 10);
      this.perUserLimit = parseInt(config.per_user || '1000', 10);
      this.windowSeconds = parseInt(config.window || '3600', 10);

      this.logger.info('Rate limiting configuration loaded', {
        enabled: this.enabled,
        perIPLimit: this.perIPLimit,
        perUserLimit: this.perUserLimit,
        windowSeconds: this.windowSeconds,
      });
    } catch (error) {
      this.logger.error('Failed to load rate limiting configuration', error);
    }
  }

  public hook_connect(next: HarakaNext, connection: HarakaConnection): void {
    const plugin = this;

    if (!this.enabled) {
      return next(next.OK);
    }

    try {
      const remoteIP = connection.remote.ip;
      
      // Check connection rate limit
      if (this.isRateLimited('connection', remoteIP, 10, 60)) { // 10 connections per minute
        connection.logwarn(plugin, `Connection rate limit exceeded for IP ${remoteIP}`);
        this.metrics.recordRateLimitHit('ip', remoteIP);
        this.logRateLimitEvent('CONNECTION_RATE_LIMIT', connection);
        return next(next.DENYDISCONNECT, 'Connection rate limit exceeded');
      }

      // Record connection
      this.recordRequest('connection', remoteIP);
      
      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Rate limiting error: ${error}`);
      this.logger.error('Rate limiting plugin error', error);
      return next(next.OK); // Don't block on rate limiting errors
    }
  }

  public hook_mail(next: HarakaNext, connection: HarakaConnection, params: any[]): void {
    const plugin = this;

    if (!this.enabled) {
      return next(next.OK);
    }

    try {
      const remoteIP = connection.remote.ip;
      const authUser = connection.notes.auth_user;

      // Check IP-based rate limit
      if (this.isRateLimited('ip', remoteIP, this.perIPLimit, this.windowSeconds)) {
        connection.logwarn(plugin, `IP rate limit exceeded for ${remoteIP}`);
        this.metrics.recordRateLimitHit('ip', remoteIP);
        this.logRateLimitEvent('IP_RATE_LIMIT', connection);
        return next(next.DENYSOFT, 'Rate limit exceeded, please try again later');
      }

      // Check user-based rate limit (if authenticated)
      if (authUser) {
        const userLimit = authUser.customRateLimit || this.perUserLimit;
        const userWindow = authUser.customRateLimitWindow || this.windowSeconds;
        
        if (this.isRateLimited('user', authUser.username, userLimit, userWindow)) {
          connection.logwarn(plugin, `User rate limit exceeded for ${authUser.username}`);
          this.metrics.recordRateLimitHit('user', remoteIP);
          this.logRateLimitEvent('USER_RATE_LIMIT', connection, { username: authUser.username });
          return next(next.DENYSOFT, 'User rate limit exceeded, please try again later');
        }

        // Record user request
        this.recordRequest('user', authUser.username);
      }

      // Record IP request
      this.recordRequest('ip', remoteIP);

      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Rate limiting error: ${error}`);
      this.logger.error('Rate limiting plugin error', error);
      return next(next.OK); // Don't block on rate limiting errors
    }
  }

  public hook_rcpt(next: HarakaNext, connection: HarakaConnection, params: any[]): void {
    const plugin = this;

    if (!this.enabled) {
      return next(next.OK);
    }

    try {
      const remoteIP = connection.remote.ip;
      const authUser = connection.notes.auth_user;

      // Check recipient rate limit (stricter than mail rate limit)
      const recipientLimit = Math.floor(this.perIPLimit / 2); // Half of mail limit
      
      if (this.isRateLimited('recipient', remoteIP, recipientLimit, this.windowSeconds)) {
        connection.logwarn(plugin, `Recipient rate limit exceeded for IP ${remoteIP}`);
        this.metrics.recordRateLimitHit('ip', remoteIP);
        this.logRateLimitEvent('RECIPIENT_RATE_LIMIT', connection);
        return next(next.DENYSOFT, 'Recipient rate limit exceeded');
      }

      // Check user recipient limit (if authenticated)
      if (authUser) {
        const userRecipientLimit = Math.floor((authUser.customRateLimit || this.perUserLimit) / 2);
        
        if (this.isRateLimited('user_recipient', authUser.username, userRecipientLimit, this.windowSeconds)) {
          connection.logwarn(plugin, `User recipient rate limit exceeded for ${authUser.username}`);
          this.metrics.recordRateLimitHit('user', remoteIP);
          this.logRateLimitEvent('USER_RECIPIENT_RATE_LIMIT', connection, { username: authUser.username });
          return next(next.DENYSOFT, 'User recipient rate limit exceeded');
        }

        // Record user recipient request
        this.recordRequest('user_recipient', authUser.username);
      }

      // Record recipient request
      this.recordRequest('recipient', remoteIP);

      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Rate limiting error: ${error}`);
      this.logger.error('Rate limiting plugin error', error);
      return next(next.OK); // Don't block on rate limiting errors
    }
  }

  private isRateLimited(type: string, key: string, limit: number, windowSeconds: number): boolean {
    const now = Date.now();
    const windowMs = windowSeconds * 1000;
    const limitKey = `${type}:${key}`;
    
    let storage: Map<string, RateLimitEntry>;
    
    switch (type) {
      case 'ip':
      case 'recipient':
        storage = this.ipLimits;
        break;
      case 'user':
      case 'user_recipient':
        storage = this.userLimits;
        break;
      case 'connection':
        storage = this.connectionLimits;
        break;
      default:
        storage = this.ipLimits;
    }

    const entry = storage.get(limitKey);

    if (!entry) {
      // No previous requests
      return false;
    }

    // Check if window has expired
    if (now > entry.resetTime) {
      // Window expired, reset
      storage.delete(limitKey);
      return false;
    }

    // Check if limit exceeded
    return entry.count >= limit;
  }

  private recordRequest(type: string, key: string): void {
    const now = Date.now();
    const limitKey = `${type}:${key}`;
    
    let storage: Map<string, RateLimitEntry>;
    let windowSeconds: number;
    
    switch (type) {
      case 'ip':
      case 'recipient':
        storage = this.ipLimits;
        windowSeconds = this.windowSeconds;
        break;
      case 'user':
      case 'user_recipient':
        storage = this.userLimits;
        windowSeconds = this.windowSeconds;
        break;
      case 'connection':
        storage = this.connectionLimits;
        windowSeconds = 60; // 1 minute for connections
        break;
      default:
        storage = this.ipLimits;
        windowSeconds = this.windowSeconds;
    }

    const windowMs = windowSeconds * 1000;
    const entry = storage.get(limitKey);

    if (!entry || now > entry.resetTime) {
      // New window
      storage.set(limitKey, {
        count: 1,
        resetTime: now + windowMs,
        firstRequest: now,
      });
    } else {
      // Increment count
      entry.count++;
    }
  }

  private async logRateLimitEvent(
    type: string,
    connection: HarakaConnection,
    metadata?: Record<string, any>
  ): Promise<void> {
    try {
      await this.database.createSecurityEvent({
        type,
        severity: 'MEDIUM',
        source: 'smtp_rate_limiting',
        description: `Rate limit exceeded: ${type}`,
        ipAddress: connection.remote.ip,
        metadata: {
          connectionId: connection.uuid,
          hostname: connection.remote.host,
          ...metadata,
        },
        blocked: true,
        action: 'RATE_LIMIT',
      });
    } catch (error) {
      this.logger.error('Failed to log rate limit event', error);
    }
  }

  private startCleanupTimer(): void {
    // Clean up expired entries every 5 minutes
    this.cleanupInterval = setInterval(() => {
      this.cleanupExpiredEntries();
    }, 5 * 60 * 1000);
  }

  private cleanupExpiredEntries(): void {
    const now = Date.now();
    let cleaned = 0;

    // Clean IP limits
    for (const [key, entry] of this.ipLimits.entries()) {
      if (now > entry.resetTime) {
        this.ipLimits.delete(key);
        cleaned++;
      }
    }

    // Clean user limits
    for (const [key, entry] of this.userLimits.entries()) {
      if (now > entry.resetTime) {
        this.userLimits.delete(key);
        cleaned++;
      }
    }

    // Clean connection limits
    for (const [key, entry] of this.connectionLimits.entries()) {
      if (now > entry.resetTime) {
        this.connectionLimits.delete(key);
        cleaned++;
      }
    }

    if (cleaned > 0) {
      this.logger.debug(`Cleaned up ${cleaned} expired rate limit entries`);
    }
  }

  // Method to reload configuration
  public async reloadConfig(): Promise<void> {
    try {
      this.logger.info('Reloading rate limiting configuration...');
      this.loadConfig();
      await this.loadConfigFromDatabase();
      this.logger.info('Rate limiting configuration reloaded successfully');
    } catch (error) {
      this.logger.error('Failed to reload rate limiting configuration', error);
      throw error;
    }
  }

  private async loadConfigFromDatabase(): Promise<void> {
    try {
      const smtpConfig = await this.database.getSMTPConfig() as any;
      
      if (smtpConfig) {
        this.enabled = smtpConfig.rateLimitEnabled;
        this.perIPLimit = smtpConfig.rateLimitPerIP;
        this.perUserLimit = smtpConfig.rateLimitPerUser;
        this.windowSeconds = smtpConfig.rateLimitWindow;
      }
    } catch (error) {
      this.logger.error('Failed to load rate limiting configuration from database', error);
    }
  }

  // Utility methods
  public getRateLimitStatus(type: string, key: string): {
    limited: boolean;
    count: number;
    limit: number;
    resetTime: number;
    remaining: number;
  } {
    const limitKey = `${type}:${key}`;
    let storage: Map<string, RateLimitEntry>;
    let limit: number;

    switch (type) {
      case 'ip':
        storage = this.ipLimits;
        limit = this.perIPLimit;
        break;
      case 'user':
        storage = this.userLimits;
        limit = this.perUserLimit;
        break;
      default:
        storage = this.ipLimits;
        limit = this.perIPLimit;
    }

    const entry = storage.get(limitKey);
    
    if (!entry) {
      return {
        limited: false,
        count: 0,
        limit,
        resetTime: 0,
        remaining: limit,
      };
    }

    const now = Date.now();
    if (now > entry.resetTime) {
      // Expired
      storage.delete(limitKey);
      return {
        limited: false,
        count: 0,
        limit,
        resetTime: 0,
        remaining: limit,
      };
    }

    return {
      limited: entry.count >= limit,
      count: entry.count,
      limit,
      resetTime: entry.resetTime,
      remaining: Math.max(0, limit - entry.count),
    };
  }

  public clearRateLimit(type: string, key: string): void {
    const limitKey = `${type}:${key}`;
    
    switch (type) {
      case 'ip':
        this.ipLimits.delete(limitKey);
        break;
      case 'user':
        this.userLimits.delete(limitKey);
        break;
      case 'connection':
        this.connectionLimits.delete(limitKey);
        break;
    }
  }

  public getStats(): Record<string, any> {
    return {
      enabled: this.enabled,
      ipLimitEntries: this.ipLimits.size,
      userLimitEntries: this.userLimits.size,
      connectionLimitEntries: this.connectionLimits.size,
      config: {
        perIPLimit: this.perIPLimit,
        perUserLimit: this.perUserLimit,
        windowSeconds: this.windowSeconds,
      },
    };
  }

  // Cleanup on shutdown
  public shutdown(): void {
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
    }
  }
}

// Export for Haraka plugin system
module.exports = new RateLimitingPlugin();
