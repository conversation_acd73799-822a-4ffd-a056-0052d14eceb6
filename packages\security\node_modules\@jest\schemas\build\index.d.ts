/**
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import {Static} from '@sinclair/typebox';
import {TBoolean} from '@sinclair/typebox';
import {TNull} from '@sinclair/typebox';
import {TNumber} from '@sinclair/typebox';
import {TObject} from '@sinclair/typebox';
import {TReadonlyOptional} from '@sinclair/typebox';
import {TString} from '@sinclair/typebox';

declare const RawSnapshotFormat: TObject<{
  callToJSON: TReadonlyOptional<TBoolean>;
  compareKeys: TReadonlyOptional<TNull>;
  escapeRegex: TReadonlyOptional<TBoolean>;
  escapeString: TReadonlyOptional<TBoolean>;
  highlight: TReadonlyOptional<TBoolean>;
  indent: TReadonlyOptional<TNumber>;
  maxDepth: TReadonlyOptional<TNumber>;
  maxWidth: TReadonlyOptional<TNumber>;
  min: TReadonlyOptional<TBoolean>;
  printBasicPrototype: TReadonlyOptional<TBoolean>;
  printFunctionName: TReadonlyOptional<TBoolean>;
  theme: TReadonlyOptional<
    TObject<{
      comment: TReadonlyOptional<TString<string>>;
      content: TReadonlyOptional<TString<string>>;
      prop: TReadonlyOptional<TString<string>>;
      tag: TReadonlyOptional<TString<string>>;
      value: TReadonlyOptional<TString<string>>;
    }>
  >;
}>;

export declare const SnapshotFormat: TObject<{
  callToJSON: TReadonlyOptional<TBoolean>;
  compareKeys: TReadonlyOptional<TNull>;
  escapeRegex: TReadonlyOptional<TBoolean>;
  escapeString: TReadonlyOptional<TBoolean>;
  highlight: TReadonlyOptional<TBoolean>;
  indent: TReadonlyOptional<TNumber>;
  maxDepth: TReadonlyOptional<TNumber>;
  maxWidth: TReadonlyOptional<TNumber>;
  min: TReadonlyOptional<TBoolean>;
  printBasicPrototype: TReadonlyOptional<TBoolean>;
  printFunctionName: TReadonlyOptional<TBoolean>;
  theme: TReadonlyOptional<
    TObject<{
      comment: TReadonlyOptional<TString<string>>;
      content: TReadonlyOptional<TString<string>>;
      prop: TReadonlyOptional<TString<string>>;
      tag: TReadonlyOptional<TString<string>>;
      value: TReadonlyOptional<TString<string>>;
    }>
  >;
}>;

export declare type SnapshotFormat = Static<typeof RawSnapshotFormat>;

export {};
