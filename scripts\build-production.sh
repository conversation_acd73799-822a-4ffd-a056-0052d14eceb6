#!/bin/bash

# IronRelay Production Build Script
# This script builds a complete production-ready deployment package

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
BUILD_DIR="dist"
PACKAGE_DIR="packages"
OBFUSCATED_DIR="obfuscated"
SEA_DIR="sea-dist"
RELEASE_DIR="release"
VERSION=$(node -p "require('./package.json').version")
PLATFORM=$(node -p "process.platform")
ARCH=$(node -p "process.arch")

echo -e "${BLUE}🚀 IronRelay Production Build Script${NC}"
echo -e "${BLUE}Version: ${VERSION}${NC}"
echo -e "${BLUE}Platform: ${PLATFORM}-${ARCH}${NC}"
echo ""

# Function to print step headers
print_step() {
    echo -e "${GREEN}📦 $1${NC}"
}

# Function to print warnings
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Function to print errors
print_error() {
    echo -e "${RED}❌ $1${NC}"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Cleanup function
cleanup() {
    print_step "Cleaning up temporary files..."
    rm -rf temp/
    echo "✅ Cleanup completed"
}

# Trap cleanup on exit
trap cleanup EXIT

# Check prerequisites
print_step "Checking prerequisites..."

if ! command_exists node; then
    print_error "Node.js is not installed"
    exit 1
fi

if ! command_exists npm; then
    print_error "npm is not installed"
    exit 1
fi

NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 20 ]; then
    print_error "Node.js 20+ is required for SEA packaging. Current version: $(node -v)"
    exit 1
fi

echo "✅ Prerequisites check passed"

# Clean previous builds
print_step "Cleaning previous builds..."
rm -rf $BUILD_DIR $OBFUSCATED_DIR $SEA_DIR $RELEASE_DIR
echo "✅ Previous builds cleaned"

# Install dependencies
print_step "Installing dependencies..."
npm ci --production=false
echo "✅ Dependencies installed"

# Run tests
print_step "Running tests..."
if [ "${SKIP_TESTS:-false}" != "true" ]; then
    npm run test:ci
    echo "✅ Tests passed"
else
    print_warning "Tests skipped (SKIP_TESTS=true)"
fi

# Lint code
print_step "Linting code..."
npm run lint
echo "✅ Code linting passed"

# Build TypeScript
print_step "Building TypeScript..."
npm run build
echo "✅ TypeScript build completed"

# Build frontend
print_step "Building frontend..."
cd packages/frontend
npm run build
cd ../..
echo "✅ Frontend build completed"

# Code obfuscation
if [ "${ENABLE_OBFUSCATION:-true}" = "true" ]; then
    print_step "Obfuscating code..."
    
    # Create obfuscated directory
    mkdir -p $OBFUSCATED_DIR
    
    # Obfuscate API
    node packages/security/scripts/obfuscate.js \
        --input packages/api/dist \
        --output $OBFUSCATED_DIR/api \
        --level HIGH \
        --verbose
    
    # Obfuscate SMTP Engine
    node packages/security/scripts/obfuscate.js \
        --input packages/smtp-engine/dist \
        --output $OBFUSCATED_DIR/smtp-engine \
        --level HIGH \
        --verbose
    
    # Copy other files
    cp -r packages/api/package.json $OBFUSCATED_DIR/api/
    cp -r packages/smtp-engine/package.json $OBFUSCATED_DIR/smtp-engine/
    cp -r packages/frontend/dist $OBFUSCATED_DIR/frontend/
    
    echo "✅ Code obfuscation completed"
else
    print_warning "Code obfuscation skipped (ENABLE_OBFUSCATION=false)"
    cp -r packages/api/dist $OBFUSCATED_DIR/api
    cp -r packages/smtp-engine/dist $OBFUSCATED_DIR/smtp-engine
    cp -r packages/frontend/dist $OBFUSCATED_DIR/frontend
fi

# SEA Packaging
if [ "${ENABLE_SEA_PACKAGING:-true}" = "true" ]; then
    print_step "Creating Single Executable Applications..."
    
    mkdir -p $SEA_DIR
    
    # Package API
    node packages/security/scripts/package-sea.js \
        --entry $OBFUSCATED_DIR/api/index.js \
        --output $SEA_DIR \
        --platform $PLATFORM \
        --arch $ARCH \
        --verbose
    
    # Package SMTP Engine
    node packages/security/scripts/package-sea.js \
        --entry $OBFUSCATED_DIR/smtp-engine/index.js \
        --output $SEA_DIR \
        --platform $PLATFORM \
        --arch $ARCH \
        --verbose
    
    echo "✅ SEA packaging completed"
else
    print_warning "SEA packaging skipped (ENABLE_SEA_PACKAGING=false)"
fi

# Create release package
print_step "Creating release package..."

mkdir -p $RELEASE_DIR

# Create directory structure
mkdir -p $RELEASE_DIR/bin
mkdir -p $RELEASE_DIR/config
mkdir -p $RELEASE_DIR/data
mkdir -p $RELEASE_DIR/logs
mkdir -p $RELEASE_DIR/ssl
mkdir -p $RELEASE_DIR/scripts
mkdir -p $RELEASE_DIR/docs

# Copy executables
if [ "${ENABLE_SEA_PACKAGING:-true}" = "true" ]; then
    cp $SEA_DIR/* $RELEASE_DIR/bin/ 2>/dev/null || true
else
    cp -r $OBFUSCATED_DIR/* $RELEASE_DIR/bin/
fi

# Copy frontend
cp -r packages/frontend/dist $RELEASE_DIR/frontend

# Copy configuration files
cp .env.example $RELEASE_DIR/config/
cp docker-compose.yml $RELEASE_DIR/config/
cp docker-compose.prod.yml $RELEASE_DIR/config/
cp ecosystem.config.js $RELEASE_DIR/config/

# Copy scripts
cp scripts/*.sh $RELEASE_DIR/scripts/
cp scripts/*.js $RELEASE_DIR/scripts/
chmod +x $RELEASE_DIR/scripts/*.sh

# Copy documentation
cp README.md $RELEASE_DIR/
cp LICENSE $RELEASE_DIR/
cp -r docs/* $RELEASE_DIR/docs/

# Copy database files
cp -r packages/api/prisma $RELEASE_DIR/data/

# Copy Docker files
cp Dockerfile* $RELEASE_DIR/
cp .dockerignore $RELEASE_DIR/

# Create package.json for release
cat > $RELEASE_DIR/package.json << EOF
{
  "name": "ironrelay",
  "version": "$VERSION",
  "description": "Commercial-grade SMTP Relay Server",
  "main": "bin/api/index.js",
  "scripts": {
    "start": "node bin/api/index.js",
    "start:smtp": "node bin/smtp-engine/index.js",
    "start:all": "npm run start & npm run start:smtp",
    "install:production": "./scripts/install-production.sh",
    "setup": "./scripts/setup.sh",
    "migrate": "cd data && npx prisma migrate deploy",
    "seed": "cd data && npx prisma db seed"
  },
  "keywords": ["smtp", "email", "relay", "server", "enterprise"],
  "author": "IronRelay Team",
  "license": "Commercial",
  "engines": {
    "node": ">=20.0.0"
  }
}
EOF

# Create installation script
cat > $RELEASE_DIR/scripts/install.sh << 'EOF'
#!/bin/bash

# IronRelay Installation Script

set -e

echo "🚀 Installing IronRelay..."

# Check Node.js version
NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
if [ "$NODE_VERSION" -lt 20 ]; then
    echo "❌ Node.js 20+ is required. Current version: $(node -v)"
    exit 1
fi

# Install production dependencies
if [ -f "bin/api/package.json" ]; then
    cd bin/api && npm ci --production && cd ../..
fi

if [ -f "bin/smtp-engine/package.json" ]; then
    cd bin/smtp-engine && npm ci --production && cd ../..
fi

# Set up configuration
if [ ! -f ".env" ]; then
    cp config/.env.example .env
    echo "📝 Please edit .env file with your configuration"
fi

# Set permissions
chmod +x bin/*
chmod +x scripts/*.sh

echo "✅ IronRelay installation completed!"
echo ""
echo "Next steps:"
echo "1. Edit .env file with your configuration"
echo "2. Set up database: npm run migrate"
echo "3. Seed initial data: npm run seed"
echo "4. Start services: npm run start:all"
EOF

chmod +x $RELEASE_DIR/scripts/install.sh

# Create startup script
cat > $RELEASE_DIR/scripts/start.sh << 'EOF'
#!/bin/bash

# IronRelay Startup Script

set -e

# Load environment variables
if [ -f ".env" ]; then
    export $(cat .env | grep -v '^#' | xargs)
fi

# Start services
echo "🚀 Starting IronRelay services..."

# Start API server
echo "📡 Starting API server..."
node bin/api/index.js &
API_PID=$!

# Start SMTP engine
echo "📧 Starting SMTP engine..."
node bin/smtp-engine/index.js &
SMTP_PID=$!

# Wait for services to start
sleep 5

# Check if services are running
if kill -0 $API_PID 2>/dev/null; then
    echo "✅ API server started (PID: $API_PID)"
else
    echo "❌ API server failed to start"
    exit 1
fi

if kill -0 $SMTP_PID 2>/dev/null; then
    echo "✅ SMTP engine started (PID: $SMTP_PID)"
else
    echo "❌ SMTP engine failed to start"
    exit 1
fi

echo "🎉 IronRelay is running!"
echo "📊 Dashboard: http://localhost:${FRONTEND_PORT:-3001}"
echo "📡 API: http://localhost:${API_PORT:-3000}"

# Wait for services
wait
EOF

chmod +x $RELEASE_DIR/scripts/start.sh

# Create version info
cat > $RELEASE_DIR/VERSION << EOF
IronRelay v$VERSION
Build Date: $(date -u +"%Y-%m-%d %H:%M:%S UTC")
Platform: $PLATFORM-$ARCH
Node.js: $(node -v)
Commit: $(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
EOF

# Create checksums
print_step "Generating checksums..."
cd $RELEASE_DIR
find . -type f -exec sha256sum {} \; > CHECKSUMS.txt
cd ..

# Create archive
print_step "Creating release archive..."
ARCHIVE_NAME="ironrelay-v$VERSION-$PLATFORM-$ARCH.tar.gz"
tar -czf $ARCHIVE_NAME -C $RELEASE_DIR .

# Generate final checksum
sha256sum $ARCHIVE_NAME > $ARCHIVE_NAME.sha256

echo ""
echo -e "${GREEN}🎉 Production build completed successfully!${NC}"
echo ""
echo -e "${BLUE}📦 Release Package:${NC} $ARCHIVE_NAME"
echo -e "${BLUE}📊 Package Size:${NC} $(du -h $ARCHIVE_NAME | cut -f1)"
echo -e "${BLUE}🔐 Checksum:${NC} $(cat $ARCHIVE_NAME.sha256 | cut -d' ' -f1)"
echo ""
echo -e "${BLUE}📁 Release Directory:${NC} $RELEASE_DIR"
echo -e "${BLUE}🗂️  Contents:${NC}"
ls -la $RELEASE_DIR

echo ""
echo -e "${GREEN}✅ Build completed successfully!${NC}"
echo ""
echo "Next steps:"
echo "1. Test the release package"
echo "2. Deploy to staging environment"
echo "3. Run integration tests"
echo "4. Deploy to production"
