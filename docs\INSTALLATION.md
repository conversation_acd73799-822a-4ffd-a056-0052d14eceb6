# IronRelay Installation Guide

This guide covers the complete installation and setup process for IronRelay SMTP Relay Server.

## 📋 Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 20.04+, CentOS 8+, RHEL 8+), Windows Server 2019+, macOS 12+
- **CPU**: 2+ cores (4+ cores recommended for production)
- **Memory**: 4GB RAM minimum (8GB+ recommended for production)
- **Storage**: 20GB available space (SSD recommended)
- **Network**: Static IP address, ports 25, 587, 465, 993, 995 available

### Software Dependencies

- **Node.js**: Version 20.0.0 or higher (required for SEA packaging)
- **PostgreSQL**: Version 14.0 or higher
- **Redis**: Version 6.0 or higher
- **Docker**: Version 20.10+ (optional, for containerized deployment)
- **Git**: For source code management

### Network Requirements

- **Inbound Ports**:
  - 25 (SMTP)
  - 587 (SMTP Submission)
  - 465 (SMTPS)
  - 3000 (API Server)
  - 3001 (Web Dashboard)
  - 9090 (Prometheus Metrics)

- **Outbound Ports**:
  - 25, 587, 465 (SMTP to external servers)
  - 53 (DNS)
  - 80, 443 (HTTP/HTTPS for license validation)

## 🚀 Installation Methods

### Method 1: Docker Deployment (Recommended)

#### 1. Clone Repository

```bash
git clone https://github.com/your-org/ironrelay.git
cd ironrelay
```

#### 2. Configure Environment

```bash
cp .env.example .env
```

Edit `.env` file:

```bash
# Database Configuration
DATABASE_URL=****************************************************/ironrelay
POSTGRES_USER=ironrelay
POSTGRES_PASSWORD=secure_password
POSTGRES_DB=ironrelay

# Redis Configuration
REDIS_URL=redis://redis:6379

# SMTP Engine Configuration
SMTP_PORT=25
SMTP_HOST=0.0.0.0
SMTP_MAX_CONNECTIONS=1000
SMTP_MAX_MESSAGE_SIZE=26214400

# API Configuration
API_PORT=3000
JWT_SECRET=your-super-secure-jwt-secret-key-here
API_KEY_SECRET=your-api-key-secret-here

# Frontend Configuration
FRONTEND_PORT=3001

# Security Configuration
ENABLE_OBFUSCATION=true
ENABLE_SEA_PACKAGING=true
LICENSE_KEY=your-commercial-license-key

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3002
```

#### 3. Start Services

```bash
# Start all services
docker-compose up -d

# Check service status
docker-compose ps

# View logs
docker-compose logs -f
```

#### 4. Initialize Database

```bash
# Run database migrations
docker-compose exec api npm run db:migrate

# Seed initial data
docker-compose exec api npm run db:seed
```

#### 5. Access Dashboard

- **Web Dashboard**: http://localhost:3001
- **API Documentation**: http://localhost:3000/api/docs
- **Grafana Monitoring**: http://localhost:3002

Default credentials:
- **Username**: <EMAIL>
- **Password**: admin123!

### Method 2: Manual Installation

#### 1. Install Node.js

```bash
# Ubuntu/Debian
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# CentOS/RHEL
curl -fsSL https://rpm.nodesource.com/setup_20.x | sudo bash -
sudo yum install -y nodejs

# macOS
brew install node@20
```

#### 2. Install PostgreSQL

```bash
# Ubuntu/Debian
sudo apt-get update
sudo apt-get install postgresql postgresql-contrib

# CentOS/RHEL
sudo yum install postgresql-server postgresql-contrib
sudo postgresql-setup initdb
sudo systemctl enable postgresql
sudo systemctl start postgresql

# macOS
brew install postgresql
brew services start postgresql
```

#### 3. Install Redis

```bash
# Ubuntu/Debian
sudo apt-get install redis-server

# CentOS/RHEL
sudo yum install redis
sudo systemctl enable redis
sudo systemctl start redis

# macOS
brew install redis
brew services start redis
```

#### 4. Create Database

```bash
sudo -u postgres psql

CREATE USER ironrelay WITH PASSWORD 'secure_password';
CREATE DATABASE ironrelay OWNER ironrelay;
GRANT ALL PRIVILEGES ON DATABASE ironrelay TO ironrelay;
\q
```

#### 5. Clone and Build

```bash
git clone https://github.com/your-org/ironrelay.git
cd ironrelay

# Install dependencies
npm install

# Build all packages
npm run build

# Set up environment
cp .env.example .env
# Edit .env with your configuration
```

#### 6. Initialize Database

```bash
npm run db:migrate
npm run db:seed
```

#### 7. Start Services

```bash
# Start all services in development mode
npm run dev

# Or start individual services
npm run start:api &
npm run start:smtp &
npm run start:frontend &
```

### Method 3: Production Deployment

#### 1. Build Production Assets

```bash
# Build optimized production build
npm run build:production

# Create obfuscated build (commercial version)
npm run build:obfuscated

# Package as single executable
npm run package:sea
```

#### 2. Deploy with PM2

```bash
# Install PM2
npm install -g pm2

# Start services with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save
pm2 startup
```

#### 3. Configure Reverse Proxy

**Nginx Configuration** (`/etc/nginx/sites-available/ironrelay`):

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/ssl/cert.pem;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # Frontend
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
    
    # API
    location /api {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Metrics (restrict access)
    location /metrics {
        proxy_pass http://localhost:3000;
        allow 10.0.0.0/8;
        allow **********/12;
        allow ***********/16;
        deny all;
    }
}
```

## 🔧 Configuration

### Environment Variables

Create `.env` file with the following variables:

```bash
# Node Environment
NODE_ENV=production

# Database
DATABASE_URL=postgresql://user:password@localhost:5432/ironrelay
DB_HOST=localhost
DB_PORT=5432
DB_USERNAME=ironrelay
DB_PASSWORD=secure_password
DB_DATABASE=ironrelay
DB_SSL=false

# Redis
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# SMTP Engine
SMTP_PORT=25
SMTP_HOST=0.0.0.0
SMTP_MAX_CONNECTIONS=1000
SMTP_MAX_MESSAGE_SIZE=26214400
SMTP_TIMEOUT=30000
SMTP_TLS_CERT_PATH=/path/to/cert.pem
SMTP_TLS_KEY_PATH=/path/to/private.key

# API Server
API_PORT=3000
API_HOST=0.0.0.0
JWT_SECRET=your-256-bit-secret-key
JWT_EXPIRES_IN=1h
REFRESH_TOKEN_EXPIRES_IN=7d
API_KEY_SECRET=your-api-key-secret

# Frontend
FRONTEND_PORT=3001
FRONTEND_HOST=0.0.0.0

# Security
ENABLE_OBFUSCATION=true
ENABLE_SEA_PACKAGING=true
LICENSE_KEY=your-commercial-license-key
LICENSE_SERVER_URL=https://license.ironrelay.com
ENABLE_RATE_LIMITING=true
RATE_LIMIT_WINDOW=900000
RATE_LIMIT_MAX=100

# Monitoring
PROMETHEUS_PORT=9090
GRAFANA_PORT=3002
LOG_LEVEL=info
LOG_FORMAT=json
LOG_FILE_PATH=/var/log/ironrelay

# Email Settings
DEFAULT_FROM_EMAIL=<EMAIL>
ADMIN_EMAIL=<EMAIL>
SMTP_NOTIFICATION_HOST=localhost
SMTP_NOTIFICATION_PORT=587
SMTP_NOTIFICATION_USER=
SMTP_NOTIFICATION_PASS=
```

### Database Configuration

#### PostgreSQL Optimization

Edit `/etc/postgresql/14/main/postgresql.conf`:

```ini
# Memory
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Connections
max_connections = 200

# Logging
log_statement = 'mod'
log_min_duration_statement = 1000

# Performance
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
```

#### Redis Optimization

Edit `/etc/redis/redis.conf`:

```ini
# Memory
maxmemory 512mb
maxmemory-policy allkeys-lru

# Persistence
save 900 1
save 300 10
save 60 10000

# Security
requirepass your-redis-password
```

## 🔒 Security Setup

### SSL/TLS Certificates

#### Using Let's Encrypt

```bash
# Install Certbot
sudo apt-get install certbot

# Generate certificates
sudo certbot certonly --standalone -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

#### Using Custom Certificates

```bash
# Create certificate directory
sudo mkdir -p /etc/ironrelay/ssl

# Copy certificates
sudo cp your-cert.pem /etc/ironrelay/ssl/cert.pem
sudo cp your-private-key.pem /etc/ironrelay/ssl/private.key

# Set permissions
sudo chmod 600 /etc/ironrelay/ssl/private.key
sudo chmod 644 /etc/ironrelay/ssl/cert.pem
```

### Firewall Configuration

#### UFW (Ubuntu)

```bash
sudo ufw enable
sudo ufw allow 22/tcp
sudo ufw allow 25/tcp
sudo ufw allow 587/tcp
sudo ufw allow 465/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
```

#### iptables

```bash
# Allow SMTP ports
iptables -A INPUT -p tcp --dport 25 -j ACCEPT
iptables -A INPUT -p tcp --dport 587 -j ACCEPT
iptables -A INPUT -p tcp --dport 465 -j ACCEPT

# Allow web ports
iptables -A INPUT -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -p tcp --dport 443 -j ACCEPT

# Save rules
iptables-save > /etc/iptables/rules.v4
```

## 📊 Monitoring Setup

### Prometheus Configuration

Create `/etc/prometheus/prometheus.yml`:

```yaml
global:
  scrape_interval: 15s

scrape_configs:
  - job_name: 'ironrelay-api'
    static_configs:
      - targets: ['localhost:3000']
    metrics_path: '/metrics'
    
  - job_name: 'ironrelay-smtp'
    static_configs:
      - targets: ['localhost:2525']
    metrics_path: '/metrics'
    
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['localhost:9100']
```

### Grafana Setup

```bash
# Import dashboards
curl -X POST \
  *********************************/api/dashboards/db \
  -H 'Content-Type: application/json' \
  -d @grafana/dashboards/ironrelay-overview.json
```

## ✅ Verification

### Health Checks

```bash
# API health
curl http://localhost:3000/health

# SMTP connectivity
telnet localhost 25

# Database connectivity
psql -h localhost -U ironrelay -d ironrelay -c "SELECT version();"

# Redis connectivity
redis-cli ping
```

### Test Email

```bash
# Send test email
curl -X POST http://localhost:3000/api/test/send-email \
  -H "Content-Type: application/json" \
  -d '{
    "to": "<EMAIL>",
    "subject": "IronRelay Test",
    "text": "Test email from IronRelay"
  }'
```

## 🔧 Troubleshooting

### Common Issues

#### Port Already in Use

```bash
# Find process using port
sudo netstat -tulpn | grep :25
sudo kill -9 <PID>
```

#### Database Connection Failed

```bash
# Check PostgreSQL status
sudo systemctl status postgresql

# Check connection
psql -h localhost -U ironrelay -d ironrelay
```

#### Permission Denied

```bash
# Fix file permissions
sudo chown -R ironrelay:ironrelay /opt/ironrelay
sudo chmod +x /opt/ironrelay/bin/*
```

### Log Locations

- **Application Logs**: `/var/log/ironrelay/`
- **SMTP Logs**: `/var/log/ironrelay/smtp.log`
- **API Logs**: `/var/log/ironrelay/api.log`
- **System Logs**: `/var/log/syslog`

## 📞 Support

For installation support:

- **Documentation**: https://docs.ironrelay.com
- **Community**: https://community.ironrelay.com
- **Commercial Support**: <EMAIL>

## 🔄 Updates

### Automatic Updates

```bash
# Enable auto-updates
sudo systemctl enable ironrelay-updater

# Manual update
sudo /opt/ironrelay/bin/update.sh
```

### Manual Updates

```bash
# Backup current installation
sudo tar -czf ironrelay-backup-$(date +%Y%m%d).tar.gz /opt/ironrelay

# Download new version
wget https://releases.ironrelay.com/latest/ironrelay-linux-x64.tar.gz

# Extract and install
sudo tar -xzf ironrelay-linux-x64.tar.gz -C /opt/

# Restart services
sudo systemctl restart ironrelay
```
