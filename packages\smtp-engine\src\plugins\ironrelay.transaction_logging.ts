// IronRelay Transaction Logging Plugin
// Handles comprehensive email transaction logging

import { Logger } from '../utils/logger';
import { DatabaseService } from '../services/database.service';
import { MetricsService } from '../services/metrics.service';

interface HarakaConnection {
  uuid: string;
  remote: {
    ip: string;
    host?: string;
  };
  notes: Record<string, any>;
  transaction?: HarakaTransaction;
  loginfo: (plugin: any, message: string) => void;
  logwarn: (plugin: any, message: string) => void;
  logerror: (plugin: any, message: string) => void;
}

interface HarakaTransaction {
  uuid: string;
  mail_from: {
    user: string;
    host: string;
    original: string;
  };
  rcpt_to: Array<{
    user: string;
    host: string;
    original: string;
  }>;
  header: {
    get: (name: string) => string | undefined;
    get_all: (name: string) => string[];
    headers_decoded: Record<string, string>;
  };
  message_stream: {
    get_data: () => Buffer;
  };
  notes: Record<string, any>;
  body?: {
    bodytext: string;
    children: any[];
  };
}

interface HarakaNext {
  (action?: number, message?: string): void;
  DENY: number;
  DENYSOFT: number;
  DENYDISCONNECT: number;
  OK: number;
  CONT: number;
}

interface HarakaPlugin {
  config: any;
  register: () => void;
  hook_mail: (next: HarakaNext, connection: HarakaConnection, params: any[]) => void;
  hook_rcpt: (next: HarakaNext, connection: HarakaConnection, params: any[]) => void;
  hook_data: (next: HarakaNext, connection: HarakaConnection) => void;
  hook_data_post: (next: HarakaNext, connection: HarakaConnection) => void;
  hook_queue: (next: HarakaNext, connection: HarakaConnection) => void;
  hook_delivered: (next: HarakaNext, hmail: any, params: any[]) => void;
  hook_bounce: (next: HarakaNext, hmail: any, error: string) => void;
}

class TransactionLoggingPlugin implements HarakaPlugin {
  public config: any;
  private logger = Logger.getInstance();
  private database = DatabaseService.getInstance();
  private metrics = MetricsService.getInstance();
  private transactionStartTimes = new Map<string, number>();

  public register(): void {
    const plugin = this;

    // Register hooks in order of SMTP transaction
    plugin.register_hook('mail', 'log_mail_from');
    plugin.register_hook('rcpt', 'log_rcpt_to');
    plugin.register_hook('data', 'log_data_start');
    plugin.register_hook('data_post', 'log_data_received');
    plugin.register_hook('queue', 'log_queue');
    plugin.register_hook('delivered', 'log_delivered');
    plugin.register_hook('bounce', 'log_bounce');
  }

  public hook_mail(next: HarakaNext, connection: HarakaConnection, params: any[]): void {
    const plugin = this;
    const transaction = connection.transaction;

    if (!transaction) {
      return next(next.OK);
    }

    try {
      // Record transaction start time
      this.transactionStartTimes.set(transaction.uuid, Date.now());

      // Initialize transaction logging data
      transaction.notes.ironrelay_log = {
        connectionId: connection.uuid,
        transactionId: transaction.uuid,
        startTime: Date.now(),
        remoteIP: connection.remote.ip,
        remoteHostname: connection.remote.host,
        mailFrom: transaction.mail_from.original,
        rcptTo: [],
        authUser: connection.notes.auth_user,
        authMethod: connection.notes.auth_method,
        tlsEnabled: connection.notes.tls && connection.notes.tls.enabled,
        accessControl: connection.notes.access_control,
      };

      connection.loginfo(plugin, `Transaction started: ${transaction.uuid} from ${transaction.mail_from.original}`);
      
      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Error in mail hook: ${error}`);
      this.logger.error('Transaction logging mail hook error', error);
      return next(next.OK);
    }
  }

  public hook_rcpt(next: HarakaNext, connection: HarakaConnection, params: any[]): void {
    const plugin = this;
    const transaction = connection.transaction;

    if (!transaction || !transaction.notes.ironrelay_log) {
      return next(next.OK);
    }

    try {
      const rcptTo = params[0];
      transaction.notes.ironrelay_log.rcptTo.push(rcptTo.original);

      connection.loginfo(plugin, `Recipient added: ${rcptTo.original}`);
      
      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Error in rcpt hook: ${error}`);
      this.logger.error('Transaction logging rcpt hook error', error);
      return next(next.OK);
    }
  }

  public hook_data(next: HarakaNext, connection: HarakaConnection): void {
    const plugin = this;
    const transaction = connection.transaction;

    if (!transaction || !transaction.notes.ironrelay_log) {
      return next(next.OK);
    }

    try {
      transaction.notes.ironrelay_log.dataStartTime = Date.now();
      connection.loginfo(plugin, `Data transfer started for transaction ${transaction.uuid}`);
      
      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Error in data hook: ${error}`);
      this.logger.error('Transaction logging data hook error', error);
      return next(next.OK);
    }
  }

  public hook_data_post(next: HarakaNext, connection: HarakaConnection): void {
    const plugin = this;
    const transaction = connection.transaction;

    if (!transaction || !transaction.notes.ironrelay_log) {
      return next(next.OK);
    }

    try {
      const logData = transaction.notes.ironrelay_log;
      const now = Date.now();
      
      // Calculate processing time
      const processingTime = now - logData.startTime;
      const dataTransferTime = logData.dataStartTime ? now - logData.dataStartTime : 0;

      // Get message details
      const messageSize = transaction.message_stream ? transaction.message_stream.get_data().length : 0;
      const subject = transaction.header ? transaction.header.get('Subject') : undefined;
      const messageId = transaction.header ? transaction.header.get('Message-ID') : `<${transaction.uuid}@ironrelay>`;

      // Extract headers for logging
      const headers = this.extractImportantHeaders(transaction);

      // Update log data
      logData.messageSize = messageSize;
      logData.subject = subject;
      logData.messageId = messageId;
      logData.processingTime = processingTime;
      logData.dataTransferTime = dataTransferTime;
      logData.headers = headers;
      logData.dataReceivedTime = now;

      connection.loginfo(plugin, `Data received: ${messageSize} bytes, processing time: ${processingTime}ms`);
      
      // Record metrics
      this.metrics.recordMessage('received', logData.authUser?.username);
      this.metrics.recordMessageSize(messageSize);
      this.metrics.recordProcessingTime(processingTime);

      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Error in data_post hook: ${error}`);
      this.logger.error('Transaction logging data_post hook error', error);
      return next(next.OK);
    }
  }

  public hook_queue(next: HarakaNext, connection: HarakaConnection): void {
    const plugin = this;
    const transaction = connection.transaction;

    if (!transaction || !transaction.notes.ironrelay_log) {
      return next(next.OK);
    }

    try {
      const logData = transaction.notes.ironrelay_log;
      
      // Create email transaction record in database
      this.createEmailTransaction(logData, 'QUEUED').catch(error => {
        this.logger.error('Failed to create email transaction record', error);
      });

      connection.loginfo(plugin, `Transaction queued: ${transaction.uuid}`);
      
      // Record metrics
      this.metrics.recordMessage('queued', logData.authUser?.username);

      return next(next.OK);

    } catch (error) {
      connection.logerror(plugin, `Error in queue hook: ${error}`);
      this.logger.error('Transaction logging queue hook error', error);
      return next(next.OK);
    }
  }

  public hook_delivered(next: HarakaNext, hmail: any, params: any[]): void {
    const plugin = this;

    try {
      const transactionId = hmail.todo?.uuid;
      if (!transactionId) {
        return next(next.OK);
      }

      const startTime = this.transactionStartTimes.get(transactionId);
      const deliveryTime = startTime ? Date.now() - startTime : 0;

      // Update transaction record
      this.updateEmailTransactionStatus(transactionId, {
        status: 'DELIVERED',
        deliveredAt: new Date(),
        deliveryTime,
        destinationHost: params[0]?.host,
        destinationPort: params[0]?.port,
      }).catch(error => {
        this.logger.error('Failed to update delivered transaction', error);
      });

      this.logger.smtpTransaction(transactionId, `delivered in ${deliveryTime}ms`);
      
      // Record metrics
      this.metrics.recordMessage('delivered');
      this.metrics.recordDeliveryTime(deliveryTime);

      // Clean up
      this.transactionStartTimes.delete(transactionId);

      return next(next.OK);

    } catch (error) {
      this.logger.error('Transaction logging delivered hook error', error);
      return next(next.OK);
    }
  }

  public hook_bounce(next: HarakaNext, hmail: any, error: string): void {
    const plugin = this;

    try {
      const transactionId = hmail.todo?.uuid;
      if (!transactionId) {
        return next(next.OK);
      }

      // Update transaction record
      this.updateEmailTransactionStatus(transactionId, {
        status: 'BOUNCED',
        errorMessage: error,
        retryCount: hmail.num_failures || 0,
      }).catch(err => {
        this.logger.error('Failed to update bounced transaction', err);
      });

      this.logger.smtpTransaction(transactionId, `bounced: ${error}`);
      
      // Record metrics
      this.metrics.recordMessage('failed');
      this.metrics.recordError('delivery', 'bounce');

      // Clean up
      this.transactionStartTimes.delete(transactionId);

      return next(next.OK);

    } catch (err) {
      this.logger.error('Transaction logging bounce hook error', err);
      return next(next.OK);
    }
  }

  private extractImportantHeaders(transaction: HarakaTransaction): Record<string, string> {
    const headers: Record<string, string> = {};
    
    if (!transaction.header) {
      return headers;
    }

    const importantHeaders = [
      'From',
      'To',
      'Cc',
      'Bcc',
      'Subject',
      'Date',
      'Message-ID',
      'Reply-To',
      'Return-Path',
      'X-Mailer',
      'User-Agent',
      'X-Originating-IP',
      'X-Forwarded-For',
      'Received',
    ];

    for (const headerName of importantHeaders) {
      const value = transaction.header.get(headerName);
      if (value) {
        headers[headerName] = value;
      }
    }

    return headers;
  }

  private async createEmailTransaction(logData: any, status: string): Promise<void> {
    try {
      const transactionData = {
        messageId: logData.messageId,
        senderIP: logData.remoteIP,
        senderHostname: logData.remoteHostname,
        mailFrom: logData.mailFrom,
        rcptTo: logData.rcptTo,
        subject: logData.subject,
        messageSize: logData.messageSize || 0,
        authenticatedUserId: logData.authUser?.id,
        authMethod: logData.authMethod,
        status,
        processingTime: logData.processingTime,
        headers: logData.headers,
        metadata: {
          connectionId: logData.connectionId,
          transactionId: logData.transactionId,
          tlsEnabled: logData.tlsEnabled,
          accessControl: logData.accessControl,
          dataTransferTime: logData.dataTransferTime,
        },
      };

      const transactionId = await this.database.createEmailTransaction(transactionData);
      
      // Store transaction ID for later updates
      logData.dbTransactionId = transactionId;

    } catch (error) {
      this.logger.error('Failed to create email transaction', error);
      throw error;
    }
  }

  private async updateEmailTransactionStatus(transactionId: string, updates: any): Promise<void> {
    try {
      // Find transaction by message ID or transaction UUID
      // This is a simplified approach - in production you might want to store the DB ID
      await this.database.updateEmailTransaction(transactionId, updates);
    } catch (error) {
      this.logger.error('Failed to update email transaction status', error);
      throw error;
    }
  }

  // Utility methods for external access
  public getTransactionStats(): Record<string, any> {
    return {
      activeTransactions: this.transactionStartTimes.size,
      oldestTransaction: this.getOldestTransactionAge(),
    };
  }

  private getOldestTransactionAge(): number {
    if (this.transactionStartTimes.size === 0) {
      return 0;
    }

    const now = Date.now();
    let oldest = now;

    for (const startTime of this.transactionStartTimes.values()) {
      if (startTime < oldest) {
        oldest = startTime;
      }
    }

    return now - oldest;
  }

  // Cleanup old transaction tracking
  public cleanupOldTransactions(): void {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours

    for (const [transactionId, startTime] of this.transactionStartTimes.entries()) {
      if (now - startTime > maxAge) {
        this.transactionStartTimes.delete(transactionId);
        this.logger.warn(`Cleaned up old transaction tracking: ${transactionId}`);
      }
    }
  }

  // Method to get transaction details
  public getTransactionDetails(transactionId: string): any {
    const startTime = this.transactionStartTimes.get(transactionId);
    if (!startTime) {
      return null;
    }

    return {
      transactionId,
      startTime,
      age: Date.now() - startTime,
    };
  }
}

// Export for Haraka plugin system
module.exports = new TransactionLoggingPlugin();
