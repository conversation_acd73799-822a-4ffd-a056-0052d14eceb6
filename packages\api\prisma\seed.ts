import { PrismaClient, UserRole, Theme, EmailStatus, QueueStatus, CertificateStatus, SecuritySeverity, AlertSeverity, AlertStatus } from '@prisma/client';
import * as bcrypt from 'bcrypt';
import { v4 as uuidv4 } from 'uuid';

const prisma = new PrismaClient();

async function main(): Promise<void> {
  console.log('🌱 Starting database seeding...');

  // Create default super admin user
  const adminPasswordHash = await bcrypt.hash('admin123!', 12);
  const superAdmin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: uuidv4(),
      email: '<EMAIL>',
      firstName: 'Super',
      lastName: 'Admin',
      passwordHash: adminPasswordHash,
      role: UserRole.SUPER_ADMIN,
      isActive: true,
      emailVerified: true,
      mfaEnabled: false,
      timezone: 'UTC',
      language: 'en',
      theme: Theme.LIGHT,
      emailNotifications: true,
      securityAlerts: true,
      passwordChangedAt: new Date(),
    },
  });

  console.log('✅ Created super admin user:', superAdmin.email);

  // Create default operator user
  const operatorPasswordHash = await bcrypt.hash('operator123!', 12);
  const operator = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      id: uuidv4(),
      email: '<EMAIL>',
      firstName: 'System',
      lastName: 'Operator',
      passwordHash: operatorPasswordHash,
      role: UserRole.OPERATOR,
      isActive: true,
      emailVerified: true,
      mfaEnabled: false,
      timezone: 'UTC',
      language: 'en',
      theme: Theme.LIGHT,
      emailNotifications: true,
      securityAlerts: true,
      passwordChangedAt: new Date(),
    },
  });

  console.log('✅ Created operator user:', operator.email);

  // Create default SMTP configuration
  const smtpConfig = await prisma.sMTPConfig.upsert({
    where: { id: 'default-smtp-config' },
    update: {},
    create: {
      id: 'default-smtp-config',
      name: 'Default SMTP Configuration',
      description: 'Default SMTP server configuration for IronRelay',
      hostname: '0.0.0.0',
      port: 2525,
      maxConnections: 100,
      maxConnectionsPerIP: 10,
      connectionTimeout: 30000,
      tlsEnabled: true,
      tlsRequired: false,
      authRequired: false,
      authMethods: ['PLAIN', 'LOGIN'],
      ipWhitelist: [],
      ipBlacklist: [],
      cidrWhitelist: [],
      cidrBlacklist: [],
      rateLimitEnabled: true,
      rateLimitPerIP: 100,
      rateLimitPerUser: 1000,
      rateLimitWindow: 3600,
      maxMessageSize: 26214400, // 25MB
      maxRecipients: 100,
      queueEnabled: true,
      queueRetryAttempts: 3,
      queueRetryDelay: 300,
      queueMaxAge: 86400,
      enabled: true,
      createdBy: superAdmin.id,
      updatedBy: superAdmin.id,
    },
  });

  console.log('✅ Created default SMTP configuration');

  // Create default system configuration
  const systemConfig = await prisma.systemConfig.upsert({
    where: { id: 'default-system-config' },
    update: {},
    create: {
      id: 'default-system-config',
      name: 'Default System Configuration',
      description: 'Default system configuration for IronRelay',
      instanceName: 'IronRelay SMTP Server',
      instanceDescription: 'Commercial-grade SMTP relay server',
      timezone: 'UTC',
      logLevel: 'info',
      logRetentionDays: 30,
      logMaxSize: 100,
      sessionTimeout: 60,
      mfaRequired: false,
      apiRateLimit: 1000,
      loginRateLimit: 5,
      backupEnabled: false,
      backupRetentionDays: 7,
      metricsEnabled: true,
      metricsRetentionDays: 30,
      alertingEnabled: true,
      enabled: true,
      createdBy: superAdmin.id,
      updatedBy: superAdmin.id,
    },
  });

  console.log('✅ Created default system configuration');

  // Create sample domain routes
  const domainRoutes = [
    {
      name: 'Gmail Route',
      description: 'Route for Gmail domains',
      domain: 'gmail.com',
      destinationHost: 'smtp.gmail.com',
      destinationPort: 587,
      destinationTLS: true,
      priority: 1,
      maxRetries: 3,
      retryDelay: 300,
      enabled: true,
    },
    {
      name: 'Outlook Route',
      description: 'Route for Outlook/Hotmail domains',
      domain: 'outlook.com',
      destinationHost: 'smtp-mail.outlook.com',
      destinationPort: 587,
      destinationTLS: true,
      priority: 1,
      maxRetries: 3,
      retryDelay: 300,
      enabled: true,
    },
    {
      name: 'Default Route',
      description: 'Default route for all other domains',
      domain: '*',
      destinationHost: 'localhost',
      destinationPort: 25,
      destinationTLS: false,
      priority: 999,
      maxRetries: 3,
      retryDelay: 300,
      enabled: true,
    },
  ];

  for (const route of domainRoutes) {
    await prisma.domainRoute.upsert({
      where: { domain: route.domain },
      update: {},
      create: {
        id: uuidv4(),
        ...route,
        createdBy: superAdmin.id,
        updatedBy: superAdmin.id,
      },
    });
  }

  console.log('✅ Created sample domain routes');

  // Create sample SMTP users
  const smtpUsers = [
    {
      name: 'Test User 1',
      description: 'Test SMTP user for development',
      username: 'testuser1',
      email: '<EMAIL>',
      password: 'testpass123!',
      allowedDomains: ['*'],
      allowedIPs: ['127.0.0.1', '::1'],
    },
    {
      name: 'Test User 2',
      description: 'Another test SMTP user',
      username: 'testuser2',
      email: '<EMAIL>',
      password: 'testpass123!',
      allowedDomains: ['example.com', 'test.com'],
      allowedIPs: [],
    },
  ];

  for (const user of smtpUsers) {
    const passwordHash = await bcrypt.hash(user.password, 12);
    await prisma.sMTPUser.upsert({
      where: { username: user.username },
      update: {},
      create: {
        id: uuidv4(),
        name: user.name,
        description: user.description,
        username: user.username,
        email: user.email,
        passwordHash,
        isActive: true,
        allowedDomains: user.allowedDomains,
        allowedIPs: user.allowedIPs,
        enabled: true,
        createdBy: superAdmin.id,
        updatedBy: superAdmin.id,
      },
    });
  }

  console.log('✅ Created sample SMTP users');

  // Create sample email transactions
  const sampleTransactions = [
    {
      messageId: '<<EMAIL>>',
      senderIP: '*************',
      senderHostname: 'client.example.com',
      mailFrom: '<EMAIL>',
      rcptTo: ['<EMAIL>'],
      subject: 'Test Email 1',
      messageSize: 1024,
      status: EmailStatus.DELIVERED,
      processingTime: 150,
      deliveryTime: 2500,
    },
    {
      messageId: '<<EMAIL>>',
      senderIP: '*************',
      senderHostname: 'server.example.com',
      mailFrom: '<EMAIL>',
      rcptTo: ['<EMAIL>', '<EMAIL>'],
      subject: 'Newsletter Update',
      messageSize: 2048,
      status: EmailStatus.DELIVERED,
      processingTime: 200,
      deliveryTime: 3200,
    },
    {
      messageId: '<<EMAIL>>',
      senderIP: '*************',
      mailFrom: '<EMAIL>',
      rcptTo: ['<EMAIL>'],
      subject: 'Suspicious Email',
      messageSize: 512,
      status: EmailStatus.REJECTED,
      processingTime: 50,
      errorCode: 'SPAM_DETECTED',
      errorMessage: 'Message rejected due to spam content',
    },
  ];

  for (const transaction of sampleTransactions) {
    await prisma.emailTransaction.create({
      data: {
        id: uuidv4(),
        ...transaction,
        receivedAt: new Date(Date.now() - Math.random() * 86400000), // Random time in last 24h
        processedAt: new Date(),
        deliveredAt: transaction.status === EmailStatus.DELIVERED ? new Date() : undefined,
      },
    });
  }

  console.log('✅ Created sample email transactions');

  // Create sample alerts
  const sampleAlerts = [
    {
      name: 'High Error Rate',
      description: 'Email error rate exceeds threshold',
      severity: AlertSeverity.WARNING,
      metric: 'email_error_rate',
      operator: 'GREATER_THAN',
      threshold: 5.0,
      duration: 300,
      status: AlertStatus.ACTIVE,
      triggeredAt: new Date(Date.now() - 3600000), // 1 hour ago
      tags: ['email', 'errors'],
    },
    {
      name: 'Queue Depth High',
      description: 'Email queue depth is unusually high',
      severity: AlertSeverity.CRITICAL,
      metric: 'queue_depth',
      operator: 'GREATER_THAN',
      threshold: 1000.0,
      duration: 600,
      status: AlertStatus.RESOLVED,
      triggeredAt: new Date(Date.now() - 7200000), // 2 hours ago
      resolvedAt: new Date(Date.now() - 3600000), // 1 hour ago
      tags: ['queue', 'performance'],
    },
  ];

  for (const alert of sampleAlerts) {
    await prisma.alert.create({
      data: {
        id: uuidv4(),
        ...alert,
        metadata: {},
      },
    });
  }

  console.log('✅ Created sample alerts');

  // Create audit log entries
  await prisma.auditLog.create({
    data: {
      id: uuidv4(),
      action: 'LOGIN',
      resource: 'User',
      resourceId: superAdmin.id,
      userId: superAdmin.id,
      userEmail: superAdmin.email,
      ipAddress: '127.0.0.1',
      userAgent: 'IronRelay-Seed-Script',
      metadata: {
        loginMethod: 'password',
        success: true,
      },
    },
  });

  console.log('✅ Created audit log entries');

  console.log('🎉 Database seeding completed successfully!');
  console.log('');
  console.log('Default users created:');
  console.log('  Super Admin: <EMAIL> / admin123!');
  console.log('  Operator: <EMAIL> / operator123!');
  console.log('');
  console.log('SMTP Test Users:');
  console.log('  testuser1 / testpass123!');
  console.log('  testuser2 / testpass123!');
}

main()
  .catch((e) => {
    console.error('❌ Error during seeding:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
