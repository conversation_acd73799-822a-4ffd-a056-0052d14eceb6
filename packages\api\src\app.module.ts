import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { ThrottlerModule } from '@nestjs/throttler';
import { ScheduleModule } from '@nestjs/schedule';

// Core modules
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { LoggerModule } from './common/logger/logger.module';
import { MetricsModule } from './common/metrics/metrics.module';
import { HealthModule } from './health/health.module';

// Feature modules
import { SmtpConfigModule } from './smtp-config/smtp-config.module';
import { DomainRoutingModule } from './domain-routing/domain-routing.module';
import { SmtpUsersModule } from './smtp-users/smtp-users.module';
import { EmailTransactionsModule } from './email-transactions/email-transactions.module';
import { TlsCertificatesModule } from './tls-certificates/tls-certificates.module';
import { SystemConfigModule } from './system-config/system-config.module';
import { MonitoringModule } from './monitoring/monitoring.module';
import { SecurityModule } from './security/security.module';
import { AuditModule } from './audit/audit.module';

// Configuration
import { configuration } from './config/configuration';
import { validationSchema } from './config/validation';

@Module({
  imports: [
    // Configuration
    ConfigModule.forRoot({
      isGlobal: true,
      load: [configuration],
      validationSchema,
      validationOptions: {
        allowUnknown: true,
        abortEarly: true,
      },
    }),

    // Rate limiting
    ThrottlerModule.forRootAsync({
      useFactory: () => ({
        ttl: 60, // 1 minute
        limit: 100, // 100 requests per minute
      }),
    }),

    // Task scheduling
    ScheduleModule.forRoot(),

    // Core modules
    LoggerModule,
    DatabaseModule,
    MetricsModule,
    HealthModule,

    // Authentication and authorization
    AuthModule,
    UsersModule,

    // SMTP management
    SmtpConfigModule,
    DomainRoutingModule,
    SmtpUsersModule,
    EmailTransactionsModule,

    // System management
    TlsCertificatesModule,
    SystemConfigModule,

    // Monitoring and security
    MonitoringModule,
    SecurityModule,
    AuditModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
