{"version": 3, "file": "oauth2Flows.js", "sourceRoot": "", "sources": ["../../../src/auth/oauth2Flows.ts"], "names": [], "mappings": "AAAA,uCAAuC;AACvC,kCAAkC", "sourcesContent": ["// Copyright (c) Microsoft Corporation.\n// Licensed under the MIT License.\n\n/**\n * Represents OAuth2 Authorization Code flow configuration.\n */\nexport interface AuthorizationCodeFlow {\n  /** Type of OAuth2 flow */\n  kind: \"authorizationCode\";\n  /** Authorization endpoint */\n  authorizationUrl: string;\n  /** Token endpoint */\n  tokenUrl: string;\n  /** Refresh token endpoint */\n  refreshUrl?: string;\n  /** OAuth2 scopes */\n  scopes?: string[];\n}\n\n/**\n * Represents OAuth2 Client Credentials flow configuration.\n */\nexport interface ClientCredentialsFlow {\n  /** Type of OAuth2 flow */\n  kind: \"clientCredentials\";\n  /** Token endpoint */\n  tokenUrl: string;\n  /** Refresh token endpoints */\n  refreshUrl?: string[];\n  /** OAuth2 scopes */\n  scopes?: string[];\n}\n\n/**\n * Represents OAuth2 Implicit flow configuration.\n */\nexport interface ImplicitFlow {\n  /** Type of OAuth2 flow */\n  kind: \"implicit\";\n  /** Authorization endpoint */\n  authorizationUrl: string;\n  /** Refresh token endpoint */\n  refreshUrl?: string;\n  /** OAuth2 scopes */\n  scopes?: string[];\n}\n\n/**\n * Represents OAuth2 Password flow configuration.\n */\nexport interface PasswordFlow {\n  /** Type of OAuth2 flow */\n  kind: \"password\";\n  /** Token endpoint */\n  tokenUrl: string;\n  /** Refresh token endpoint */\n  refreshUrl?: string;\n  /** OAuth2 scopes */\n  scopes?: string[];\n}\n\n/** Union type of all supported OAuth2 flows */\nexport type OAuth2Flow =\n  | AuthorizationCodeFlow\n  | ClientCredentialsFlow\n  | ImplicitFlow\n  | PasswordFlow;\n"]}