import { Injectable, UnauthorizedException, BadRequestException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
import * as bcrypt from 'bcrypt';
import * as crypto from 'crypto';

import { UsersService } from '../users/users.service';
import { PrismaService } from '../database/prisma.service';
import { Logger } from '../common/logger/logger.service';
import { LoginDto, ChangePasswordDto, ForgotPasswordDto, ResetPasswordDto } from './dto';
import { JwtPayload, LoginResponse, RefreshTokenResponse } from './interfaces';

@Injectable()
export class AuthService {
  private readonly logger = new Logger(AuthService.name);

  constructor(
    private readonly usersService: UsersService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
    private readonly prisma: PrismaService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    try {
      const user = await this.usersService.findByEmail(email);
      
      if (!user) {
        return null;
      }

      if (!user.isActive) {
        throw new UnauthorizedException('Account is disabled');
      }

      if (!user.emailVerified) {
        throw new UnauthorizedException('Email not verified');
      }

      // Check if account is locked
      if (user.lockedUntil && user.lockedUntil > new Date()) {
        throw new UnauthorizedException('Account is temporarily locked');
      }

      const isPasswordValid = await bcrypt.compare(password, user.passwordHash);
      
      if (!isPasswordValid) {
        // Increment failed login attempts
        await this.handleFailedLogin(user.id);
        return null;
      }

      // Reset failed login attempts on successful login
      await this.handleSuccessfulLogin(user.id);

      const { passwordHash, mfaSecret, ...result } = user;
      return result;
    } catch (error) {
      this.logger.error('User validation failed', error);
      throw error;
    }
  }

  async login(loginDto: LoginDto, ip: string, userAgent: string): Promise<LoginResponse> {
    try {
      const user = await this.validateUser(loginDto.email, loginDto.password);
      
      if (!user) {
        this.logger.authFailure(loginDto.email, 'Invalid credentials', ip, userAgent);
        throw new UnauthorizedException('Invalid credentials');
      }

      // Check MFA if enabled
      if (user.mfaEnabled && !loginDto.mfaCode) {
        throw new UnauthorizedException('MFA code required');
      }

      if (user.mfaEnabled && loginDto.mfaCode) {
        const isMfaValid = await this.validateMfaCode(user.id, loginDto.mfaCode);
        if (!isMfaValid) {
          this.logger.authFailure(loginDto.email, 'Invalid MFA code', ip, userAgent);
          throw new UnauthorizedException('Invalid MFA code');
        }
      }

      // Generate tokens
      const payload: JwtPayload = {
        sub: user.id,
        email: user.email,
        role: user.role,
      };

      const accessToken = this.jwtService.sign(payload);
      const refreshToken = await this.generateRefreshToken(user.id);

      // Create session
      await this.createSession(user.id, refreshToken, ip, userAgent);

      this.logger.authSuccess(user.id, user.email, ip, userAgent);

      return {
        user,
        accessToken,
        refreshToken,
        expiresIn: this.getTokenExpirationTime(),
      };
    } catch (error) {
      this.logger.error('Login failed', error);
      throw error;
    }
  }

  async refreshToken(refreshToken: string): Promise<RefreshTokenResponse> {
    try {
      const session = await this.prisma.userSession.findUnique({
        where: { sessionToken: refreshToken },
        include: { user: true },
      });

      if (!session || !session.isActive || session.expiresAt < new Date()) {
        throw new UnauthorizedException('Invalid refresh token');
      }

      if (!session.user.isActive) {
        throw new UnauthorizedException('Account is disabled');
      }

      // Generate new access token
      const payload: JwtPayload = {
        sub: session.user.id,
        email: session.user.email,
        role: session.user.role,
      };

      const accessToken = this.jwtService.sign(payload);

      // Update session last accessed time
      await this.prisma.userSession.update({
        where: { id: session.id },
        data: { lastAccessedAt: new Date() },
      });

      return {
        accessToken,
        expiresIn: this.getTokenExpirationTime(),
      };
    } catch (error) {
      this.logger.error('Token refresh failed', error);
      throw error;
    }
  }

  async logout(refreshToken: string): Promise<void> {
    try {
      await this.prisma.userSession.update({
        where: { sessionToken: refreshToken },
        data: {
          isActive: false,
          revokedAt: new Date(),
          revokedReason: 'User logout',
        },
      });
    } catch (error) {
      this.logger.error('Logout failed', error);
      // Don't throw error for logout failures
    }
  }

  async changePassword(userId: string, changePasswordDto: ChangePasswordDto): Promise<void> {
    try {
      const user = await this.usersService.findById(userId);
      
      if (!user) {
        throw new BadRequestException('User not found');
      }

      const isCurrentPasswordValid = await bcrypt.compare(
        changePasswordDto.currentPassword,
        user.passwordHash,
      );

      if (!isCurrentPasswordValid) {
        throw new BadRequestException('Current password is incorrect');
      }

      if (changePasswordDto.newPassword !== changePasswordDto.confirmPassword) {
        throw new BadRequestException('New passwords do not match');
      }

      const hashedPassword = await bcrypt.hash(
        changePasswordDto.newPassword,
        this.configService.get<number>('security.bcryptRounds'),
      );

      await this.prisma.user.update({
        where: { id: userId },
        data: {
          passwordHash: hashedPassword,
          passwordChangedAt: new Date(),
        },
      });

      // Revoke all existing sessions except current one
      await this.revokeAllSessions(userId, 'Password changed');

      this.logger.info(`Password changed for user ${user.email}`);
    } catch (error) {
      this.logger.error('Password change failed', error);
      throw error;
    }
  }

  async forgotPassword(forgotPasswordDto: ForgotPasswordDto): Promise<void> {
    try {
      const user = await this.usersService.findByEmail(forgotPasswordDto.email);
      
      if (!user) {
        // Don't reveal if email exists
        return;
      }

      const resetToken = crypto.randomBytes(32).toString('hex');
      const resetExpires = new Date(Date.now() + 3600000); // 1 hour

      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          passwordResetToken: resetToken,
          passwordResetExpires: resetExpires,
        },
      });

      // TODO: Send password reset email
      this.logger.info(`Password reset requested for user ${user.email}`);
    } catch (error) {
      this.logger.error('Forgot password failed', error);
      throw error;
    }
  }

  async resetPassword(resetPasswordDto: ResetPasswordDto): Promise<void> {
    try {
      const user = await this.prisma.user.findFirst({
        where: {
          passwordResetToken: resetPasswordDto.token,
          passwordResetExpires: {
            gt: new Date(),
          },
        },
      });

      if (!user) {
        throw new BadRequestException('Invalid or expired reset token');
      }

      if (resetPasswordDto.newPassword !== resetPasswordDto.confirmPassword) {
        throw new BadRequestException('Passwords do not match');
      }

      const hashedPassword = await bcrypt.hash(
        resetPasswordDto.newPassword,
        this.configService.get<number>('security.bcryptRounds'),
      );

      await this.prisma.user.update({
        where: { id: user.id },
        data: {
          passwordHash: hashedPassword,
          passwordChangedAt: new Date(),
          passwordResetToken: null,
          passwordResetExpires: null,
          failedLoginAttempts: 0,
          lockedUntil: null,
        },
      });

      // Revoke all existing sessions
      await this.revokeAllSessions(user.id, 'Password reset');

      this.logger.info(`Password reset completed for user ${user.email}`);
    } catch (error) {
      this.logger.error('Password reset failed', error);
      throw error;
    }
  }

  private async handleFailedLogin(userId: string): Promise<void> {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user) return;

    const failedAttempts = user.failedLoginAttempts + 1;
    const maxAttempts = 5;
    const lockoutDuration = 15 * 60 * 1000; // 15 minutes

    const updateData: any = {
      failedLoginAttempts: failedAttempts,
    };

    if (failedAttempts >= maxAttempts) {
      updateData.lockedUntil = new Date(Date.now() + lockoutDuration);
    }

    await this.prisma.user.update({
      where: { id: userId },
      data: updateData,
    });
  }

  private async handleSuccessfulLogin(userId: string): Promise<void> {
    await this.prisma.user.update({
      where: { id: userId },
      data: {
        lastLogin: new Date(),
        loginCount: {
          increment: 1,
        },
        failedLoginAttempts: 0,
        lockedUntil: null,
      },
    });
  }

  private async generateRefreshToken(userId: string): Promise<string> {
    const token = crypto.randomBytes(32).toString('hex');
    return token;
  }

  private async createSession(
    userId: string,
    sessionToken: string,
    ip: string,
    userAgent: string,
  ): Promise<void> {
    const expiresAt = new Date();
    expiresAt.setDate(expiresAt.getDate() + 7); // 7 days

    await this.prisma.userSession.create({
      data: {
        userId,
        sessionToken,
        ipAddress: ip,
        userAgent,
        expiresAt,
      },
    });
  }

  private async revokeAllSessions(userId: string, reason: string): Promise<void> {
    await this.prisma.userSession.updateMany({
      where: {
        userId,
        isActive: true,
      },
      data: {
        isActive: false,
        revokedAt: new Date(),
        revokedReason: reason,
      },
    });
  }

  private async validateMfaCode(userId: string, code: string): Promise<boolean> {
    // TODO: Implement MFA validation (TOTP)
    // For now, return true if code is provided
    return code.length === 6;
  }

  private getTokenExpirationTime(): number {
    const expiresIn = this.configService.get<string>('jwt.expiresIn');
    // Convert to seconds (assuming format like '1h', '30m', etc.)
    if (expiresIn.endsWith('h')) {
      return parseInt(expiresIn) * 3600;
    } else if (expiresIn.endsWith('m')) {
      return parseInt(expiresIn) * 60;
    } else if (expiresIn.endsWith('s')) {
      return parseInt(expiresIn);
    }
    return 3600; // Default 1 hour
  }
}
