"use strict";var i=Object.defineProperty;var n=Object.getOwnPropertyDescriptor;var h=Object.getOwnPropertyNames;var t=Object.prototype.hasOwnProperty;var d=(r,e)=>{for(var o in e)i(r,o,{get:e[o],enumerable:!0})},l=(r,e,o,a)=>{if(e&&typeof e=="object"||typeof e=="function")for(let s of h(e))!t.call(r,s)&&s!==o&&i(r,s,{get:()=>e[s],enumerable:!(a=n(e,s))||a.enumerable});return r};var u=r=>l(i({},"__esModule",{value:!0}),r);var m={};d(m,{default:()=>y});module.exports=u(m);var y=["Avon","Bedfordshire","Berkshire","Borders","Buckinghamshire","Cambridgeshire","Central","Cheshire","Cleveland","Clwyd","Cornwall","County Antrim","County Armagh","County Down","County Fermanagh","County Londonderry","County Tyrone","Cumbria","Derbyshire","Devon","Dorset","Dumfries and Galloway","Durham","Dyfed","East Sussex","Essex","Fife","Gloucestershire","Grampian","Greater Manchester","Gwent","Gwynedd County","Hampshire","Herefordshire","Hertfordshire","Highlands and Islands","Humberside","Isle of Wight","Kent","Lancashire","Leicestershire","Lincolnshire","Lothian","Merseyside","Mid Glamorgan","Norfolk","North Yorkshire","Northamptonshire","Northumberland","Nottinghamshire","Oxfordshire","Powys","Rutland","Shropshire","Somerset","South Glamorgan","South Yorkshire","Staffordshire","Strathclyde","Suffolk","Surrey","Tayside","Tyne and Wear","Warwickshire","West Glamorgan","West Midlands","West Sussex","West Yorkshire","Wiltshire","Worcestershire"];
