import React from 'react';
import { useQuery } from '@tanstack/react-query';
import { Helmet } from 'react-helmet-async';
import {
  EnvelopeIcon,
  UserGroupIcon,
  ServerIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon,
  ClockIcon,
  XCircleIcon,
} from '@heroicons/react/24/outline';

import { healthApi, usersApi, emailTransactionsApi, securityApi } from '../../services/api';
import LoadingSpinner from '../../components/LoadingSpinner';
import MetricCard from '../../components/MetricCard';
import StatusBadge from '../../components/StatusBadge';

export default function DashboardPage() {
  // Fetch dashboard data
  const { data: healthData, isLoading: healthLoading } = useQuery({
    queryKey: ['health'],
    queryFn: healthApi.getHealth,
    refetchInterval: 30000, // Refresh every 30 seconds
  });

  const { data: userStats, isLoading: userStatsLoading } = useQuery({
    queryKey: ['users', 'stats'],
    queryFn: usersApi.getUserStats,
  });

  const { data: emailStats, isLoading: emailStatsLoading } = useQuery({
    queryKey: ['email-transactions', 'stats'],
    queryFn: emailTransactionsApi.getTransactionStats,
  });

  const { data: securityStats, isLoading: securityStatsLoading } = useQuery({
    queryKey: ['security', 'stats'],
    queryFn: securityApi.getSecurityStats,
  });

  const isLoading = healthLoading || userStatsLoading || emailStatsLoading || securityStatsLoading;

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  return (
    <>
      <Helmet>
        <title>Dashboard - IronRelay</title>
      </Helmet>

      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
            Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400">
            Overview of your SMTP relay server status and metrics
          </p>
        </div>

        {/* System Status */}
        <div className="card">
          <div className="card-header">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
              System Status
            </h2>
          </div>
          <div className="card-body">
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                {healthData?.status === 'healthy' ? (
                  <CheckCircleIcon className="h-6 w-6 text-success-500 mr-2" />
                ) : (
                  <ExclamationTriangleIcon className="h-6 w-6 text-error-500 mr-2" />
                )}
                <span className="text-lg font-medium text-gray-900 dark:text-gray-100">
                  {healthData?.status === 'healthy' ? 'All Systems Operational' : 'System Issues Detected'}
                </span>
              </div>
              <StatusBadge
                status={healthData?.status === 'healthy' ? 'success' : 'error'}
                text={healthData?.status || 'Unknown'}
              />
            </div>
            
            {healthData?.checks && (
              <div className="mt-4 grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Database</span>
                  <StatusBadge
                    status={healthData.checks.database?.status === 'healthy' ? 'success' : 'error'}
                    text={healthData.checks.database?.status || 'Unknown'}
                  />
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm text-gray-600 dark:text-gray-400">SMTP Engine</span>
                  <StatusBadge
                    status={healthData.checks.smtpEngine?.status === 'healthy' ? 'success' : 'error'}
                    text={healthData.checks.smtpEngine?.status || 'Unknown'}
                  />
                </div>
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Memory</span>
                  <StatusBadge
                    status={healthData.checks.memory?.status === 'healthy' ? 'success' : 'warning'}
                    text={healthData.checks.memory?.status || 'Unknown'}
                  />
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {/* Email Metrics */}
          <MetricCard
            title="Total Emails"
            value={emailStats?.total || 0}
            icon={EnvelopeIcon}
            color="primary"
            change={undefined}
          />
          
          <MetricCard
            title="Delivered"
            value={emailStats?.delivered || 0}
            icon={CheckCircleIcon}
            color="success"
            change={undefined}
          />
          
          <MetricCard
            title="Pending"
            value={emailStats?.pending || 0}
            icon={ClockIcon}
            color="warning"
            change={undefined}
          />
          
          <MetricCard
            title="Failed"
            value={emailStats?.failed || 0}
            icon={XCircleIcon}
            color="error"
            change={undefined}
          />
        </div>

        {/* Secondary Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <MetricCard
            title="Total Users"
            value={userStats?.totalUsers || 0}
            icon={UserGroupIcon}
            color="primary"
            change={undefined}
          />
          
          <MetricCard
            title="Active Users"
            value={userStats?.activeUsers || 0}
            icon={UserGroupIcon}
            color="success"
            change={undefined}
          />
          
          <MetricCard
            title="Security Events"
            value={securityStats?.totalEvents || 0}
            icon={ExclamationTriangleIcon}
            color="warning"
            change={undefined}
          />
          
          <MetricCard
            title="Blocked IPs"
            value={securityStats?.blockedIPs || 0}
            icon={ExclamationTriangleIcon}
            color="error"
            change={undefined}
          />
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Email Activity */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                Email Activity
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Delivery Rate</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {emailStats?.total ? Math.round((emailStats.delivered / emailStats.total) * 100) : 0}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Bounce Rate</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {emailStats?.total ? Math.round((emailStats.bounced / emailStats.total) * 100) : 0}%
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Queue Depth</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {emailStats?.pending || 0}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* System Info */}
          <div className="card">
            <div className="card-header">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                System Information
              </h3>
            </div>
            <div className="card-body">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Uptime</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {healthData?.uptime ? Math.floor(healthData.uptime / 3600) : 0}h
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Version</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {healthData?.version || '1.0.0'}
                  </span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600 dark:text-gray-400">Environment</span>
                  <span className="text-sm font-medium text-gray-900 dark:text-gray-100">
                    {healthData?.environment || 'production'}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
