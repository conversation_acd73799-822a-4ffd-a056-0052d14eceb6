{"name": "@types/hpp", "version": "0.2.6", "description": "TypeScript definitions for hpp", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/hpp", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "kryops", "url": "https://github.com/kryops"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/hpp"}, "scripts": {}, "dependencies": {"@types/express": "*"}, "typesPublisherContentHash": "4c745e41069ce627d6021ab6e7bda500d05e247669e5c0d7ea2323948e7a17f7", "typeScriptVersion": "4.6"}