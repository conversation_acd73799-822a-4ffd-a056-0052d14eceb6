version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ironrelay-postgres
    restart: unless-stopped
    environment:
      POSTGRES_DB: ironrelay
      POSTGRES_USER: ironrelay
      POSTGRES_PASSWORD: ironrelay
      PGDATA: /var/lib/postgresql/data/pgdata
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - ironrelay-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ironrelay -d ironrelay"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (for caching and sessions)
  redis:
    image: redis:7-alpine
    container_name: ironrelay-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ironrelay
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - ironrelay-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Backend API Service
  api:
    build:
      context: .
      dockerfile: docker/api/Dockerfile
      target: development
    container_name: ironrelay-api
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: **********************************************/ironrelay?schema=public
      REDIS_URL: redis://:ironrelay@redis:6379
      JWT_SECRET: dev-jwt-secret-change-in-production
      API_PORT: 3000
      SMTP_PORT: 2525
      LOG_LEVEL: info
      ENABLE_SWAGGER: "true"
      ENABLE_CORS: "true"
      CORS_ORIGIN: http://localhost:3001
    volumes:
      - ./packages/api:/app
      - /app/node_modules
      - ./logs:/app/logs
      - ./certs:/app/certs
    ports:
      - "3000:3000"
      - "2525:2525"
    networks:
      - ironrelay-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Frontend Web Application
  frontend:
    build:
      context: .
      dockerfile: docker/frontend/Dockerfile
      target: development
    container_name: ironrelay-frontend
    restart: unless-stopped
    environment:
      NODE_ENV: development
      REACT_APP_API_URL: http://localhost:3000
      REACT_APP_WS_URL: ws://localhost:3000
      PORT: 3001
    volumes:
      - ./packages/frontend:/app
      - /app/node_modules
    ports:
      - "3001:3001"
    networks:
      - ironrelay-network
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001"]
      interval: 30s
      timeout: 10s
      retries: 3

  # SMTP Engine Service
  smtp-engine:
    build:
      context: .
      dockerfile: docker/smtp-engine/Dockerfile
      target: development
    container_name: ironrelay-smtp
    restart: unless-stopped
    environment:
      NODE_ENV: development
      DATABASE_URL: **********************************************/ironrelay?schema=public
      REDIS_URL: redis://:ironrelay@redis:6379
      SMTP_PORT: 2525
      SMTP_HOST: 0.0.0.0
      LOG_LEVEL: info
      API_URL: http://api:3000
    volumes:
      - ./packages/smtp-engine:/app
      - /app/node_modules
      - ./logs:/app/logs
      - ./certs:/app/certs
    ports:
      - "2526:2525"  # Different port to avoid conflict with API
    networks:
      - ironrelay-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
      api:
        condition: service_healthy

  # Prometheus (Metrics Collection)
  prometheus:
    image: prom/prometheus:latest
    container_name: ironrelay-prometheus
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
    volumes:
      - ./docker/prometheus/prometheus.yml:/etc/prometheus/prometheus.yml
      - prometheus_data:/prometheus
    ports:
      - "9090:9090"
    networks:
      - ironrelay-network
    depends_on:
      - api

  # Grafana (Metrics Visualization)
  grafana:
    image: grafana/grafana:latest
    container_name: ironrelay-grafana
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_USER: admin
      GF_SECURITY_ADMIN_PASSWORD: admin
      GF_USERS_ALLOW_SIGN_UP: "false"
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards
    ports:
      - "3002:3000"
    networks:
      - ironrelay-network
    depends_on:
      - prometheus

  # Nginx (Reverse Proxy)
  nginx:
    image: nginx:alpine
    container_name: ironrelay-nginx
    restart: unless-stopped
    volumes:
      - ./docker/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./docker/nginx/conf.d:/etc/nginx/conf.d
      - ./certs:/etc/nginx/certs
    ports:
      - "80:80"
      - "443:443"
    networks:
      - ironrelay-network
    depends_on:
      - api
      - frontend

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local

networks:
  ironrelay-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
