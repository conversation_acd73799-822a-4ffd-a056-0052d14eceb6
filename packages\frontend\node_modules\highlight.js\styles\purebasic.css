/*

PureBASIC native IDE style ( version 1.0 - April 2016 )

by <PERSON><PERSON> <<EMAIL>>

Public Domain

NOTE_1:	PureBASIC code syntax highlighting only applies the following classes:
			.hljs-comment
			.hljs-function
			.hljs-keywords
			.hljs-string
			.hljs-symbol

		Other classes are added here for the benefit of styling other languages with the look and feel of PureBASIC native IDE style.
		If you need to customize a stylesheet for PureBASIC only, remove all non-relevant classes -- PureBASIC-related classes are followed by
		a "--- used for PureBASIC ... ---" comment on same line.

NOTE_2:	Color names provided in comments were derived using "Name that Color" online tool:
			http://chir.ag/projects/name-that-color
*/

.hljs {
	display: block;
	overflow-x: auto;
	padding: 0.5em;
	background: #FFFFDF; /* Half and Half (approx.) */
/* --- Uncomment to add PureBASIC native IDE styled font!
	font-family: Consolas;
*/
}

.hljs, /* --- used for PureBASIC base color --- */
.hljs-type,  /* --- used for PureBASIC Procedures return type --- */
.hljs-function, /* --- used for wrapping PureBASIC Procedures definitions --- */
.hljs-name,
.hljs-number,
.hljs-attr,
.hljs-params,
.hljs-subst {
	color: #000000; /* Black */
}

.hljs-comment, /* --- used for PureBASIC Comments --- */
.hljs-regexp,
.hljs-section,
.hljs-selector-pseudo,
.hljs-addition {
	color: #00AAAA; /* Persian Green (approx.) */
}

.hljs-title, /* --- used for PureBASIC Procedures Names --- */
.hljs-tag,
.hljs-variable,
.hljs-code  {
	color: #006666; /* Blue Stone (approx.) */
}

.hljs-keyword, /* --- used for PureBASIC Keywords --- */
.hljs-class,
.hljs-meta-keyword,
.hljs-selector-class,
.hljs-built_in,
.hljs-builtin-name {
	color: #006666; /* Blue Stone (approx.) */
	font-weight: bold;
}

.hljs-string, /* --- used for PureBASIC Strings --- */
.hljs-selector-attr {
	color: #0080FF; /* Azure Radiance (approx.) */
}

.hljs-symbol, /* --- used for PureBASIC Constants --- */
.hljs-link,
.hljs-deletion,
.hljs-attribute {
	color: #924B72; /* Cannon Pink (approx.) */
}

.hljs-meta,
.hljs-literal,
.hljs-selector-id {
	color: #924B72; /* Cannon Pink (approx.) */
	font-weight: bold;
}

.hljs-strong,
.hljs-name {
	font-weight: bold;
}

.hljs-emphasis {
	font-style: italic;
}
