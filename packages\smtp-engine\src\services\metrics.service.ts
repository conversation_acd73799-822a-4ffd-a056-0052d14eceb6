import * as promClient from 'prom-client';
import * as http from 'http';
import { Logger } from '../utils/logger';
import { Config } from '../config/config';

export class MetricsService {
  private static instance: MetricsService;
  private logger = Logger.getInstance();
  private config = Config.getInstance();
  private server?: http.Server;
  private register: promClient.Registry;

  // SMTP Metrics
  private smtpConnectionsTotal: promClient.Counter;
  private smtpConnectionsActive: promClient.Gauge;
  private smtpConnectionDuration: promClient.Histogram;
  private smtpMessagesTotal: promClient.Counter;
  private smtpMessageSize: promClient.Histogram;
  private smtpProcessingTime: promClient.Histogram;
  private smtpDeliveryTime: promClient.Histogram;
  private smtpQueueDepth: promClient.Gauge;
  private smtpErrorsTotal: promClient.Counter;
  private smtpAuthAttempts: promClient.Counter;
  private smtpRateLimitHits: promClient.Counter;

  // System Metrics
  private systemUptime: promClient.Gauge;
  private systemMemoryUsage: promClient.Gauge;
  private systemCpuUsage: promClient.Gauge;

  private constructor() {
    this.register = new promClient.Registry();
    this.initializeMetrics();
  }

  public static getInstance(): MetricsService {
    if (!MetricsService.instance) {
      MetricsService.instance = new MetricsService();
    }
    return MetricsService.instance;
  }

  private initializeMetrics(): void {
    // SMTP Connection Metrics
    this.smtpConnectionsTotal = new promClient.Counter({
      name: 'smtp_connections_total',
      help: 'Total number of SMTP connections',
      labelNames: ['status', 'remote_ip'],
      registers: [this.register],
    });

    this.smtpConnectionsActive = new promClient.Gauge({
      name: 'smtp_connections_active',
      help: 'Number of active SMTP connections',
      registers: [this.register],
    });

    this.smtpConnectionDuration = new promClient.Histogram({
      name: 'smtp_connection_duration_seconds',
      help: 'Duration of SMTP connections in seconds',
      buckets: [0.1, 0.5, 1, 5, 10, 30, 60, 300],
      registers: [this.register],
    });

    // SMTP Message Metrics
    this.smtpMessagesTotal = new promClient.Counter({
      name: 'smtp_messages_total',
      help: 'Total number of SMTP messages processed',
      labelNames: ['status', 'auth_user'],
      registers: [this.register],
    });

    this.smtpMessageSize = new promClient.Histogram({
      name: 'smtp_message_size_bytes',
      help: 'Size of SMTP messages in bytes',
      buckets: [1024, 10240, 102400, 1048576, 10485760, 26214400], // 1KB to 25MB
      registers: [this.register],
    });

    this.smtpProcessingTime = new promClient.Histogram({
      name: 'smtp_processing_time_seconds',
      help: 'Time spent processing SMTP messages',
      buckets: [0.001, 0.01, 0.1, 0.5, 1, 5, 10],
      registers: [this.register],
    });

    this.smtpDeliveryTime = new promClient.Histogram({
      name: 'smtp_delivery_time_seconds',
      help: 'Time spent delivering SMTP messages',
      buckets: [0.1, 0.5, 1, 5, 10, 30, 60, 300],
      registers: [this.register],
    });

    // SMTP Queue Metrics
    this.smtpQueueDepth = new promClient.Gauge({
      name: 'smtp_queue_depth',
      help: 'Number of messages in the SMTP queue',
      labelNames: ['status'],
      registers: [this.register],
    });

    // SMTP Error Metrics
    this.smtpErrorsTotal = new promClient.Counter({
      name: 'smtp_errors_total',
      help: 'Total number of SMTP errors',
      labelNames: ['type', 'code'],
      registers: [this.register],
    });

    // SMTP Authentication Metrics
    this.smtpAuthAttempts = new promClient.Counter({
      name: 'smtp_auth_attempts_total',
      help: 'Total number of SMTP authentication attempts',
      labelNames: ['method', 'status'],
      registers: [this.register],
    });

    // SMTP Rate Limiting Metrics
    this.smtpRateLimitHits = new promClient.Counter({
      name: 'smtp_rate_limit_hits_total',
      help: 'Total number of rate limit hits',
      labelNames: ['type', 'remote_ip'],
      registers: [this.register],
    });

    // System Metrics
    this.systemUptime = new promClient.Gauge({
      name: 'system_uptime_seconds',
      help: 'System uptime in seconds',
      registers: [this.register],
    });

    this.systemMemoryUsage = new promClient.Gauge({
      name: 'system_memory_usage_bytes',
      help: 'System memory usage in bytes',
      labelNames: ['type'],
      registers: [this.register],
    });

    this.systemCpuUsage = new promClient.Gauge({
      name: 'system_cpu_usage_percent',
      help: 'System CPU usage percentage',
      registers: [this.register],
    });

    // Collect default metrics
    promClient.collectDefaultMetrics({ register: this.register });
  }

  public async initialize(): Promise<void> {
    if (!this.config.get<boolean>('metrics.enabled')) {
      this.logger.info('Metrics collection is disabled');
      return;
    }

    try {
      const port = this.config.get<number>('metrics.port');
      
      this.server = http.createServer(async (req, res) => {
        if (req.url === '/metrics') {
          res.setHeader('Content-Type', this.register.contentType);
          res.end(await this.register.metrics());
        } else if (req.url === '/health') {
          res.setHeader('Content-Type', 'application/json');
          res.end(JSON.stringify({ status: 'healthy', timestamp: new Date().toISOString() }));
        } else {
          res.statusCode = 404;
          res.end('Not Found');
        }
      });

      this.server.listen(port, () => {
        this.logger.info(`Metrics server started on port ${port}`);
      });

      // Start collecting system metrics
      this.startSystemMetricsCollection();

    } catch (error) {
      this.logger.error('Failed to initialize metrics service', error);
      throw error;
    }
  }

  public async stop(): Promise<void> {
    if (this.server) {
      return new Promise((resolve) => {
        this.server!.close(() => {
          this.logger.info('Metrics server stopped');
          resolve();
        });
      });
    }
  }

  private startSystemMetricsCollection(): void {
    const collectSystemMetrics = (): void => {
      try {
        // Update uptime
        this.systemUptime.set(process.uptime());

        // Update memory usage
        const memUsage = process.memoryUsage();
        this.systemMemoryUsage.set({ type: 'rss' }, memUsage.rss);
        this.systemMemoryUsage.set({ type: 'heapTotal' }, memUsage.heapTotal);
        this.systemMemoryUsage.set({ type: 'heapUsed' }, memUsage.heapUsed);
        this.systemMemoryUsage.set({ type: 'external' }, memUsage.external);

        // Update CPU usage (simplified)
        const cpuUsage = process.cpuUsage();
        const totalUsage = (cpuUsage.user + cpuUsage.system) / 1000000; // Convert to seconds
        this.systemCpuUsage.set(totalUsage);

      } catch (error) {
        this.logger.error('Failed to collect system metrics', error);
      }
    };

    // Collect metrics every 30 seconds
    setInterval(collectSystemMetrics, 30000);
    collectSystemMetrics(); // Initial collection
  }

  // SMTP Connection Metrics Methods
  public recordConnection(status: 'accepted' | 'rejected', remoteIP: string): void {
    this.smtpConnectionsTotal.inc({ status, remote_ip: remoteIP });
  }

  public incrementActiveConnections(): void {
    this.smtpConnectionsActive.inc();
  }

  public decrementActiveConnections(): void {
    this.smtpConnectionsActive.dec();
  }

  public recordConnectionDuration(duration: number): void {
    this.smtpConnectionDuration.observe(duration);
  }

  // SMTP Message Metrics Methods
  public recordMessage(status: 'received' | 'delivered' | 'failed' | 'rejected', authUser?: string): void {
    this.smtpMessagesTotal.inc({ status, auth_user: authUser || 'anonymous' });
  }

  public recordMessageSize(size: number): void {
    this.smtpMessageSize.observe(size);
  }

  public recordProcessingTime(time: number): void {
    this.smtpProcessingTime.observe(time / 1000); // Convert ms to seconds
  }

  public recordDeliveryTime(time: number): void {
    this.smtpDeliveryTime.observe(time / 1000); // Convert ms to seconds
  }

  // SMTP Queue Metrics Methods
  public setQueueDepth(depth: number, status: 'pending' | 'processing' | 'failed'): void {
    this.smtpQueueDepth.set({ status }, depth);
  }

  // SMTP Error Metrics Methods
  public recordError(type: string, code?: string): void {
    this.smtpErrorsTotal.inc({ type, code: code || 'unknown' });
  }

  // SMTP Authentication Metrics Methods
  public recordAuthAttempt(method: string, status: 'success' | 'failure'): void {
    this.smtpAuthAttempts.inc({ method, status });
  }

  // SMTP Rate Limiting Metrics Methods
  public recordRateLimitHit(type: 'ip' | 'user', remoteIP: string): void {
    this.smtpRateLimitHits.inc({ type, remote_ip: remoteIP });
  }

  // Get metrics for API endpoints
  public async getMetrics(): Promise<string> {
    return this.register.metrics();
  }

  public getRegister(): promClient.Registry {
    return this.register;
  }

  // Custom metric recording for specific events
  public recordCustomMetric(name: string, value: number, labels?: Record<string, string>): void {
    try {
      // This is a simplified implementation
      // In a real implementation, you might want to create custom metrics dynamically
      this.logger.debug(`Custom metric: ${name} = ${value}`, labels);
    } catch (error) {
      this.logger.error('Failed to record custom metric', error);
    }
  }
}
