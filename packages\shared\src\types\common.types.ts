import { z } from 'zod';

// Common utility types
export type UUID = string;
export type Timestamp = Date;
export type EmailAddress = string;
export type IPAddress = string;
export type Domain = string;

// Pagination types
export interface PaginationParams {
  page: number;
  limit: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// API Response types
export interface ApiResponse<T = unknown> {
  success: boolean;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: unknown;
  };
  timestamp: Timestamp;
  requestId: string;
}

// Error types
export enum ErrorCode {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  AUTHENTICATION_ERROR = 'AUTHENTICATION_ERROR',
  AUTHORIZATION_ERROR = 'AUTHORIZATION_ERROR',
  NOT_FOUND = 'NOT_FOUND',
  CONFLICT = 'CONFLICT',
  RATE_LIMIT_EXCEEDED = 'RATE_LIMIT_EXCEEDED',
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  SMTP_ERROR = 'SMTP_ERROR',
  TLS_ERROR = 'TLS_ERROR',
  LICENSE_ERROR = 'LICENSE_ERROR',
}

export interface IronRelayError {
  code: ErrorCode;
  message: string;
  details?: Record<string, unknown>;
  timestamp: Timestamp;
  requestId?: string;
  userId?: UUID;
}

// Validation schemas using Zod
export const UUIDSchema = z.string().uuid();
export const EmailSchema = z.string().email();
export const IPAddressSchema = z.string().ip();
export const DomainSchema = z.string().regex(/^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/);

export const PaginationParamsSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(20),
  sortBy: z.string().optional(),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Health check types
export interface HealthStatus {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: Timestamp;
  uptime: number;
  version: string;
  services: Record<string, ServiceHealth>;
}

export interface ServiceHealth {
  status: 'healthy' | 'unhealthy' | 'degraded';
  responseTime?: number;
  lastCheck: Timestamp;
  error?: string;
}

// Configuration types
export interface BaseConfig {
  id: UUID;
  name: string;
  description?: string;
  enabled: boolean;
  createdAt: Timestamp;
  updatedAt: Timestamp;
  createdBy: UUID;
  updatedBy: UUID;
}

// Audit log types
export interface AuditLog {
  id: UUID;
  action: string;
  resource: string;
  resourceId?: UUID;
  userId: UUID;
  userEmail: EmailAddress;
  timestamp: Timestamp;
  ipAddress: IPAddress;
  userAgent?: string;
  changes?: Record<string, { old: unknown; new: unknown }>;
  metadata?: Record<string, unknown>;
}

export enum AuditAction {
  CREATE = 'CREATE',
  UPDATE = 'UPDATE',
  DELETE = 'DELETE',
  LOGIN = 'LOGIN',
  LOGOUT = 'LOGOUT',
  PASSWORD_CHANGE = 'PASSWORD_CHANGE',
  PERMISSION_GRANT = 'PERMISSION_GRANT',
  PERMISSION_REVOKE = 'PERMISSION_REVOKE',
  CONFIG_CHANGE = 'CONFIG_CHANGE',
  CERTIFICATE_UPLOAD = 'CERTIFICATE_UPLOAD',
  CERTIFICATE_RENEWAL = 'CERTIFICATE_RENEWAL',
  SMTP_START = 'SMTP_START',
  SMTP_STOP = 'SMTP_STOP',
  SMTP_RESTART = 'SMTP_RESTART',
}

// File upload types
export interface FileUpload {
  filename: string;
  mimetype: string;
  size: number;
  buffer: Buffer;
}

export interface UploadedFile {
  id: UUID;
  filename: string;
  originalName: string;
  mimetype: string;
  size: number;
  path: string;
  uploadedBy: UUID;
  uploadedAt: Timestamp;
}

// Environment types
export type Environment = 'development' | 'staging' | 'production';

export interface EnvironmentConfig {
  NODE_ENV: Environment;
  LOG_LEVEL: 'error' | 'warn' | 'info' | 'debug';
  DATABASE_URL: string;
  REDIS_URL?: string;
  JWT_SECRET: string;
  API_PORT: number;
  SMTP_PORT: number;
  FRONTEND_PORT?: number;
  TLS_CERT_PATH?: string;
  TLS_KEY_PATH?: string;
  PROMETHEUS_PORT?: number;
  GRAFANA_PORT?: number;
}
