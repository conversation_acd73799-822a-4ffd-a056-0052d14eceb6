import { z } from 'zod';
import { <PERSON>UID, Timestamp, PaginationParams, PaginatedResponse, ApiResponse } from './common.types';
import { SMTPConfig, DomainRoute, SMTPUser, EmailTransaction } from './smtp.types';
import { User, <PERSON><PERSON><PERSON><PERSON>, UserSession } from './user.types';
import { SystemConfig, TLSCertificate, NotificationConfig } from './config.types';
import { SystemMetrics, SMTPMetrics, APIMetrics, Alert, Dashboard } from './monitoring.types';

// API Request/Response types for all endpoints

// Authentication endpoints
export interface LoginRequest {
  email: string;
  password: string;
  mfaCode?: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: Omit<User, 'passwordHash' | 'mfaSecret'>;
  accessToken: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface RefreshTokenResponse {
  accessToken: string;
  expiresIn: number;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

export interface ForgotPasswordRequest {
  email: string;
}

export interface ResetPasswordRequest {
  token: string;
  newPassword: string;
  confirmPassword: string;
}

// User management endpoints
export interface CreateUserRequest {
  email: string;
  firstName: string;
  lastName: string;
  password: string;
  role: string;
  timezone?: string;
  language?: string;
}

export interface UpdateUserRequest {
  firstName?: string;
  lastName?: string;
  role?: string;
  isActive?: boolean;
  timezone?: string;
  language?: string;
  theme?: 'light' | 'dark' | 'auto';
  emailNotifications?: boolean;
  securityAlerts?: boolean;
}

export interface GetUsersRequest extends PaginationParams {
  search?: string;
  role?: string;
  isActive?: boolean;
}

export type GetUsersResponse = PaginatedResponse<Omit<User, 'passwordHash' | 'mfaSecret'>>;

// API Key management
export interface CreateApiKeyRequest {
  name: string;
  description?: string;
  permissions: string[];
  expiresAt?: string;
  ipWhitelist?: string[];
  rateLimitPerHour?: number;
  rateLimitPerDay?: number;
}

export interface CreateApiKeyResponse {
  apiKey: Omit<ApiKey, 'keyHash'>;
  key: string; // Only returned once
}

export interface GetApiKeysRequest extends PaginationParams {
  search?: string;
  userId?: UUID;
}

export type GetApiKeysResponse = PaginatedResponse<Omit<ApiKey, 'keyHash'>>;

// SMTP Configuration endpoints
export interface UpdateSMTPConfigRequest {
  hostname?: string;
  port?: number;
  maxConnections?: number;
  maxConnectionsPerIP?: number;
  connectionTimeout?: number;
  tlsEnabled?: boolean;
  tlsRequired?: boolean;
  authRequired?: boolean;
  authMethods?: string[];
  ipWhitelist?: string[];
  ipBlacklist?: string[];
  cidrWhitelist?: string[];
  cidrBlacklist?: string[];
  rateLimitEnabled?: boolean;
  rateLimitPerIP?: number;
  rateLimitPerUser?: number;
  rateLimitWindow?: number;
  maxMessageSize?: number;
  maxRecipients?: number;
  queueEnabled?: boolean;
  queueRetryAttempts?: number;
  queueRetryDelay?: number;
  queueMaxAge?: number;
}

export type GetSMTPConfigResponse = ApiResponse<SMTPConfig>;

// Domain routing endpoints
export interface CreateDomainRouteRequest {
  name: string;
  description?: string;
  domain: string;
  destinationHost: string;
  destinationPort: number;
  destinationTLS: boolean;
  destinationAuth?: {
    username: string;
    password: string;
  };
  priority: number;
  maxRetries: number;
  retryDelay: number;
  enabled: boolean;
}

export interface UpdateDomainRouteRequest {
  name?: string;
  description?: string;
  domain?: string;
  destinationHost?: string;
  destinationPort?: number;
  destinationTLS?: boolean;
  destinationAuth?: {
    username: string;
    password: string;
  };
  priority?: number;
  maxRetries?: number;
  retryDelay?: number;
  enabled?: boolean;
}

export interface GetDomainRoutesRequest extends PaginationParams {
  search?: string;
  domain?: string;
  enabled?: boolean;
}

export type GetDomainRoutesResponse = PaginatedResponse<DomainRoute>;

// SMTP Users endpoints
export interface CreateSMTPUserRequest {
  name: string;
  description?: string;
  username: string;
  email: string;
  password: string;
  isActive: boolean;
  allowedDomains: string[];
  allowedIPs: string[];
  customRateLimit?: number;
  customRateLimitWindow?: number;
  enabled: boolean;
}

export interface UpdateSMTPUserRequest {
  name?: string;
  description?: string;
  username?: string;
  email?: string;
  password?: string;
  isActive?: boolean;
  allowedDomains?: string[];
  allowedIPs?: string[];
  customRateLimit?: number;
  customRateLimitWindow?: number;
  enabled?: boolean;
}

export interface GetSMTPUsersRequest extends PaginationParams {
  search?: string;
  username?: string;
  email?: string;
  isActive?: boolean;
}

export type GetSMTPUsersResponse = PaginatedResponse<Omit<SMTPUser, 'passwordHash'>>;

// Email transaction endpoints
export interface GetEmailTransactionsRequest extends PaginationParams {
  search?: string;
  status?: string;
  senderIP?: string;
  mailFrom?: string;
  rcptTo?: string;
  authenticatedUser?: UUID;
  dateFrom?: string;
  dateTo?: string;
}

export type GetEmailTransactionsResponse = PaginatedResponse<EmailTransaction>;

export interface GetEmailTransactionStatsRequest {
  dateFrom?: string;
  dateTo?: string;
  groupBy?: 'hour' | 'day' | 'week' | 'month';
}

export interface EmailTransactionStats {
  period: string;
  received: number;
  delivered: number;
  failed: number;
  queued: number;
  bounced: number;
  rejected: number;
}

export type GetEmailTransactionStatsResponse = ApiResponse<EmailTransactionStats[]>;

// TLS Certificate endpoints
export interface UploadCertificateRequest {
  name: string;
  description?: string;
  commonName: string;
  domains: string[];
  certificate: string; // PEM format
  privateKey: string; // PEM format
  chain?: string; // PEM format
  isDefault: boolean;
  autoRenew: boolean;
  renewalDays: number;
}

export interface UpdateCertificateRequest {
  name?: string;
  description?: string;
  domains?: string[];
  isDefault?: boolean;
  autoRenew?: boolean;
  renewalDays?: number;
  enabled?: boolean;
}

export interface GetCertificatesRequest extends PaginationParams {
  search?: string;
  status?: string;
  expiringDays?: number;
}

export type GetCertificatesResponse = PaginatedResponse<TLSCertificate>;

// System configuration endpoints
export interface UpdateSystemConfigRequest {
  instanceName?: string;
  instanceDescription?: string;
  timezone?: string;
  logLevel?: 'error' | 'warn' | 'info' | 'debug';
  logRetentionDays?: number;
  logMaxSize?: number;
  sessionTimeout?: number;
  apiRateLimit?: number;
  loginRateLimit?: number;
  backupEnabled?: boolean;
  backupSchedule?: string;
  backupRetentionDays?: number;
  metricsEnabled?: boolean;
  metricsRetentionDays?: number;
  alertingEnabled?: boolean;
}

export type GetSystemConfigResponse = ApiResponse<SystemConfig>;

// Monitoring endpoints
export interface GetMetricsRequest {
  type: 'system' | 'smtp' | 'api';
  dateFrom?: string;
  dateTo?: string;
  interval?: '1m' | '5m' | '15m' | '1h' | '1d';
}

export interface MetricsResponse {
  system?: SystemMetrics[];
  smtp?: SMTPMetrics[];
  api?: APIMetrics[];
}

export type GetMetricsResponse = ApiResponse<MetricsResponse>;

export interface GetAlertsRequest extends PaginationParams {
  severity?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

export type GetAlertsResponse = PaginatedResponse<Alert>;

export interface AcknowledgeAlertRequest {
  alertId: UUID;
  comment?: string;
}

// Dashboard endpoints
export interface CreateDashboardRequest {
  name: string;
  description?: string;
  layout: {
    columns: number;
    rows: number;
    gap: number;
  };
  isPublic: boolean;
  allowedUsers?: UUID[];
  allowedRoles?: string[];
  autoRefresh: boolean;
  refreshInterval: number;
}

export interface UpdateDashboardRequest {
  name?: string;
  description?: string;
  layout?: {
    columns: number;
    rows: number;
    gap: number;
  };
  isPublic?: boolean;
  allowedUsers?: UUID[];
  allowedRoles?: string[];
  autoRefresh?: boolean;
  refreshInterval?: number;
}

export interface GetDashboardsRequest extends PaginationParams {
  search?: string;
  isPublic?: boolean;
}

export type GetDashboardsResponse = PaginatedResponse<Dashboard>;

// Health check endpoints
export interface HealthCheckResponse {
  status: 'healthy' | 'unhealthy' | 'degraded';
  timestamp: Timestamp;
  uptime: number;
  version: string;
  services: {
    database: {
      status: 'healthy' | 'unhealthy' | 'degraded';
      responseTime: number;
      lastCheck: Timestamp;
    };
    smtp: {
      status: 'healthy' | 'unhealthy' | 'degraded';
      responseTime: number;
      lastCheck: Timestamp;
      activeConnections: number;
    };
    queue: {
      status: 'healthy' | 'unhealthy' | 'degraded';
      responseTime: number;
      lastCheck: Timestamp;
      queueDepth: number;
    };
  };
}

// Validation schemas for API requests
export const LoginRequestSchema = z.object({
  email: z.string().email(),
  password: z.string().min(1),
  mfaCode: z.string().length(6).optional(),
  rememberMe: z.boolean().default(false),
});

export const CreateUserRequestSchema = z.object({
  email: z.string().email(),
  firstName: z.string().min(1).max(50),
  lastName: z.string().min(1).max(50),
  password: z.string().min(8).max(128),
  role: z.string(),
  timezone: z.string().default('UTC'),
  language: z.string().default('en'),
});

export const UpdateSMTPConfigRequestSchema = z.object({
  hostname: z.string().min(1).optional(),
  port: z.number().int().min(1).max(65535).optional(),
  maxConnections: z.number().int().min(1).optional(),
  maxConnectionsPerIP: z.number().int().min(1).optional(),
  connectionTimeout: z.number().int().min(1000).optional(),
  tlsEnabled: z.boolean().optional(),
  tlsRequired: z.boolean().optional(),
  authRequired: z.boolean().optional(),
  authMethods: z.array(z.string()).optional(),
  ipWhitelist: z.array(z.string().ip()).optional(),
  ipBlacklist: z.array(z.string().ip()).optional(),
  rateLimitEnabled: z.boolean().optional(),
  rateLimitPerIP: z.number().int().min(1).optional(),
  rateLimitPerUser: z.number().int().min(1).optional(),
  rateLimitWindow: z.number().int().min(1).optional(),
  maxMessageSize: z.number().int().min(1024).optional(),
  maxRecipients: z.number().int().min(1).optional(),
});

export const CreateDomainRouteRequestSchema = z.object({
  name: z.string().min(1).max(100),
  description: z.string().max(500).optional(),
  domain: z.string().regex(/^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/),
  destinationHost: z.string().min(1),
  destinationPort: z.number().int().min(1).max(65535),
  destinationTLS: z.boolean(),
  priority: z.number().int().min(0),
  maxRetries: z.number().int().min(0),
  retryDelay: z.number().int().min(1),
  enabled: z.boolean(),
});
