// IronRelay Security & Protection Layer
// This module provides enterprise-grade security features including:
// - Code obfuscation and protection
// - License validation and enforcement
// - Runtime integrity checks
// - Anti-tampering mechanisms
// - Secure configuration management

export { SecurityManager } from './core/SecurityManager';
export { LicenseValidator } from './license/LicenseValidator';
export { CodeProtection } from './protection/CodeProtection';
export { IntegrityChecker } from './integrity/IntegrityChecker';
export { SecureConfig } from './config/SecureConfig';
export { AntiTamper } from './protection/AntiTamper';
export { ObfuscationManager } from './obfuscation/ObfuscationManager';

// Security utilities
export { CryptoUtils } from './utils/CryptoUtils';
export { SecurityLogger } from './utils/SecurityLogger';
export { EnvironmentValidator } from './utils/EnvironmentValidator';

// Types
export * from './types/security.types';
export * from './types/license.types';
export * from './types/protection.types';
