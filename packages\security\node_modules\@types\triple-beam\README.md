# Installation
> `npm install --save @types/triple-beam`

# Summary
This package contains type definitions for triple-beam (https://github.com/winstonjs/triple-beam).

# Details
Files were exported from https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/triple-beam.
## [index.d.ts](https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/triple-beam/index.d.ts)
````ts
export as namespace TripleBeam;

export const LEVEL: unique symbol;
export const MESSAGE: unique symbol;
export const SPLAT: unique symbol;
export const configs: Configs;

export interface Config {
    readonly levels: { [k: string]: number };
    readonly colors: { [k: string]: string };
}

export interface Configs {
    readonly cli: Config;
    readonly npm: Config;
    readonly syslog: Config;
}

````

### Additional Details
 * Last updated: Tue, 07 Nov 2023 15:11:36 GMT
 * Dependencies: none

# Credits
These definitions were written by [<PERSON>](https://github.com/danwbyrne).
