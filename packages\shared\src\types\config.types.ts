import { z } from 'zod';
import { UUID, Timestamp, BaseConfig } from './common.types';

// TLS Certificate configuration
export interface TLSCertificate extends BaseConfig {
  // Certificate details
  commonName: string;
  subjectAlternativeNames: string[];
  issuer: string;
  serialNumber: string;
  
  // Validity
  validFrom: Timestamp;
  validTo: Timestamp;
  
  // Certificate data
  certificatePath: string;
  privateKeyPath: string;
  chainPath?: string;
  
  // Let's Encrypt integration
  isLetsEncrypt: boolean;
  autoRenew: boolean;
  renewalDays: number; // days before expiry to renew
  
  // Usage
  domains: string[];
  isDefault: boolean;
  
  // Status
  status: CertificateStatus;
  lastValidation?: Timestamp;
  validationError?: string;
}

export enum CertificateStatus {
  VALID = 'VALID',
  EXPIRED = 'EXPIRED',
  EXPIRING_SOON = 'EXPIRING_SOON',
  INVALID = 'INVALID',
  REVOKED = 'REVOKED',
  PENDING_VALIDATION = 'PENDING_VALIDATION',
}

// System configuration
export interface SystemConfig extends BaseConfig {
  // General settings
  instanceName: string;
  instanceDescription?: string;
  timezone: string;
  
  // Logging configuration
  logLevel: 'error' | 'warn' | 'info' | 'debug';
  logRetentionDays: number;
  logMaxSize: number; // MB
  
  // Security settings
  sessionTimeout: number; // minutes
  passwordPolicy: PasswordPolicyConfig;
  mfaRequired: boolean;
  
  // Rate limiting
  apiRateLimit: number; // requests per minute
  loginRateLimit: number; // attempts per minute
  
  // Backup settings
  backupEnabled: boolean;
  backupSchedule: string; // cron expression
  backupRetentionDays: number;
  
  // Monitoring settings
  metricsEnabled: boolean;
  metricsRetentionDays: number;
  alertingEnabled: boolean;
  
  // License settings
  licenseKey?: string;
  licenseValidUntil?: Timestamp;
  maxDomains?: number;
  maxThroughput?: number; // emails per hour
}

export interface PasswordPolicyConfig {
  minLength: number;
  maxLength: number;
  requireUppercase: boolean;
  requireLowercase: boolean;
  requireNumbers: boolean;
  requireSpecialChars: boolean;
  preventReuse: number;
  maxAge: number; // days
  lockoutThreshold: number;
  lockoutDuration: number; // minutes
}

// Email template configuration
export interface EmailTemplate extends BaseConfig {
  templateType: EmailTemplateType;
  subject: string;
  htmlBody: string;
  textBody: string;
  
  // Template variables
  variables: TemplateVariable[];
  
  // Localization
  language: string;
  
  // Usage tracking
  lastUsed?: Timestamp;
  usageCount: number;
}

export enum EmailTemplateType {
  USER_INVITATION = 'USER_INVITATION',
  PASSWORD_RESET = 'PASSWORD_RESET',
  EMAIL_VERIFICATION = 'EMAIL_VERIFICATION',
  SECURITY_ALERT = 'SECURITY_ALERT',
  SYSTEM_NOTIFICATION = 'SYSTEM_NOTIFICATION',
  CERTIFICATE_EXPIRY = 'CERTIFICATE_EXPIRY',
  BACKUP_REPORT = 'BACKUP_REPORT',
  DAILY_DIGEST = 'DAILY_DIGEST',
}

export interface TemplateVariable {
  name: string;
  description: string;
  required: boolean;
  defaultValue?: string;
  type: 'string' | 'number' | 'date' | 'boolean';
}

// Notification configuration
export interface NotificationConfig extends BaseConfig {
  // Email settings
  smtpHost: string;
  smtpPort: number;
  smtpSecure: boolean;
  smtpUsername: string;
  smtpPassword: string;
  fromEmail: string;
  fromName: string;
  
  // Notification rules
  rules: NotificationRule[];
  
  // Rate limiting
  maxEmailsPerHour: number;
  
  // Testing
  testMode: boolean;
  testEmail?: string;
}

export interface NotificationRule {
  id: UUID;
  name: string;
  description?: string;
  enabled: boolean;
  
  // Trigger conditions
  eventType: NotificationEventType;
  conditions: NotificationCondition[];
  
  // Recipients
  recipients: NotificationRecipient[];
  
  // Template
  templateId: UUID;
  
  // Throttling
  throttleMinutes: number;
  
  // Priority
  priority: NotificationPriority;
}

export enum NotificationEventType {
  SYSTEM_ERROR = 'SYSTEM_ERROR',
  SMTP_ERROR = 'SMTP_ERROR',
  HIGH_ERROR_RATE = 'HIGH_ERROR_RATE',
  QUEUE_FULL = 'QUEUE_FULL',
  CERTIFICATE_EXPIRY = 'CERTIFICATE_EXPIRY',
  LICENSE_EXPIRY = 'LICENSE_EXPIRY',
  SECURITY_BREACH = 'SECURITY_BREACH',
  USER_LOGIN_FAILURE = 'USER_LOGIN_FAILURE',
  BACKUP_FAILURE = 'BACKUP_FAILURE',
  DISK_SPACE_LOW = 'DISK_SPACE_LOW',
  MEMORY_HIGH = 'MEMORY_HIGH',
  CPU_HIGH = 'CPU_HIGH',
}

export interface NotificationCondition {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'contains' | 'regex';
  value: string | number | boolean;
}

export interface NotificationRecipient {
  type: 'user' | 'email' | 'role';
  value: string; // user ID, email address, or role name
}

export enum NotificationPriority {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

// Backup configuration
export interface BackupConfig extends BaseConfig {
  // Schedule
  schedule: string; // cron expression
  timezone: string;
  
  // What to backup
  includeDatabase: boolean;
  includeConfigs: boolean;
  includeLogs: boolean;
  includeCertificates: boolean;
  
  // Storage
  storageType: BackupStorageType;
  storageConfig: BackupStorageConfig;
  
  // Retention
  retentionDays: number;
  maxBackups: number;
  
  // Compression
  compressionEnabled: boolean;
  compressionLevel: number; // 1-9
  
  // Encryption
  encryptionEnabled: boolean;
  encryptionKey?: string;
  
  // Notifications
  notifyOnSuccess: boolean;
  notifyOnFailure: boolean;
  notificationRecipients: string[];
}

export enum BackupStorageType {
  LOCAL = 'LOCAL',
  S3 = 'S3',
  AZURE_BLOB = 'AZURE_BLOB',
  GOOGLE_CLOUD = 'GOOGLE_CLOUD',
  FTP = 'FTP',
  SFTP = 'SFTP',
}

export interface BackupStorageConfig {
  // Local storage
  localPath?: string;
  
  // S3 storage
  s3Bucket?: string;
  s3Region?: string;
  s3AccessKey?: string;
  s3SecretKey?: string;
  s3Prefix?: string;
  
  // Azure Blob storage
  azureConnectionString?: string;
  azureContainer?: string;
  azurePrefix?: string;
  
  // Google Cloud storage
  gcpProjectId?: string;
  gcpKeyFile?: string;
  gcpBucket?: string;
  gcpPrefix?: string;
  
  // FTP/SFTP
  ftpHost?: string;
  ftpPort?: number;
  ftpUsername?: string;
  ftpPassword?: string;
  ftpPath?: string;
  ftpSecure?: boolean;
}

// Validation schemas
export const TLSCertificateSchema = z.object({
  commonName: z.string().min(1),
  domains: z.array(z.string().min(1)),
  isDefault: z.boolean(),
  autoRenew: z.boolean(),
  renewalDays: z.number().int().min(1).max(90).default(30),
});

export const SystemConfigSchema = z.object({
  instanceName: z.string().min(1).max(100),
  instanceDescription: z.string().max(500).optional(),
  timezone: z.string(),
  logLevel: z.enum(['error', 'warn', 'info', 'debug']),
  logRetentionDays: z.number().int().min(1).max(365),
  sessionTimeout: z.number().int().min(5).max(1440),
  apiRateLimit: z.number().int().min(1),
  loginRateLimit: z.number().int().min(1),
});

export const NotificationConfigSchema = z.object({
  smtpHost: z.string().min(1),
  smtpPort: z.number().int().min(1).max(65535),
  smtpSecure: z.boolean(),
  smtpUsername: z.string().min(1),
  smtpPassword: z.string().min(1),
  fromEmail: z.string().email(),
  fromName: z.string().min(1),
  maxEmailsPerHour: z.number().int().min(1),
  testMode: z.boolean(),
  testEmail: z.string().email().optional(),
});
