# IronRelay - Commercial-Grade SMTP Relay Server

IronRelay is a production-ready SMTP relay server designed for enterprise environments that need reliable email routing between internal mail servers (like Microsoft Exchange) and external email providers.

## 🚀 Features

### Core SMTP Engine
- **Haraka-based SMTP server** with custom plugins
- **IP/CIDR whitelisting and blacklisting** with configurable rules
- **Domain-based routing** with per-domain destination mapping and failover
- **SMTP authentication control** (PLAIN, LOGIN, CRAM-MD5)
- **TLS encryption** for both incoming and outgoing sessions
- **Email security features**: SPF validation, DKIM signing/verification, DMARC enforcement
- **Rate limiting** per IP/domain/user to prevent abuse
- **Queue management** with retry logic and dead letter handling

### Web Management Dashboard
- **Modern React TypeScript interface** with responsive design
- **Role-based access control (RBAC)** with multiple admin levels
- **Real-time monitoring** of email throughput, queue status, and error rates
- **Comprehensive log management** with search, filtering, and export
- **TLS certificate management** with Let's Encrypt integration
- **Multi-instance management** for centralized control

### Security & Protection
- **Node.js Single Executable Applications (SEA)** packaging
- **Hardware fingerprinting** for license binding
- **Code obfuscation** and runtime integrity checks
- **mTLS communication** between services
- **Comprehensive audit logging**

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   Backend API   │    │  SMTP Engine    │
│   (React TS)    │◄──►│   (NestJS)      │◄──►│   (Haraka)      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
                       ┌─────────────────┐
                       │   PostgreSQL    │
                       │   Database      │
                       └─────────────────┘
```

## 📦 Project Structure

```
ironrelay/
├── packages/
│   ├── api/              # NestJS Backend API
│   ├── frontend/         # React TypeScript Frontend
│   ├── smtp-engine/      # Haraka SMTP Server
│   ├── shared/           # Shared types and utilities
│   └── security/         # Security and licensing modules
├── docker/               # Docker configurations
├── k8s/                  # Kubernetes manifests
├── docs/                 # Documentation
└── scripts/              # Build and deployment scripts
```

## 🚀 Quick Start

### Prerequisites
- Node.js 18+ and npm 9+
- Docker and Docker Compose
- PostgreSQL 14+

### Development Setup

1. **Clone and install dependencies:**
```bash
git clone https://github.com/ironrelay/ironrelay.git
cd ironrelay
npm install
```

2. **Start development environment:**
```bash
# Start all services in development mode
npm run dev

# Or start individual services
npm run dev:api      # Backend API on port 3000
npm run dev:frontend # Frontend on port 3001
npm run dev:smtp     # SMTP server on port 2525
```

3. **Database setup:**
```bash
# Run database migrations
npm run migrate

# Seed initial data
npm run seed
```

### Production Deployment

1. **Using Docker Compose:**
```bash
# Build and start all services
npm run docker:build
npm run docker:up
```

2. **Using Kubernetes:**
```bash
# Deploy to Kubernetes cluster
kubectl apply -f k8s/
```

## 🔧 Configuration

### Environment Variables

| Variable | Description | Default |
|----------|-------------|---------|
| `DATABASE_URL` | PostgreSQL connection string | `postgresql://localhost:5432/ironrelay` |
| `SMTP_PORT` | SMTP server listening port | `2525` |
| `API_PORT` | Backend API port | `3000` |
| `FRONTEND_PORT` | Frontend development port | `3001` |
| `JWT_SECRET` | JWT token secret | `your-secret-key` |
| `TLS_CERT_PATH` | Path to TLS certificate | `/etc/ssl/certs/` |

### SMTP Configuration

Configure SMTP settings through the web dashboard or API:

- **Listening ports and interfaces**
- **IP access control lists**
- **Domain routing rules**
- **Authentication methods**
- **TLS certificate management**
- **Rate limiting policies**

## 📊 Monitoring

### Metrics and Observability
- **Prometheus metrics** for email throughput, latency, and errors
- **Grafana dashboards** for visualization
- **Structured logging** with configurable levels
- **Health checks** for all services
- **Alerting** for critical system events

### Key Metrics
- Email throughput (emails/second)
- Queue depth and processing time
- Error rates by type and destination
- System resource utilization
- Authentication success/failure rates

## 🔒 Security

### Authentication & Authorization
- **JWT-based API authentication**
- **Role-based access control (RBAC)**
- **Multi-factor authentication support**
- **Session management with secure cookies**

### Network Security
- **TLS encryption** for all communications
- **mTLS** for inter-service communication
- **IP whitelisting/blacklisting**
- **Rate limiting** and DDoS protection

### Code Protection
- **Single Executable Application (SEA)** packaging
- **Code obfuscation** before packaging
- **Runtime integrity checks**
- **License validation** with hardware fingerprinting

## 📚 Documentation

- [Installation Guide](docs/installation.md)
- [Configuration Reference](docs/configuration.md)
- [API Documentation](docs/api.md)
- [Security Guide](docs/security.md)
- [Troubleshooting](docs/troubleshooting.md)

## 🧪 Testing

```bash
# Run all tests
npm test

# Run specific test suites
npm run test:api
npm run test:smtp

# Run with coverage
npm run test:coverage
```

## 📄 License

This is commercial software. See [LICENSE](LICENSE) for details.

## 🤝 Support

For enterprise support and licensing inquiries, contact: <EMAIL>

---

**IronRelay** - Reliable SMTP relay for enterprise environments.
