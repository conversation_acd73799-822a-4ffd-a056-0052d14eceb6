version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: ironrelay-postgres-prod
    restart: unless-stopped
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-ironrelay}
      POSTGRES_USER: ${POSTGRES_USER:-ironrelay}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/postgres/init:/docker-entrypoint-initdb.d
    ports:
      - "127.0.0.1:5432:5432"
    networks:
      - ironrelay-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-ironrelay} -d ${POSTGRES_DB:-ironrelay}"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: ironrelay-redis-prod
    restart: unless-stopped
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./docker/redis/redis.conf:/usr/local/etc/redis/redis.conf
    ports:
      - "127.0.0.1:6379:6379"
    networks:
      - ironrelay-network
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # API Server
  api:
    build:
      context: .
      dockerfile: Dockerfile.api
      target: production
    container_name: ironrelay-api-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER:-ironrelay}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-ironrelay}
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      JWT_SECRET: ${JWT_SECRET}
      API_KEY_SECRET: ${API_KEY_SECRET}
      PORT: 3000
      LOG_LEVEL: ${LOG_LEVEL:-info}
      ENABLE_METRICS: true
      ENABLE_HEALTH_CHECKS: true
    ports:
      - "127.0.0.1:3000:3000"
    volumes:
      - api_logs:/app/logs
      - ./ssl:/app/ssl:ro
    networks:
      - ironrelay-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      replicas: 2
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # SMTP Engine
  smtp:
    build:
      context: .
      dockerfile: Dockerfile.smtp
      target: production
    container_name: ironrelay-smtp-prod
    restart: unless-stopped
    environment:
      NODE_ENV: production
      DATABASE_URL: postgresql://${POSTGRES_USER:-ironrelay}:${POSTGRES_PASSWORD}@postgres:5432/${POSTGRES_DB:-ironrelay}
      REDIS_URL: redis://:${REDIS_PASSWORD}@redis:6379
      SMTP_PORT: 25
      SMTP_HOST: 0.0.0.0
      SMTP_MAX_CONNECTIONS: ${SMTP_MAX_CONNECTIONS:-1000}
      SMTP_MAX_MESSAGE_SIZE: ${SMTP_MAX_MESSAGE_SIZE:-26214400}
      LOG_LEVEL: ${LOG_LEVEL:-info}
    ports:
      - "25:25"
      - "587:587"
      - "465:465"
    volumes:
      - smtp_logs:/app/logs
      - smtp_queue:/app/queue
      - ./ssl:/app/ssl:ro
    networks:
      - ironrelay-network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "nc", "-z", "localhost", "25"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
      restart_policy:
        condition: on-failure
        delay: 5s
        max_attempts: 3
        window: 120s

  # Frontend (Nginx)
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
      target: production
    container_name: ironrelay-frontend-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./ssl:/etc/nginx/ssl:ro
      - ./docker/nginx/nginx.prod.conf:/etc/nginx/nginx.conf:ro
      - frontend_logs:/var/log/nginx
    networks:
      - ironrelay-network
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 30s
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # Prometheus Monitoring
  prometheus:
    image: prom/prometheus:latest
    container_name: ironrelay-prometheus-prod
    restart: unless-stopped
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=30d'
      - '--web.enable-lifecycle'
      - '--web.enable-admin-api'
    volumes:
      - ./docker/prometheus/prometheus.prod.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    ports:
      - "127.0.0.1:9090:9090"
    networks:
      - ironrelay-network
    depends_on:
      - api
      - smtp
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'

  # Grafana Dashboards
  grafana:
    image: grafana/grafana:latest
    container_name: ironrelay-grafana-prod
    restart: unless-stopped
    environment:
      GF_SECURITY_ADMIN_PASSWORD: ${GRAFANA_ADMIN_PASSWORD}
      GF_USERS_ALLOW_SIGN_UP: false
      GF_INSTALL_PLUGINS: grafana-clock-panel,grafana-simple-json-datasource
      GF_SERVER_ROOT_URL: https://${DOMAIN}/grafana/
      GF_SERVER_SERVE_FROM_SUB_PATH: true
    volumes:
      - grafana_data:/var/lib/grafana
      - ./docker/grafana/provisioning:/etc/grafana/provisioning:ro
      - ./docker/grafana/dashboards:/var/lib/grafana/dashboards:ro
    ports:
      - "127.0.0.1:3002:3000"
    networks:
      - ironrelay-network
    depends_on:
      - prometheus
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # Log Aggregation
  loki:
    image: grafana/loki:latest
    container_name: ironrelay-loki-prod
    restart: unless-stopped
    command: -config.file=/etc/loki/local-config.yaml
    volumes:
      - ./docker/loki/loki.yml:/etc/loki/local-config.yaml:ro
      - loki_data:/loki
    ports:
      - "127.0.0.1:3100:3100"
    networks:
      - ironrelay-network
    deploy:
      resources:
        limits:
          memory: 256M
          cpus: '0.5'
        reservations:
          memory: 128M
          cpus: '0.25'

  # Log Shipper
  promtail:
    image: grafana/promtail:latest
    container_name: ironrelay-promtail-prod
    restart: unless-stopped
    command: -config.file=/etc/promtail/config.yml
    volumes:
      - ./docker/promtail/promtail.yml:/etc/promtail/config.yml:ro
      - api_logs:/var/log/api:ro
      - smtp_logs:/var/log/smtp:ro
      - frontend_logs:/var/log/nginx:ro
      - /var/log:/var/log/host:ro
    networks:
      - ironrelay-network
    depends_on:
      - loki
    deploy:
      resources:
        limits:
          memory: 128M
          cpus: '0.25'
        reservations:
          memory: 64M
          cpus: '0.1'

networks:
  ironrelay-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  api_logs:
    driver: local
  smtp_logs:
    driver: local
  smtp_queue:
    driver: local
  frontend_logs:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
  loki_data:
    driver: local
