import { Injectable, LoggerService as NestLoggerService } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import * as winston from 'winston';
import * as DailyRotateFile from 'winston-daily-rotate-file';
import * as path from 'path';
import * as fs from 'fs';

@Injectable()
export class Logger implements NestLoggerService {
  private logger: winston.Logger;
  private context?: string;

  constructor(context?: string, private configService?: ConfigService) {
    this.context = context;
    this.logger = this.createLogger();
  }

  private createLogger(): winston.Logger {
    const logLevel = this.configService?.get<string>('logging.level') || process.env.LOG_LEVEL || 'info';
    const logDir = this.configService?.get<string>('logging.dir') || process.env.LOG_DIR || './logs';

    // Ensure log directory exists
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    // Custom format for logs
    const logFormat = winston.format.combine(
      winston.format.timestamp({
        format: 'YYYY-MM-DD HH:mm:ss.SSS',
      }),
      winston.format.errors({ stack: true }),
      winston.format.printf(({ timestamp, level, message, context, stack, ...meta }) => {
        const contextStr = context ? `[${context}] ` : '';
        let log = `${timestamp} [${level.toUpperCase()}] ${contextStr}${message}`;
        
        if (Object.keys(meta).length > 0) {
          log += ` ${JSON.stringify(meta)}`;
        }
        
        if (stack) {
          log += `\n${stack}`;
        }
        
        return log;
      })
    );

    // Console format for development
    const consoleFormat = winston.format.combine(
      winston.format.colorize(),
      winston.format.timestamp({
        format: 'HH:mm:ss',
      }),
      winston.format.printf(({ timestamp, level, message, context, ...meta }) => {
        const contextStr = context ? `[${context}] ` : '';
        let log = `${timestamp} ${level} ${contextStr}${message}`;
        
        if (Object.keys(meta).length > 0) {
          log += ` ${JSON.stringify(meta, null, 2)}`;
        }
        
        return log;
      })
    );

    const transports: winston.transport[] = [
      // Console transport
      new winston.transports.Console({
        level: logLevel,
        format: consoleFormat,
      }),

      // Error log file
      new DailyRotateFile({
        filename: path.join(logDir, 'error-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        level: 'error',
        format: logFormat,
        maxSize: '20m',
        maxFiles: '14d',
        zippedArchive: true,
      }),

      // Combined log file
      new DailyRotateFile({
        filename: path.join(logDir, 'combined-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        format: logFormat,
        maxSize: '20m',
        maxFiles: '30d',
        zippedArchive: true,
      }),

      // API specific log file
      new DailyRotateFile({
        filename: path.join(logDir, 'api-%DATE%.log'),
        datePattern: 'YYYY-MM-DD',
        format: logFormat,
        maxSize: '50m',
        maxFiles: '30d',
        zippedArchive: true,
        level: 'info',
      }),
    ];

    return winston.createLogger({
      level: logLevel,
      format: logFormat,
      transports,
      exitOnError: false,
    });
  }

  log(message: string, context?: string): void {
    this.info(message, context);
  }

  info(message: string, meta?: unknown): void {
    this.logger.info(message, { context: this.context, ...this.formatMeta(meta) });
  }

  error(message: string, trace?: string | Error, context?: string): void {
    const errorContext = context || this.context;
    
    if (trace instanceof Error) {
      this.logger.error(message, {
        context: errorContext,
        error: trace.message,
        stack: trace.stack,
      });
    } else if (typeof trace === 'string') {
      this.logger.error(message, {
        context: errorContext,
        trace,
      });
    } else {
      this.logger.error(message, {
        context: errorContext,
        ...this.formatMeta(trace),
      });
    }
  }

  warn(message: string, meta?: unknown): void {
    this.logger.warn(message, { context: this.context, ...this.formatMeta(meta) });
  }

  debug(message: string, meta?: unknown): void {
    this.logger.debug(message, { context: this.context, ...this.formatMeta(meta) });
  }

  verbose(message: string, meta?: unknown): void {
    this.logger.verbose(message, { context: this.context, ...this.formatMeta(meta) });
  }

  // API specific logging methods
  apiRequest(method: string, url: string, statusCode: number, duration: number, meta?: unknown): void {
    this.logger.info(`${method} ${url} ${statusCode} - ${duration}ms`, {
      context: 'API',
      method,
      url,
      statusCode,
      duration,
      ...this.formatMeta(meta),
    });
  }

  apiError(method: string, url: string, statusCode: number, error: string, meta?: unknown): void {
    this.logger.error(`${method} ${url} ${statusCode} - ${error}`, {
      context: 'API',
      method,
      url,
      statusCode,
      error,
      ...this.formatMeta(meta),
    });
  }

  // Authentication logging
  authSuccess(userId: string, email: string, ip: string, userAgent?: string): void {
    this.logger.info('Authentication successful', {
      context: 'AUTH',
      userId,
      email,
      ip,
      userAgent,
    });
  }

  authFailure(email: string, reason: string, ip: string, userAgent?: string): void {
    this.logger.warn('Authentication failed', {
      context: 'AUTH',
      email,
      reason,
      ip,
      userAgent,
    });
  }

  // Security logging
  securityEvent(event: string, severity: string, ip: string, meta?: unknown): void {
    this.logger.warn(`Security event: ${event}`, {
      context: 'SECURITY',
      event,
      severity,
      ip,
      ...this.formatMeta(meta),
    });
  }

  // Database logging
  dbQuery(query: string, duration: number, meta?: unknown): void {
    this.logger.debug(`Database query executed in ${duration}ms`, {
      context: 'DATABASE',
      query,
      duration,
      ...this.formatMeta(meta),
    });
  }

  dbError(operation: string, error: string, meta?: unknown): void {
    this.logger.error(`Database operation failed: ${operation}`, {
      context: 'DATABASE',
      operation,
      error,
      ...this.formatMeta(meta),
    });
  }

  // Performance logging
  performance(operation: string, duration: number, meta?: unknown): void {
    this.logger.info(`Performance: ${operation} completed in ${duration}ms`, {
      context: 'PERFORMANCE',
      operation,
      duration,
      ...this.formatMeta(meta),
    });
  }

  // Audit logging
  audit(action: string, resource: string, userId: string, changes?: unknown): void {
    this.logger.info(`Audit: ${action} on ${resource}`, {
      context: 'AUDIT',
      action,
      resource,
      userId,
      changes,
    });
  }

  private formatMeta(meta: unknown): Record<string, unknown> {
    if (!meta) return {};
    if (typeof meta === 'object') return meta as Record<string, unknown>;
    return { meta };
  }

  // Get the underlying winston logger for advanced usage
  getWinstonLogger(): winston.Logger {
    return this.logger;
  }

  // Update log level dynamically
  setLogLevel(level: string): void {
    this.logger.level = level;
    this.logger.transports.forEach(transport => {
      transport.level = level;
    });
    this.info(`Log level changed to: ${level}`);
  }

  // Create child logger with additional context
  child(context: string): Logger {
    const childLogger = new Logger(context, this.configService);
    return childLogger;
  }

  // Flush logs (useful for graceful shutdown)
  async flush(): Promise<void> {
    return new Promise((resolve) => {
      this.logger.on('finish', resolve);
      this.logger.end();
    });
  }
}
