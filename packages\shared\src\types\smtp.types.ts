import { z } from 'zod';
import { UUI<PERSON>, Timestamp, EmailAddress, IPAddress, Domain, BaseConfig } from './common.types';

// SMTP Server Configuration
export interface SMTPConfig extends BaseConfig {
  // Server settings
  hostname: string;
  port: number;
  maxConnections: number;
  maxConnectionsPerIP: number;
  connectionTimeout: number;
  
  // TLS settings
  tlsEnabled: boolean;
  tlsRequired: boolean;
  tlsCertPath?: string;
  tlsKeyPath?: string;
  tlsCiphers?: string[];
  
  // Authentication settings
  authRequired: boolean;
  authMethods: SMTPAuthMethod[];
  
  // Security settings
  ipWhitelist: IPAddress[];
  ipBlacklist: IPAddress[];
  cidrWhitelist: string[];
  cidrBlacklist: string[];
  
  // Rate limiting
  rateLimitEnabled: boolean;
  rateLimitPerIP: number;
  rateLimitPerUser: number;
  rateLimitWindow: number; // seconds
  
  // Message limits
  maxMessageSize: number; // bytes
  maxRecipients: number;
  
  // Queue settings
  queueEnabled: boolean;
  queueRetryAttempts: number;
  queueRetryDelay: number; // seconds
  queueMaxAge: number; // seconds
}

export enum SMTPAuthMethod {
  PLAIN = 'PLAIN',
  LOGIN = 'LOGIN',
  CRAM_MD5 = 'CRAM-MD5',
}

// Domain routing configuration
export interface DomainRoute extends BaseConfig {
  domain: Domain;
  destinationHost: string;
  destinationPort: number;
  destinationTLS: boolean;
  destinationAuth?: {
    username: string;
    password: string;
  };
  priority: number;
  fallbackRoutes: DomainRoute[];
  maxRetries: number;
  retryDelay: number;
}

// SMTP User for authentication
export interface SMTPUser extends BaseConfig {
  username: string;
  email: EmailAddress;
  passwordHash: string;
  isActive: boolean;
  lastLogin?: Timestamp;
  loginCount: number;
  
  // Permissions
  allowedDomains: Domain[];
  allowedIPs: IPAddress[];
  
  // Rate limits (overrides global)
  customRateLimit?: number;
  customRateLimitWindow?: number;
}

// Email transaction log
export interface EmailTransaction {
  id: UUID;
  messageId: string;
  
  // Sender information
  senderIP: IPAddress;
  senderHostname?: string;
  mailFrom: EmailAddress;
  
  // Recipient information
  rcptTo: EmailAddress[];
  
  // Authentication
  authenticatedUser?: UUID;
  authMethod?: SMTPAuthMethod;
  
  // Message details
  subject?: string;
  messageSize: number;
  
  // Processing details
  receivedAt: Timestamp;
  processedAt?: Timestamp;
  deliveredAt?: Timestamp;
  
  // Status
  status: EmailStatus;
  statusMessage?: string;
  
  // Routing
  routeUsed?: UUID;
  destinationHost?: string;
  destinationPort?: number;
  
  // Security checks
  spfResult?: SPFResult;
  dkimResult?: DKIMResult;
  dmarcResult?: DMARCResult;
  
  // Performance metrics
  processingTime: number; // milliseconds
  queueTime?: number; // milliseconds
  deliveryTime?: number; // milliseconds
  
  // Error information
  errorCode?: string;
  errorMessage?: string;
  retryCount: number;
  
  // Metadata
  headers?: Record<string, string>;
  metadata?: Record<string, unknown>;
}

export enum EmailStatus {
  RECEIVED = 'RECEIVED',
  QUEUED = 'QUEUED',
  PROCESSING = 'PROCESSING',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  REJECTED = 'REJECTED',
  DEFERRED = 'DEFERRED',
  BOUNCED = 'BOUNCED',
}

// Email security check results
export enum SPFResult {
  PASS = 'PASS',
  FAIL = 'FAIL',
  SOFTFAIL = 'SOFTFAIL',
  NEUTRAL = 'NEUTRAL',
  NONE = 'NONE',
  TEMPERROR = 'TEMPERROR',
  PERMERROR = 'PERMERROR',
}

export enum DKIMResult {
  PASS = 'PASS',
  FAIL = 'FAIL',
  NEUTRAL = 'NEUTRAL',
  NONE = 'NONE',
  TEMPERROR = 'TEMPERROR',
  PERMERROR = 'PERMERROR',
}

export enum DMARCResult {
  PASS = 'PASS',
  FAIL = 'FAIL',
  NONE = 'NONE',
  TEMPERROR = 'TEMPERROR',
  PERMERROR = 'PERMERROR',
}

// SMTP Queue management
export interface QueuedEmail {
  id: UUID;
  transactionId: UUID;
  
  // Message data
  messageData: Buffer;
  
  // Delivery details
  attempts: number;
  maxAttempts: number;
  nextAttempt: Timestamp;
  
  // Status
  status: QueueStatus;
  lastError?: string;
  
  // Timestamps
  queuedAt: Timestamp;
  lastAttemptAt?: Timestamp;
  
  // Priority
  priority: number;
}

export enum QueueStatus {
  PENDING = 'PENDING',
  PROCESSING = 'PROCESSING',
  DELIVERED = 'DELIVERED',
  FAILED = 'FAILED',
  DEAD_LETTER = 'DEAD_LETTER',
}

// SMTP Statistics
export interface SMTPStats {
  timestamp: Timestamp;
  
  // Connection stats
  totalConnections: number;
  activeConnections: number;
  rejectedConnections: number;
  
  // Message stats
  messagesReceived: number;
  messagesDelivered: number;
  messagesFailed: number;
  messagesQueued: number;
  
  // Performance stats
  averageProcessingTime: number;
  averageDeliveryTime: number;
  
  // Security stats
  authenticationAttempts: number;
  authenticationFailures: number;
  spfFailures: number;
  dkimFailures: number;
  dmarcFailures: number;
  
  // Rate limiting stats
  rateLimitHits: number;
  
  // Error stats
  errorsByType: Record<string, number>;
}

// Validation schemas
export const SMTPConfigSchema = z.object({
  hostname: z.string().min(1),
  port: z.number().int().min(1).max(65535),
  maxConnections: z.number().int().min(1),
  maxConnectionsPerIP: z.number().int().min(1),
  connectionTimeout: z.number().int().min(1000),
  tlsEnabled: z.boolean(),
  tlsRequired: z.boolean(),
  authRequired: z.boolean(),
  authMethods: z.array(z.nativeEnum(SMTPAuthMethod)),
  ipWhitelist: z.array(z.string().ip()),
  ipBlacklist: z.array(z.string().ip()),
  rateLimitEnabled: z.boolean(),
  rateLimitPerIP: z.number().int().min(1),
  rateLimitPerUser: z.number().int().min(1),
  rateLimitWindow: z.number().int().min(1),
  maxMessageSize: z.number().int().min(1024),
  maxRecipients: z.number().int().min(1),
});

export const DomainRouteSchema = z.object({
  domain: z.string().regex(/^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/),
  destinationHost: z.string().min(1),
  destinationPort: z.number().int().min(1).max(65535),
  destinationTLS: z.boolean(),
  priority: z.number().int().min(0),
  maxRetries: z.number().int().min(0),
  retryDelay: z.number().int().min(1),
});

export const SMTPUserSchema = z.object({
  username: z.string().min(3).max(50),
  email: z.string().email(),
  password: z.string().min(8),
  isActive: z.boolean(),
  allowedDomains: z.array(z.string()),
  allowedIPs: z.array(z.string().ip()),
});
