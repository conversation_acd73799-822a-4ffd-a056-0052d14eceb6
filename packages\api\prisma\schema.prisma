// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// User management
model User {
  id                        String    @id @default(uuid())
  email                     String    @unique
  firstName                 String
  lastName                  String
  passwordHash              String
  role                      UserRole
  isActive                  Boolean   @default(true)
  emailVerified             <PERSON>olean   @default(false)
  
  // Security
  mfaEnabled                Boolean   @default(false)
  mfaSecret                 String?
  
  // Session management
  lastLogin                 DateTime?
  lastLoginIP               String?
  loginCount                Int       @default(0)
  failedLoginAttempts       Int       @default(0)
  lockedUntil               DateTime?
  
  // Password management
  passwordChangedAt         DateTime  @default(now())
  passwordResetToken        String?
  passwordResetExpires      DateTime?
  
  // Email verification
  emailVerificationToken    String?
  emailVerificationExpires  DateTime?
  
  // Preferences
  timezone                  String    @default("UTC")
  language                  String    @default("en")
  theme                     Theme     @default(LIGHT)
  
  // Notifications
  emailNotifications        Bo<PERSON><PERSON>   @default(true)
  securityAlerts            Boolean   @default(true)
  
  // Timestamps
  createdAt                 DateTime  @default(now())
  updatedAt                 DateTime  @updatedAt
  
  // Relations
  createdConfigs            SMTPConfig[]       @relation("ConfigCreatedBy")
  updatedConfigs            SMTPConfig[]       @relation("ConfigUpdatedBy")
  createdRoutes             DomainRoute[]      @relation("RouteCreatedBy")
  updatedRoutes             DomainRoute[]      @relation("RouteUpdatedBy")
  createdSMTPUsers          SMTPUser[]         @relation("SMTPUserCreatedBy")
  updatedSMTPUsers          SMTPUser[]         @relation("SMTPUserUpdatedBy")
  createdCertificates       TLSCertificate[]   @relation("CertificateCreatedBy")
  updatedCertificates       TLSCertificate[]   @relation("CertificateUpdatedBy")
  createdSystemConfigs      SystemConfig[]     @relation("SystemConfigCreatedBy")
  updatedSystemConfigs      SystemConfig[]     @relation("SystemConfigUpdatedBy")
  apiKeys                   ApiKey[]
  sessions                  UserSession[]
  auditLogs                 AuditLog[]
  createdDashboards         Dashboard[]        @relation("DashboardCreatedBy")
  acknowledgedAlerts        Alert[]            @relation("AlertAcknowledgedBy")
  investigatedSecurityEvents SecurityEvent[]  @relation("SecurityEventInvestigatedBy")
  securityEvents            SecurityEvent[]
  
  @@map("users")
}

enum UserRole {
  SUPER_ADMIN
  ADMIN
  OPERATOR
  VIEWER
}

enum Theme {
  LIGHT
  DARK
  AUTO
}

// API Keys for programmatic access
model ApiKey {
  id                String      @id @default(uuid())
  name              String
  description       String?
  keyId             String      @unique
  keyHash           String
  userId            String
  
  // Permissions (stored as JSON array)
  permissions       Json
  
  // Usage tracking
  lastUsed          DateTime?
  usageCount        Int         @default(0)
  
  // Restrictions
  ipWhitelist       String[]    @default([])
  expiresAt         DateTime?
  
  // Rate limiting
  rateLimitPerHour  Int?
  rateLimitPerDay   Int?
  
  // Status
  enabled           Boolean     @default(true)
  
  // Timestamps
  createdAt         DateTime    @default(now())
  updatedAt         DateTime    @updatedAt
  
  // Relations
  user              User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("api_keys")
}

// User sessions
model UserSession {
  id              String    @id @default(uuid())
  userId          String
  sessionToken    String    @unique
  
  // Session details
  ipAddress       String
  userAgent       String
  
  // Timestamps
  createdAt       DateTime  @default(now())
  lastAccessedAt  DateTime  @default(now())
  expiresAt       DateTime
  
  // Security
  isActive        Boolean   @default(true)
  revokedAt       DateTime?
  revokedBy       String?
  revokedReason   String?
  
  // Relations
  user            User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  
  @@map("user_sessions")
}

// SMTP Configuration
model SMTPConfig {
  id                    String    @id @default(uuid())
  name                  String
  description           String?
  
  // Server settings
  hostname              String
  port                  Int
  maxConnections        Int       @default(100)
  maxConnectionsPerIP   Int       @default(10)
  connectionTimeout     Int       @default(30000)
  
  // TLS settings
  tlsEnabled            Boolean   @default(true)
  tlsRequired           Boolean   @default(false)
  tlsCertPath           String?
  tlsKeyPath            String?
  tlsCiphers            String[]  @default([])
  
  // Authentication settings
  authRequired          Boolean   @default(false)
  authMethods           String[]  @default(["PLAIN", "LOGIN"])
  
  // Security settings
  ipWhitelist           String[]  @default([])
  ipBlacklist           String[]  @default([])
  cidrWhitelist         String[]  @default([])
  cidrBlacklist         String[]  @default([])
  
  // Rate limiting
  rateLimitEnabled      Boolean   @default(true)
  rateLimitPerIP        Int       @default(100)
  rateLimitPerUser      Int       @default(1000)
  rateLimitWindow       Int       @default(3600)
  
  // Message limits
  maxMessageSize        Int       @default(26214400) // 25MB
  maxRecipients         Int       @default(100)
  
  // Queue settings
  queueEnabled          Boolean   @default(true)
  queueRetryAttempts    Int       @default(3)
  queueRetryDelay       Int       @default(300)
  queueMaxAge           Int       @default(86400)
  
  // Status
  enabled               Boolean   @default(true)
  
  // Timestamps
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  createdBy             String
  updatedBy             String
  
  // Relations
  creator               User      @relation("ConfigCreatedBy", fields: [createdBy], references: [id])
  updater               User      @relation("ConfigUpdatedBy", fields: [updatedBy], references: [id])
  
  @@map("smtp_configs")
}

// Domain routing configuration
model DomainRoute {
  id                String    @id @default(uuid())
  name              String
  description       String?
  domain            String
  destinationHost   String
  destinationPort   Int
  destinationTLS    Boolean   @default(true)
  
  // Authentication for destination
  destinationUsername String?
  destinationPassword String?
  
  priority          Int       @default(0)
  maxRetries        Int       @default(3)
  retryDelay        Int       @default(300)
  
  // Status
  enabled           Boolean   @default(true)
  
  // Timestamps
  createdAt         DateTime  @default(now())
  updatedAt         DateTime  @updatedAt
  createdBy         String
  updatedBy         String
  
  // Relations
  creator           User      @relation("RouteCreatedBy", fields: [createdBy], references: [id])
  updater           User      @relation("RouteUpdatedBy", fields: [updatedBy], references: [id])
  fallbackRoutes    DomainRoute[] @relation("RouteFallback")
  parentRoute       DomainRoute?  @relation("RouteFallback", fields: [parentRouteId], references: [id])
  parentRouteId     String?
  
  @@unique([domain])
  @@map("domain_routes")
}

// SMTP Users for authentication
model SMTPUser {
  id                    String    @id @default(uuid())
  name                  String
  description           String?
  username              String    @unique
  email                 String
  passwordHash          String
  isActive              Boolean   @default(true)
  
  // Permissions
  allowedDomains        String[]  @default([])
  allowedIPs            String[]  @default([])
  
  // Rate limits (overrides global)
  customRateLimit       Int?
  customRateLimitWindow Int?
  
  // Usage tracking
  lastLogin             DateTime?
  loginCount            Int       @default(0)
  
  // Status
  enabled               Boolean   @default(true)
  
  // Timestamps
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  createdBy             String
  updatedBy             String
  
  // Relations
  creator               User      @relation("SMTPUserCreatedBy", fields: [createdBy], references: [id])
  updater               User      @relation("SMTPUserUpdatedBy", fields: [updatedBy], references: [id])
  emailTransactions     EmailTransaction[]
  
  @@map("smtp_users")
}

// Email transaction log
model EmailTransaction {
  id                String    @id @default(uuid())
  messageId         String
  
  // Sender information
  senderIP          String
  senderHostname    String?
  mailFrom          String
  
  // Recipient information
  rcptTo            String[]
  
  // Authentication
  authenticatedUserId String?
  authMethod        String?
  
  // Message details
  subject           String?
  messageSize       Int
  
  // Processing details
  receivedAt        DateTime  @default(now())
  processedAt       DateTime?
  deliveredAt       DateTime?
  
  // Status
  status            EmailStatus
  statusMessage     String?
  
  // Routing
  routeUsed         String?
  destinationHost   String?
  destinationPort   Int?
  
  // Security checks
  spfResult         String?
  dkimResult        String?
  dmarcResult       String?
  
  // Performance metrics
  processingTime    Int       @default(0) // milliseconds
  queueTime         Int?      // milliseconds
  deliveryTime      Int?      // milliseconds
  
  // Error information
  errorCode         String?
  errorMessage      String?
  retryCount        Int       @default(0)
  
  // Metadata (stored as JSON)
  headers           Json?
  metadata          Json?
  
  // Relations
  authenticatedUser SMTPUser? @relation(fields: [authenticatedUserId], references: [id])
  queuedEmail       QueuedEmail?
  
  @@index([receivedAt])
  @@index([status])
  @@index([senderIP])
  @@index([mailFrom])
  @@map("email_transactions")
}

enum EmailStatus {
  RECEIVED
  QUEUED
  PROCESSING
  DELIVERED
  FAILED
  REJECTED
  DEFERRED
  BOUNCED
}

// SMTP Queue management
model QueuedEmail {
  id              String      @id @default(uuid())
  transactionId   String      @unique
  
  // Message data (stored as base64)
  messageData     String
  
  // Delivery details
  attempts        Int         @default(0)
  maxAttempts     Int         @default(3)
  nextAttempt     DateTime
  
  // Status
  status          QueueStatus @default(PENDING)
  lastError       String?
  
  // Timestamps
  queuedAt        DateTime    @default(now())
  lastAttemptAt   DateTime?
  
  // Priority
  priority        Int         @default(0)
  
  // Relations
  transaction     EmailTransaction @relation(fields: [transactionId], references: [id], onDelete: Cascade)
  
  @@index([status])
  @@index([nextAttempt])
  @@index([priority])
  @@map("queued_emails")
}

enum QueueStatus {
  PENDING
  PROCESSING
  DELIVERED
  FAILED
  DEAD_LETTER
}

// TLS Certificate management
model TLSCertificate {
  id                      String            @id @default(uuid())
  name                    String
  description             String?

  // Certificate details
  commonName              String
  subjectAlternativeNames String[]          @default([])
  issuer                  String
  serialNumber            String

  // Validity
  validFrom               DateTime
  validTo                 DateTime

  // Certificate data
  certificatePath         String
  privateKeyPath          String
  chainPath               String?

  // Let's Encrypt integration
  isLetsEncrypt           Boolean           @default(false)
  autoRenew               Boolean           @default(false)
  renewalDays             Int               @default(30)

  // Usage
  domains                 String[]          @default([])
  isDefault               Boolean           @default(false)

  // Status
  status                  CertificateStatus @default(PENDING_VALIDATION)
  lastValidation          DateTime?
  validationError         String?

  // Status
  enabled                 Boolean           @default(true)

  // Timestamps
  createdAt               DateTime          @default(now())
  updatedAt               DateTime          @updatedAt
  createdBy               String
  updatedBy               String

  // Relations
  creator                 User              @relation("CertificateCreatedBy", fields: [createdBy], references: [id])
  updater                 User              @relation("CertificateUpdatedBy", fields: [updatedBy], references: [id])

  @@map("tls_certificates")
}

enum CertificateStatus {
  VALID
  EXPIRED
  EXPIRING_SOON
  INVALID
  REVOKED
  PENDING_VALIDATION
}

// System configuration
model SystemConfig {
  id                    String    @id @default(uuid())
  name                  String
  description           String?

  // General settings
  instanceName          String
  instanceDescription   String?
  timezone              String    @default("UTC")

  // Logging configuration
  logLevel              String    @default("info")
  logRetentionDays      Int       @default(30)
  logMaxSize            Int       @default(100) // MB

  // Security settings
  sessionTimeout        Int       @default(60) // minutes
  mfaRequired           Boolean   @default(false)

  // Rate limiting
  apiRateLimit          Int       @default(1000) // requests per minute
  loginRateLimit        Int       @default(5) // attempts per minute

  // Backup settings
  backupEnabled         Boolean   @default(false)
  backupSchedule        String?   // cron expression
  backupRetentionDays   Int       @default(7)

  // Monitoring settings
  metricsEnabled        Boolean   @default(true)
  metricsRetentionDays  Int       @default(30)
  alertingEnabled       Boolean   @default(true)

  // License settings
  licenseKey            String?
  licenseValidUntil     DateTime?
  maxDomains            Int?
  maxThroughput         Int?      // emails per hour

  // Status
  enabled               Boolean   @default(true)

  // Timestamps
  createdAt             DateTime  @default(now())
  updatedAt             DateTime  @updatedAt
  createdBy             String
  updatedBy             String

  // Relations
  creator               User      @relation("SystemConfigCreatedBy", fields: [createdBy], references: [id])
  updater               User      @relation("SystemConfigUpdatedBy", fields: [updatedBy], references: [id])

  @@map("system_configs")
}

// Audit logging
model AuditLog {
  id          String    @id @default(uuid())
  action      String
  resource    String
  resourceId  String?
  userId      String
  userEmail   String
  timestamp   DateTime  @default(now())
  ipAddress   String
  userAgent   String?

  // Changes (stored as JSON)
  changes     Json?
  metadata    Json?

  // Relations
  user        User      @relation(fields: [userId], references: [id])

  @@index([timestamp])
  @@index([action])
  @@index([resource])
  @@index([userId])
  @@map("audit_logs")
}

// Security events
model SecurityEvent {
  id              String            @id @default(uuid())
  type            String
  severity        SecuritySeverity

  // Event details
  timestamp       DateTime          @default(now())
  source          String
  description     String

  // Context
  userId          String?
  sessionId       String?
  ipAddress       String?
  userAgent       String?

  // Additional data (stored as JSON)
  metadata        Json?

  // Response
  blocked         Boolean           @default(false)
  action          String?

  // Investigation
  investigated    Boolean           @default(false)
  investigatedBy  String?
  investigatedAt  DateTime?
  resolution      String?

  // Relations
  user            User?             @relation(fields: [userId], references: [id])
  investigator    User?             @relation("SecurityEventInvestigatedBy", fields: [investigatedBy], references: [id])

  @@index([timestamp])
  @@index([type])
  @@index([severity])
  @@map("security_events")
}

enum SecuritySeverity {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

// Monitoring alerts
model Alert {
  id                  String        @id @default(uuid())
  name                String
  description         String
  severity            AlertSeverity

  // Trigger conditions
  metric              String
  operator            String
  threshold           Float
  duration            Int           // seconds

  // Status
  status              AlertStatus   @default(ACTIVE)
  triggeredAt         DateTime?
  resolvedAt          DateTime?
  acknowledgedAt      DateTime?
  acknowledgedBy      String?

  // Notification
  notificationSent    Boolean       @default(false)
  lastNotificationAt  DateTime?

  // Metadata (stored as JSON)
  metadata            Json?
  tags                String[]      @default([])

  // Timestamps
  createdAt           DateTime      @default(now())
  updatedAt           DateTime      @updatedAt

  // Relations
  acknowledger        User?         @relation("AlertAcknowledgedBy", fields: [acknowledgedBy], references: [id])

  @@index([status])
  @@index([severity])
  @@index([triggeredAt])
  @@map("alerts")
}

enum AlertSeverity {
  INFO
  WARNING
  CRITICAL
  EMERGENCY
}

enum AlertStatus {
  ACTIVE
  RESOLVED
  ACKNOWLEDGED
  SUPPRESSED
}

// Dashboard configuration
model Dashboard {
  id              String    @id @default(uuid())
  name            String
  description     String?

  // Layout (stored as JSON)
  layout          Json

  // Widgets (stored as JSON array)
  widgets         Json

  // Permissions
  isPublic        Boolean   @default(false)
  allowedUsers    String[]  @default([])
  allowedRoles    String[]  @default([])

  // Settings
  autoRefresh     Boolean   @default(true)
  refreshInterval Int       @default(30) // seconds

  // Timestamps
  createdAt       DateTime  @default(now())
  updatedAt       DateTime  @updatedAt
  createdBy       String

  // Relations
  creator         User      @relation("DashboardCreatedBy", fields: [createdBy], references: [id])

  @@map("dashboards")
}


