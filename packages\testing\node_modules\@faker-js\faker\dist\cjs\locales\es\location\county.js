"use strict";var i=Object.defineProperty;var n=Object.getOwnPropertyDescriptor;var d=Object.getOwnPropertyNames;var u=Object.prototype.hasOwnProperty;var c=(e,a)=>{for(var l in a)i(e,l,{get:a[l],enumerable:!0})},s=(e,a,l,o)=>{if(a&&typeof a=="object"||typeof a=="function")for(let r of d(a))!u.call(e,r)&&r!==l&&i(e,r,{get:()=>a[r],enumerable:!(o=n(a,r))||o.enumerable});return e};var t=e=>s(i({},"__esModule",{value:!0}),e);var v={};c(v,{default:()=>C});module.exports=t(v);var C=["\xC1lava","Albacete","Alicante","Almer\xEDa","Asturias","\xC1vila","Badajoz","Barcelona","Burgos","Cantabria","Castell\xF3n","Ciudad Real","Cuenca","C\xE1ceres","C\xE1diz","C\xF3rdoba","Gerona","Granada","Guadalajara","Guip\xFAzcoa","Huelva","Huesca","Islas Baleares","Ja\xE9n","La Coru\xF1a","La Rioja","Las Palmas","Le\xF3n","Lugo","l\xE9rida","Madrid","Murcia","M\xE1laga","Navarra","Orense","Palencia","Pontevedra","Salamanca","Santa Cruz de Tenerife","Segovia","Sevilla","Soria","Tarragona","Teruel","Toledo","Valencia","Valladolid","Vizcaya","Zamora","Zaragoza"];
