import React from 'react';
import { clsx } from 'clsx';

interface StatusBadgeProps {
  status: 'success' | 'warning' | 'error' | 'info' | 'gray';
  text: string;
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const statusClasses = {
  success: 'badge-success',
  warning: 'badge-warning',
  error: 'badge-error',
  info: 'badge-info',
  gray: 'badge-gray',
};

const sizeClasses = {
  sm: 'text-xs px-2 py-0.5',
  md: 'text-sm px-2.5 py-0.5',
  lg: 'text-base px-3 py-1',
};

export default function StatusBadge({
  status,
  text,
  size = 'md',
  className,
}: StatusBadgeProps) {
  return (
    <span
      className={clsx(
        'badge',
        statusClasses[status],
        sizeClasses[size],
        className
      )}
    >
      {text}
    </span>
  );
}
