"use strict";var r=Object.defineProperty;var o=Object.getOwnPropertyDescriptor;var l=Object.getOwnPropertyNames;var t=Object.prototype.hasOwnProperty;var c=(a,i)=>{for(var n in i)r(a,n,{get:i[n],enumerable:!0})},d=(a,i,n,s)=>{if(i&&typeof i=="object"||typeof i=="function")for(let e of l(i))!t.call(a,e)&&e!==n&&r(a,e,{get:()=>i[e],enumerable:!(s=o(i,e))||s.enumerable});return a};var u=a=>d(r({},"__esModule",{value:!0}),a);var h={};c(h,{default:()=>C});module.exports=u(h);var C=["Anenii Noi","Basarabeas<PERSON>","<PERSON><PERSON>","<PERSON><PERSON><PERSON>","<PERSON><PERSON><PERSON>","Cal<PERSON><PERSON>","Cantemir","Cause<PERSON>","Chisinau","Cimislia","Criuleni","Donduseni","Drochia","Dubasari","Edinet","Falesti","Floresti","Glodeni","Hincesti","Ialoveni","Leova","Nisporeni","Ocnita","Orhei","Rezina","Riscani","Singerei","Soldanesti","Soroca","Stefan-Voda","Straseni","Taraclia","Telenesti","Ungheni"];
