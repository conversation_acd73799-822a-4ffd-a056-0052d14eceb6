{"name": "ironrelay", "version": "1.0.0", "description": "Commercial-grade SMTP Relay Server with centralized web-based management", "main": "dist/index.js", "scripts": {"build": "npm run build:api && npm run build:frontend", "build:api": "cd packages/api && npm run build", "build:frontend": "cd packages/frontend && npm run build", "build:smtp": "cd packages/smtp-engine && npm run build", "dev": "concurrently \"npm run dev:api\" \"npm run dev:frontend\" \"npm run dev:smtp\"", "dev:api": "cd packages/api && npm run dev", "dev:frontend": "cd packages/frontend && npm run dev", "dev:smtp": "cd packages/smtp-engine && npm run dev", "test": "npm run test:api && npm run test:smtp", "test:api": "cd packages/api && npm run test", "test:smtp": "cd packages/smtp-engine && npm run test", "lint": "eslint packages/*/src --ext .ts,.tsx", "lint:fix": "eslint packages/*/src --ext .ts,.tsx --fix", "docker:build": "docker-compose build", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "migrate": "cd packages/api && npm run migrate", "seed": "cd packages/api && npm run seed"}, "workspaces": ["packages/*"], "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0", "prettier": "^3.0.0", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}, "repository": {"type": "git", "url": "https://github.com/ironrelay/ironrelay.git"}, "keywords": ["smtp", "relay", "email", "server", "haraka", "enterprise", "commercial"], "author": "IronRelay Team", "license": "Commercial", "private": true}