import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { PrismaClient } from '@prisma/client';
import { Logger } from '../common/logger/logger.service';

@Injectable()
export class PrismaService extends PrismaClient implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);

  constructor(private configService: ConfigService) {
    super({
      datasources: {
        db: {
          url: configService.get<string>('database.url'),
        },
      },
      log: [
        {
          emit: 'event',
          level: 'query',
        },
        {
          emit: 'event',
          level: 'error',
        },
        {
          emit: 'event',
          level: 'info',
        },
        {
          emit: 'event',
          level: 'warn',
        },
      ],
    });

    // Set up Prisma event listeners
    this.setupEventListeners();
  }

  async onModuleInit(): Promise<void> {
    try {
      await this.$connect();
      this.logger.info('Database connected successfully');
    } catch (error) {
      this.logger.error('Failed to connect to database', error);
      throw error;
    }
  }

  async onModuleDestroy(): Promise<void> {
    try {
      await this.$disconnect();
      this.logger.info('Database disconnected successfully');
    } catch (error) {
      this.logger.error('Failed to disconnect from database', error);
    }
  }

  private setupEventListeners(): void {
    this.$on('query', (e) => {
      if (this.configService.get('nodeEnv') === 'development') {
        this.logger.debug('Database Query', {
          query: e.query,
          params: e.params,
          duration: `${e.duration}ms`,
          target: e.target,
        });
      }
    });

    this.$on('error', (e) => {
      this.logger.error('Database Error', {
        message: e.message,
        target: e.target,
      });
    });

    this.$on('info', (e) => {
      this.logger.info('Database Info', {
        message: e.message,
        target: e.target,
      });
    });

    this.$on('warn', (e) => {
      this.logger.warn('Database Warning', {
        message: e.message,
        target: e.target,
      });
    });
  }

  async healthCheck(): Promise<boolean> {
    try {
      await this.$queryRaw`SELECT 1`;
      return true;
    } catch (error) {
      this.logger.error('Database health check failed', error);
      return false;
    }
  }

  async getConnectionInfo(): Promise<{
    connected: boolean;
    version?: string;
    connectionCount?: number;
  }> {
    try {
      const result = await this.$queryRaw<Array<{ version: string }>>`SELECT version()`;
      const version = result[0]?.version;

      // Get connection count (PostgreSQL specific)
      const connectionResult = await this.$queryRaw<Array<{ count: bigint }>>`
        SELECT count(*) as count FROM pg_stat_activity WHERE state = 'active'
      `;
      const connectionCount = Number(connectionResult[0]?.count || 0);

      return {
        connected: true,
        version,
        connectionCount,
      };
    } catch (error) {
      this.logger.error('Failed to get database connection info', error);
      return {
        connected: false,
      };
    }
  }

  async getTableStats(): Promise<Record<string, { count: number; size?: string }>> {
    try {
      // Get row counts for main tables
      const tables = [
        'users',
        'smtp_configs',
        'domain_routes',
        'smtp_users',
        'email_transactions',
        'queued_emails',
        'tls_certificates',
        'system_configs',
        'audit_logs',
        'security_events',
        'alerts',
        'dashboards',
      ];

      const stats: Record<string, { count: number; size?: string }> = {};

      for (const table of tables) {
        try {
          const countResult = await this.$queryRawUnsafe(`SELECT COUNT(*) as count FROM ${table}`);
          const count = Number((countResult as any)[0]?.count || 0);

          // Get table size (PostgreSQL specific)
          const sizeResult = await this.$queryRawUnsafe(`
            SELECT pg_size_pretty(pg_total_relation_size('${table}')) as size
          `);
          const size = (sizeResult as any)[0]?.size;

          stats[table] = { count, size };
        } catch (error) {
          this.logger.warn(`Failed to get stats for table ${table}`, error);
          stats[table] = { count: 0 };
        }
      }

      return stats;
    } catch (error) {
      this.logger.error('Failed to get table stats', error);
      return {};
    }
  }

  async cleanupOldRecords(days: number = 30): Promise<{
    auditLogs: number;
    securityEvents: number;
    emailTransactions: number;
  }> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - days);

      // Clean up old audit logs
      const auditLogsDeleted = await this.auditLog.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      // Clean up old security events
      const securityEventsDeleted = await this.securityEvent.deleteMany({
        where: {
          timestamp: {
            lt: cutoffDate,
          },
        },
      });

      // Clean up old email transactions (keep for longer - 90 days)
      const emailCutoffDate = new Date();
      emailCutoffDate.setDate(emailCutoffDate.getDate() - 90);

      const emailTransactionsDeleted = await this.emailTransaction.deleteMany({
        where: {
          receivedAt: {
            lt: emailCutoffDate,
          },
        },
      });

      this.logger.info('Database cleanup completed', {
        auditLogs: auditLogsDeleted.count,
        securityEvents: securityEventsDeleted.count,
        emailTransactions: emailTransactionsDeleted.count,
        cutoffDate,
      });

      return {
        auditLogs: auditLogsDeleted.count,
        securityEvents: securityEventsDeleted.count,
        emailTransactions: emailTransactionsDeleted.count,
      };
    } catch (error) {
      this.logger.error('Database cleanup failed', error);
      throw error;
    }
  }

  async vacuum(): Promise<void> {
    try {
      // PostgreSQL VACUUM to reclaim space
      await this.$executeRawUnsafe('VACUUM ANALYZE');
      this.logger.info('Database vacuum completed');
    } catch (error) {
      this.logger.error('Database vacuum failed', error);
      throw error;
    }
  }

  async backup(filePath: string): Promise<void> {
    try {
      // This would typically use pg_dump or similar
      // For now, just log the operation
      this.logger.info(`Database backup requested to: ${filePath}`);
      
      // In a real implementation, you would:
      // 1. Use pg_dump to create a backup
      // 2. Compress the backup
      // 3. Store it in the specified location
      // 4. Verify the backup integrity
      
      throw new Error('Database backup not implemented yet');
    } catch (error) {
      this.logger.error('Database backup failed', error);
      throw error;
    }
  }

  async restore(filePath: string): Promise<void> {
    try {
      // This would typically use pg_restore or similar
      this.logger.info(`Database restore requested from: ${filePath}`);
      
      // In a real implementation, you would:
      // 1. Verify the backup file
      // 2. Stop all connections
      // 3. Drop and recreate the database
      // 4. Restore from the backup
      // 5. Verify the restoration
      
      throw new Error('Database restore not implemented yet');
    } catch (error) {
      this.logger.error('Database restore failed', error);
      throw error;
    }
  }

  // Transaction helper methods
  async withTransaction<T>(fn: (tx: PrismaService) => Promise<T>): Promise<T> {
    return this.$transaction(async (tx) => {
      return fn(tx as PrismaService);
    });
  }

  // Pagination helper
  async paginate<T>(
    model: any,
    args: any,
    page: number = 1,
    limit: number = 20,
  ): Promise<{
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      totalPages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  }> {
    const skip = (page - 1) * limit;
    const take = limit;

    const [data, total] = await Promise.all([
      model.findMany({
        ...args,
        skip,
        take,
      }),
      model.count({
        where: args.where,
      }),
    ]);

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    };
  }
}
