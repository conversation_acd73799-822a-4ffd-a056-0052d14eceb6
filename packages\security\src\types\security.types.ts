export interface SecurityConfig {
  licenseKey?: string;
  licenseServerUrl?: string;
  enableIntegrityChecks: boolean;
  enableAntiTamper: boolean;
  enableObfuscation: boolean;
  allowedEnvironments: string[];
  maxInstances: number;
  heartbeatInterval: number;
  encryptionKey?: string;
  debugMode: boolean;
}

export interface SecurityContext {
  instanceId: string;
  startTime: Date;
  environment: string;
  nodeVersion: string;
  platform: string;
  architecture: string;
  processId: number;
  parentProcessId?: number;
  workingDirectory: string;
  executablePath: string;
  commandLineArgs: string[];
}

export interface SecurityEvent {
  id: string;
  type: SecurityEventType;
  severity: SecuritySeverity;
  timestamp: Date;
  source: string;
  description: string;
  metadata?: Record<string, any>;
  context: SecurityContext;
}

export enum SecurityEventType {
  LICENSE_VALIDATION = 'LICENSE_VALIDATION',
  LICENSE_EXPIRED = 'LICENSE_EXPIRED',
  LICENSE_INVALID = 'LICENSE_INVALID',
  INTEGRITY_VIOLATION = 'INTEGRITY_VIOLATION',
  TAMPERING_DETECTED = 'TAMPERING_DETECTED',
  UNAUTHORIZED_ACCESS = 'UNAUTHORIZED_ACCESS',
  ENVIRONMENT_VIOLATION = 'ENVIRONMENT_VIOLATION',
  INSTANCE_LIMIT_EXCEEDED = 'INSTANCE_LIMIT_EXCEEDED',
  HEARTBEAT_FAILURE = 'HEARTBEAT_FAILURE',
  OBFUSCATION_BYPASS = 'OBFUSCATION_BYPASS',
  DEBUG_MODE_DETECTED = 'DEBUG_MODE_DETECTED',
  SUSPICIOUS_ACTIVITY = 'SUSPICIOUS_ACTIVITY',
}

export enum SecuritySeverity {
  LOW = 'LOW',
  MEDIUM = 'MEDIUM',
  HIGH = 'HIGH',
  CRITICAL = 'CRITICAL',
}

export interface SecurityMetrics {
  totalEvents: number;
  eventsByType: Record<SecurityEventType, number>;
  eventsBySeverity: Record<SecuritySeverity, number>;
  lastEventTime?: Date;
  uptime: number;
  integrityChecks: number;
  licenseValidations: number;
  tamperingAttempts: number;
}

export interface SecurityAlert {
  id: string;
  type: SecurityEventType;
  severity: SecuritySeverity;
  message: string;
  timestamp: Date;
  acknowledged: boolean;
  acknowledgedBy?: string;
  acknowledgedAt?: Date;
  resolved: boolean;
  resolvedBy?: string;
  resolvedAt?: Date;
  metadata?: Record<string, any>;
}

export interface SecurityPolicy {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  rules: SecurityRule[];
  actions: SecurityAction[];
  createdAt: Date;
  updatedAt: Date;
}

export interface SecurityRule {
  id: string;
  type: SecurityRuleType;
  condition: string;
  parameters: Record<string, any>;
  enabled: boolean;
}

export enum SecurityRuleType {
  LICENSE_CHECK = 'LICENSE_CHECK',
  INTEGRITY_CHECK = 'INTEGRITY_CHECK',
  ENVIRONMENT_CHECK = 'ENVIRONMENT_CHECK',
  INSTANCE_LIMIT = 'INSTANCE_LIMIT',
  HEARTBEAT_CHECK = 'HEARTBEAT_CHECK',
  CUSTOM = 'CUSTOM',
}

export interface SecurityAction {
  id: string;
  type: SecurityActionType;
  parameters: Record<string, any>;
  enabled: boolean;
}

export enum SecurityActionType {
  LOG_EVENT = 'LOG_EVENT',
  SEND_ALERT = 'SEND_ALERT',
  BLOCK_ACCESS = 'BLOCK_ACCESS',
  SHUTDOWN_APPLICATION = 'SHUTDOWN_APPLICATION',
  NOTIFY_ADMIN = 'NOTIFY_ADMIN',
  QUARANTINE = 'QUARANTINE',
  CUSTOM = 'CUSTOM',
}

export interface SecurityReport {
  id: string;
  type: SecurityReportType;
  title: string;
  description: string;
  generatedAt: Date;
  period: {
    start: Date;
    end: Date;
  };
  metrics: SecurityMetrics;
  events: SecurityEvent[];
  alerts: SecurityAlert[];
  recommendations: SecurityRecommendation[];
}

export enum SecurityReportType {
  DAILY = 'DAILY',
  WEEKLY = 'WEEKLY',
  MONTHLY = 'MONTHLY',
  INCIDENT = 'INCIDENT',
  COMPLIANCE = 'COMPLIANCE',
  CUSTOM = 'CUSTOM',
}

export interface SecurityRecommendation {
  id: string;
  type: SecurityRecommendationType;
  severity: SecuritySeverity;
  title: string;
  description: string;
  action: string;
  priority: number;
  implemented: boolean;
  implementedAt?: Date;
}

export enum SecurityRecommendationType {
  LICENSE_RENEWAL = 'LICENSE_RENEWAL',
  SECURITY_UPDATE = 'SECURITY_UPDATE',
  CONFIGURATION_CHANGE = 'CONFIGURATION_CHANGE',
  POLICY_UPDATE = 'POLICY_UPDATE',
  MONITORING_IMPROVEMENT = 'MONITORING_IMPROVEMENT',
  ACCESS_CONTROL = 'ACCESS_CONTROL',
  ENCRYPTION_UPGRADE = 'ENCRYPTION_UPGRADE',
  CUSTOM = 'CUSTOM',
}
